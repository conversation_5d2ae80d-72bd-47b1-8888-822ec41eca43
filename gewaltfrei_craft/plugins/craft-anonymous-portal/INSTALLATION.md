# Installation & Setup Guide

## 1. Plugin Installation

### Schritt 1: Plugin kopieren
```bash
# Plugin in das plugins Verzeichnis kopieren
cp -r craft-anonymous-portal /path/to/craft/plugins/
```

### Schritt 2: Plugin installieren
1. Craft Control Panel öffnen
2. Zu "Settings" → "Plugins" navigieren
3. "Anonymous Portal" Plugin installieren

### Schritt 3: Datenbank-Migration
```bash
# Migrationen ausführen
php craft migrate/all
```

## 2. Matrix Setup

### Matrix-Server vorbereiten
1. Matrix-Homeserver installieren (z.B. Synapse)
2. Bot-User erstellen:
```bash
# Beispiel für Synapse
register_new_matrix_user -c homeserver.yaml http://localhost:8008
```

### Access Token generieren
```bash
curl -XPOST -d '{"type":"m.login.password", "user":"@bot:example.com", "password":"botpassword"}' "https://matrix.example.com/_matrix/client/r0/login"
```

### Plugin konfigurieren
- **Matrix Homeserver URL**: `https://matrix.example.com`
- **Matrix Access Token**: Token aus dem Login-Response

## 3. Postmoogle Setup

### Postmoogle installieren
```bash
git clone https://github.com/etkecc/postmoogle
cd postmoogle
# Installation nach Postmoogle-Dokumentation
```

### API-Konfiguration
1. Postmoogle API-Endpunkt konfigurieren
2. API-Key generieren
3. Webhook-URLs einrichten:
   - Matrix → Postmoogle: `/webhooks/anonymous-portal/matrix`
   - Postmoogle → Craft: `/webhooks/anonymous-portal/postmoogle`

### Plugin konfigurieren
- **Postmoogle API URL**: `https://postmoogle.example.com/api`
- **Postmoogle API Key**: Generierter API-Key

## 4. E-Mail Konfiguration

### SMTP Setup
```php
// config/app.php
return [
    'components' => [
        'mailer' => [
            'class' => craft\mail\Mailer::class,
            'transport' => [
                'class' => craft\mail\transportadapters\Smtp::class,
                'host' => 'smtp.example.com',
                'port' => 587,
                'username' => '<EMAIL>',
                'password' => 'password',
                'encryption' => 'tls',
            ],
        ],
    ],
];
```

### Plugin konfigurieren
- **Consultant Email**: `<EMAIL>`
- **From Email**: `<EMAIL>`

## 5. Sicherheitseinstellungen

### Empfohlene Werte
- **Session Timeout**: 30 Minuten
- **Rate Limit Attempts**: 5
- **Rate Limit Window**: 10 Minuten
- **Cleanup Days**: 90

### HTTPS erzwingen
```php
// config/general.php
return [
    '*' => [
        'useSecureCookies' => true,
        'requireMatchingUserAgentForSession' => true,
    ],
];
```

## 6. Cron-Jobs einrichten

### Automatische Bereinigung
```bash
# Crontab bearbeiten
crontab -e

# Täglich um 2:00 Uhr bereinigen
0 2 * * * /usr/bin/php /path/to/craft anonymous-portal/cleanup/threads --force
```

### Überwachung
```bash
# Wöchentliche Statistiken
0 9 * * 1 /usr/bin/php /path/to/craft anonymous-portal/cleanup/stats
```

## 7. Webhook-Konfiguration

### Matrix Webhooks
Matrix-Server konfigurieren, um Events an Craft zu senden:
```yaml
# homeserver.yaml
app_service_config_files:
  - "/path/to/craft-bridge.yaml"
```

### Postmoogle Webhooks
Postmoogle konfigurieren:
```yaml
# postmoogle.yaml
webhooks:
  incoming_mail: "https://yoursite.com/webhooks/anonymous-portal/postmoogle"
  matrix_message: "https://yoursite.com/webhooks/anonymous-portal/matrix"
```

## 8. Testing

### Verbindungen testen
```bash
# Matrix testen
curl -X GET https://yoursite.com/webhooks/anonymous-portal/test/matrix

# Postmoogle testen
curl -X GET https://yoursite.com/webhooks/anonymous-portal/test/postmoogle

# Health Check
curl -X POST https://yoursite.com/webhooks/anonymous-portal/health
```

### Funktionstest
1. Neue Beratung über `/anfrage` erstellen
2. ID und Secret notieren
3. Mit Credentials einloggen
4. Nachricht senden
5. E-Mail-Benachrichtigung prüfen
6. Per E-Mail antworten
7. Antwort im Portal prüfen

## 9. Monitoring

### Log-Dateien überwachen
```bash
# Craft Logs
tail -f storage/logs/web.log

# Plugin-spezifische Events
grep "Anonymous Portal" storage/logs/web.log
```

### Dashboard Widget
Das Plugin stellt ein Dashboard-Widget zur Verfügung:
- Konfigurationsstatus
- Verbindungsstatus
- Statistiken
- Quick Actions

## 10. Troubleshooting

### Häufige Probleme

**Plugin wird nicht angezeigt**
```bash
# Composer autoload aktualisieren
composer dump-autoload
php craft clear-caches/all
```

**Matrix-Verbindung fehlgeschlagen**
- Access Token prüfen
- Homeserver erreichbar?
- Bot-User korrekt konfiguriert?

**E-Mails kommen nicht an**
- SMTP-Konfiguration prüfen
- Postmoogle-Logs kontrollieren
- Webhook-URLs testen

**Rate Limiting zu aggressiv**
```bash
# Cache leeren
php craft clear-caches/all

# Einstellungen in Plugin-Konfiguration anpassen
```

### Debug-Modus
```php
// config/general.php
return [
    'dev' => [
        'devMode' => true,
        'enableTemplateCaching' => false,
    ],
];
```

## 11. Produktions-Deployment

### Sicherheits-Checklist
- [ ] HTTPS erzwungen
- [ ] Sichere Session-Konfiguration
- [ ] Rate Limiting aktiviert
- [ ] Logs rotieren
- [ ] Backup-Strategie
- [ ] Monitoring eingerichtet

### Performance-Optimierung
```php
// config/general.php
return [
    'production' => [
        'enableTemplateCaching' => true,
        'cacheElementQueries' => true,
        'maxCachedCloudImageSize' => 2000,
    ],
];
```

### Backup-Strategie
```bash
# Datenbank-Backup
php craft backup/db

# Vollständiges Backup
tar -czf backup-$(date +%Y%m%d).tar.gz \
    --exclude=storage/logs \
    --exclude=web/cpresources \
    /path/to/craft
```

## Support

Bei Problemen:
1. Logs prüfen
2. Verbindungstests ausführen
3. Plugin-Dokumentation konsultieren
4. GitHub Issues erstellen
