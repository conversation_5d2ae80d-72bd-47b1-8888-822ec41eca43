{"name": "kldev/craft-anonymous-portal", "description": "Anonymous consultation portal with Matrix integration for secure visitor communication", "type": "craft-plugin", "require": {"php": ">=8.0.2", "craftcms/cms": "^4.5.0"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "autoload": {"psr-4": {"kldev\\anonymousportal\\": "src/"}}, "extra": {"handle": "_craft-anonymous-portal", "name": "Anonymous Portal", "developer": "kldev", "documentationUrl": ""}, "scripts": {"check-cs": "ecs check --ansi", "fix-cs": "ecs check --ansi --fix", "phpstan": "phpstan --memory-limit=1G"}, "config": {"sort-packages": true, "platform": {"php": "8.0.2"}, "allow-plugins": {"yiisoft/yii2-composer": true, "craftcms/plugin-installer": true, "php-http/discovery": true}}}