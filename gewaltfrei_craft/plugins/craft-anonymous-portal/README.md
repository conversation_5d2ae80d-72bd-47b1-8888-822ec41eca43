# Anonymous Portal Plugin for Craft CMS

Ein Plugin für anonyme Beratungsportale, das auf Matrix + Postmoogle aufsetzt und vollständig im Frontend funktioniert.

## Features

- **Vollständig anonyme Beratung** - Keine Craft-Benutzer oder Rollen erforderlich
- **Matrix-Integration** - Automatische Erstellung von Matrix-Räumen für jeden Thread
- **Postmoogle-Bridge** - E-Mail-Integration über Postmoogle
- **Sichere Authentifizierung** - Argon2ID-Hashing mit Rate-Limiting
- **HTMX-basierte UI** - Moderne, reaktive Benutzeroberfläche
- **Automatische Bereinigung** - Threads werden nach konfigurierbarer Zeit gelöscht
- **Panik-Button** - ESC-Taste schließt sofort die Seite
- **PDF-Export** - Threads können als PDF heruntergeladen werden

## Installation

1. Plugin in das `plugins/` Verzeichnis kopieren
2. Plugin über das Craft Control Panel installieren
3. Konfiguration in den Plugin-Einstellungen vornehmen

## Konfiguration

### Matrix Setup
- **Homeserver URL**: URL Ihres Matrix-Homeservers
- **Access Token**: Bot-User Access Token

### Postmoogle Setup  
- **API URL**: URL der Postmoogle-API
- **API Key**: Authentifizierungs-Schlüssel

### E-Mail Konfiguration
- **Berater E-Mail**: Adresse für Benachrichtigungen
- **Absender E-Mail**: Absender für ausgehende E-Mails

### Sicherheitseinstellungen
- **Session Timeout**: Sitzungsdauer in Minuten
- **Rate Limiting**: Schutz vor Brute-Force-Angriffen
- **Bereinigung**: Automatische Löschung nach X Tagen

## Verwendung

### Neue Beratung starten
1. Besucher geht auf `/anfrage`
2. Füllt das Formular aus und sendet es ab
3. Erhält ID und Secret (nur einmal angezeigt!)
4. Berater erhalten E-Mail-Benachrichtigung

### Auf Beratung zugreifen
1. Besucher gibt ID und Secret ein
2. Kann Nachrichten lesen und antworten
3. Berater antworten per E-Mail oder Matrix

### Berater-Workflow
1. Neue Anfrage kommt per E-Mail
2. Antwort per E-Mail an die generierte Mailbox
3. Nachricht erscheint automatisch im Portal

## Routen

- `/anfrage` - Login/Neue Beratung
- `/anfrage/send` - Neue Beratung erstellen
- `/anfrage/login` - Anmeldung
- `/anfrage/{id}` - Thread-Ansicht
- `/anfrage/{id}/reply` - Antwort senden
- `/anfrage/{id}/messages` - Nachrichten abrufen (HTMX)
- `/anfrage/{id}/download` - PDF-Export

### Webhooks
- `/webhooks/anonymous-portal/matrix` - Matrix-Nachrichten
- `/webhooks/anonymous-portal/postmoogle` - E-Mail-Nachrichten
- `/webhooks/anonymous-portal/health` - Health Check

## Console Commands

### Bereinigung
```bash
# Alte Threads löschen (Standard: 90 Tage)
php craft anonymous-portal/cleanup/threads

# Mit benutzerdefinierten Tagen
php craft anonymous-portal/cleanup/threads --days=30

# Dry-run (nur anzeigen, nicht löschen)
php craft anonymous-portal/cleanup/threads --dry-run

# Ohne Bestätigung
php craft anonymous-portal/cleanup/threads --force
```

### Statistiken
```bash
# Übersicht über Threads und Nachrichten
php craft anonymous-portal/cleanup/stats
```

### Verbindungstests
```bash
# Matrix und Postmoogle testen
php craft anonymous-portal/cleanup/test-connections
```

## Sicherheitsfeatures

### Authentifizierung
- 8-stellige Hex-ID (4 Milliarden Kombinationen)
- 22-Zeichen Base64-Secret (~128 Bit Entropie)
- Argon2ID-Hashing mit konfigurierbaren Parametern

### Rate Limiting
- IP-basierte Begrenzung (Standard: 10 Versuche/10 Min)
- ID-basierte Begrenzung (Standard: 5 Versuche)
- Automatische Sperrung bei Überschreitung

### Session Management
- Sichere PHP-Sessions (kein Cookie-Storage)
- Konfigurierbare Timeouts
- Thread-spezifische Validierung

### Logging
- Sicherheitsereignisse werden protokolliert
- IP-Adressen und User-Agents erfasst
- Keine sensiblen Daten in Logs

## Datenmodell

### Anfrage (Request)
- `publicId` - 8-stellige Hex-ID
- `secretHash` - Argon2ID-Hash des Secrets
- `roomId` - Matrix-Raum-ID
- `mailbox` - Postmoogle-Mailbox
- `encryptedEmail` - Optional verschlüsselte Besucher-E-Mail

### Antwort (Reply)
- `anfrageId` - Referenz zur Anfrage
- `sender` - 'visitor' oder 'consultant'
- `body` - Nachrichtentext
- `ts` - Zeitstempel

## Cron-Job Setup

Für automatische Bereinigung:

```bash
# Täglich um 2:00 Uhr
0 2 * * * /path/to/php /path/to/craft anonymous-portal/cleanup/threads --force
```

## Entwicklung

### Struktur
```
src/
├── console/controllers/     # Console Commands
├── controllers/            # Web Controllers
├── elements/              # Anfrage & Antwort Elements
├── migrations/            # Datenbank-Migrationen
├── models/               # Settings Model
├── records/              # ActiveRecord Classes
├── services/             # Business Logic
├── templates/            # Twig Templates
└── translations/         # Übersetzungen
```

### Testing
```bash
# Verbindungen testen
curl -X POST http://localhost/webhooks/anonymous-portal/health

# Matrix testen
curl -X GET http://localhost/webhooks/anonymous-portal/test/matrix

# Postmoogle testen  
curl -X GET http://localhost/webhooks/anonymous-portal/test/postmoogle
```

## Troubleshooting

### Häufige Probleme

**Matrix-Verbindung fehlgeschlagen**
- Access Token prüfen
- Homeserver-URL korrekt?
- Netzwerk-Erreichbarkeit testen

**Postmoogle-Integration funktioniert nicht**
- API-URL und Key prüfen
- Webhook-Endpunkte konfiguriert?
- Mailbox-Erstellung erfolgreich?

**Rate Limiting zu streng**
- Einstellungen in Plugin-Konfiguration anpassen
- Cache leeren: `php craft clear-caches/all`

**Sessions funktionieren nicht**
- PHP-Session-Konfiguration prüfen
- Craft-Session-Einstellungen kontrollieren

## Lizenz

Dieses Plugin wurde für das Gewaltfrei-Projekt entwickelt.

## Support

Bei Fragen oder Problemen wenden Sie sich an das Entwicklungsteam.
