<?php

namespace kldev\anonymousportal\elements;

use Craft;
use craft\base\Element;
use craft\elements\db\ElementQueryInterface;
use craft\helpers\UrlHelper;
use kldev\anonymousportal\elements\db\AnfrageQuery;
use kldev\anonymousportal\records\AnfrageRecord;

/**
 * Anfrage element type
 */
class Anfrage extends Element
{
    public ?string $publicId = null;
    public ?string $secretHash = null;
    public ?string $replyToEmail = null;
    public ?string $encryptedEmail = null;

    public static function displayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'Anfrage');
    }

    public static function lowerDisplayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'anfrage');
    }

    public static function pluralDisplayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'Anfragen');
    }

    public static function pluralLowerDisplayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'anfragen');
    }

    public static function refHandle(): ?string
    {
        return 'anfrage';
    }

    public static function hasContent(): bool
    {
        return false;
    }

    public static function hasTitles(): bool
    {
        return false;
    }

    public static function hasUris(): bool
    {
        return false;
    }

    public static function isLocalized(): bool
    {
        return false;
    }

    public static function hasStatuses(): bool
    {
        return false;
    }

    public static function find(): ElementQueryInterface
    {
        return new AnfrageQuery(static::class);
    }

    protected static function defineSources(string $context = null): array
    {
        return [
            [
                'key' => '*',
                'label' => Craft::t('_craft-anonymous-portal', 'All anfragen'),
            ],
        ];
    }

    protected static function defineActions(string $source = null): array
    {
        return [];
    }

    protected static function defineSearchableAttributes(): array
    {
        return ['publicId', 'mailbox'];
    }

    protected static function defineSortOptions(): array
    {
        return [
            'dateCreated' => Craft::t('app', 'Date Created'),
            'dateUpdated' => Craft::t('app', 'Date Updated'),
        ];
    }

    protected static function defineTableAttributes(): array
    {
        return [
            'publicId' => ['label' => Craft::t('_craft-anonymous-portal', 'Public ID')],
            'mailbox' => ['label' => Craft::t('_craft-anonymous-portal', 'Mailbox')],
            'dateCreated' => ['label' => Craft::t('app', 'Date Created')],
        ];
    }

    protected static function defineDefaultTableAttributes(string $source): array
    {
        return ['publicId', 'mailbox', 'dateCreated'];
    }

    public function getTableAttributeHtml(string $attribute): string
    {
        switch ($attribute) {
            case 'publicId':
                return $this->publicId ? '<code>' . $this->publicId . '</code>' : '';
            case 'mailbox':
                return $this->mailbox ? '<code>' . $this->mailbox . '</code>' : '';
            default:
                return parent::getTableAttributeHtml($attribute);
        }
    }

    public function getCpEditUrl(): ?string
    {
        return null; // No CP editing for security reasons
    }

    public function getUriFormat(): ?string
    {
        return null;
    }

    protected function defineRules(): array
    {
        $rules = parent::defineRules();
        $rules[] = [['publicId', 'secretHash'], 'required'];
        $rules[] = [['publicId'], 'string', 'length' => 8];
        $rules[] = [['secretHash', 'replyToEmail', 'encryptedEmail'], 'string'];
        $rules[] = [['publicId'], 'unique', 'targetClass' => AnfrageRecord::class];
        return $rules;
    }

    public function afterSave(bool $isNew): void
    {
        if ($isNew) {
            $record = new AnfrageRecord();
            $record->id = (int)$this->id;
        } else {
            $record = AnfrageRecord::findOne($this->id);
        }

        $record->publicId = $this->publicId;
        $record->secretHash = $this->secretHash;
        $record->replyToEmail = $this->replyToEmail;
        $record->encryptedEmail = $this->encryptedEmail;

        $record->save(false);

        parent::afterSave($isNew);
    }

    public function beforeDelete(): bool
    {
        if (!parent::beforeDelete()) {
            return false;
        }

        // Delete related Antwort elements
        $antworten = \kldev\anonymousportal\elements\Antwort::find()
            ->anfrageId($this->id)
            ->all();

        foreach ($antworten as $antwort) {
            Craft::$app->elements->deleteElement($antwort);
        }

        return true;
    }

    public function afterDelete(): void
    {
        $record = AnfrageRecord::findOne($this->id);
        if ($record) {
            $record->delete();
        }

        parent::afterDelete();
    }
}
