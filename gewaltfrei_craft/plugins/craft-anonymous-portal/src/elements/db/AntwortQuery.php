<?php

namespace kldev\anonymousportal\elements\db;

use craft\elements\db\ElementQuery;
use craft\helpers\Db;

/**
 * AntwortQuery represents a SELECT SQL statement for Antwort elements.
 */
class AntwortQuery extends ElementQuery
{
    public ?int $anfrageId = null;
    public ?string $sender = null;

    public function anfrageId(?int $value): static
    {
        $this->anfrageId = $value;
        return $this;
    }

    public function sender(?string $value): static
    {
        $this->sender = $value;
        return $this;
    }

    protected function beforePrepare(): bool
    {
        $this->joinElementTable('{{%anonymousportal_antworten}}');

        $this->query->select([
            '{{%anonymousportal_antworten}}.anfrageId',
            '{{%anonymousportal_antworten}}.sender',
            '{{%anonymousportal_antworten}}.body',
            '{{%anonymousportal_antworten}}.ts',
        ]);

        if ($this->anfrageId) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_antworten}}.anfrageId', $this->anfrageId));
        }

        if ($this->sender) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_antworten}}.sender', $this->sender));
        }

        return parent::beforePrepare();
    }
}
