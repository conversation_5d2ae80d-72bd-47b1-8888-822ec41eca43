<?php

namespace kldev\anonymousportal\elements\db;

use craft\elements\db\ElementQuery;
use craft\helpers\Db;

/**
 * AnfrageQuery represents a SELECT SQL statement for Anfrage elements.
 */
class AnfrageQuery extends ElementQuery
{
    public ?string $publicId = null;
    public ?string $replyToEmail = null;

    public function publicId(?string $value): static
    {
        $this->publicId = $value;
        return $this;
    }

    public function replyToEmail(?string $value): static
    {
        $this->replyToEmail = $value;
        return $this;
    }

    protected function beforePrepare(): bool
    {
        $this->joinElementTable('{{%anonymousportal_anfragen}}');

        $this->query->select([
            '{{%anonymousportal_anfragen}}.publicId',
            '{{%anonymousportal_anfragen}}.secretHash',
            '{{%anonymousportal_anfragen}}.replyToEmail',
            '{{%anonymousportal_anfragen}}.encryptedEmail',
        ]);

        if ($this->publicId) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_anfragen}}.publicId', $this->publicId));
        }

        if ($this->replyToEmail) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_anfragen}}.replyToEmail', $this->replyToEmail));
        }

        return parent::beforePrepare();
    }
}
