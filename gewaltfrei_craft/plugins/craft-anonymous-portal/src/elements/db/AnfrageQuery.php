<?php

namespace kldev\anonymousportal\elements\db;

use craft\elements\db\ElementQuery;
use craft\helpers\Db;

/**
 * AnfrageQuery represents a SELECT SQL statement for Anfrage elements.
 */
class AnfrageQuery extends ElementQuery
{
    public ?string $publicId = null;
    public ?string $roomId = null;
    public ?string $mailbox = null;

    public function publicId(?string $value): static
    {
        $this->publicId = $value;
        return $this;
    }

    public function roomId(?string $value): static
    {
        $this->roomId = $value;
        return $this;
    }

    public function mailbox(?string $value): static
    {
        $this->mailbox = $value;
        return $this;
    }

    protected function beforePrepare(): bool
    {
        $this->joinElementTable('{{%anonymousportal_anfragen}}');

        $this->query->select([
            '{{%anonymousportal_anfragen}}.publicId',
            '{{%anonymousportal_anfragen}}.secretHash',
            '{{%anonymousportal_anfragen}}.roomId',
            '{{%anonymousportal_anfragen}}.mailbox',
            '{{%anonymousportal_anfragen}}.encryptedEmail',
        ]);

        if ($this->publicId) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_anfragen}}.publicId', $this->publicId));
        }

        if ($this->roomId) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_anfragen}}.roomId', $this->roomId));
        }

        if ($this->mailbox) {
            $this->subQuery->andWhere(Db::parseParam('{{%anonymousportal_anfragen}}.mailbox', $this->mailbox));
        }

        return parent::beforePrepare();
    }
}
