<?php

namespace kldev\anonymousportal\elements;

use Craft;
use craft\base\Element;
use craft\elements\db\ElementQueryInterface;
use kldev\anonymousportal\elements\db\AntwortQuery;
use kldev\anonymousportal\records\AntwortRecord;

/**
 * Antwort element type
 */
class Antwort extends Element
{
    public ?int $anfrageId = null;
    public ?string $sender = null; // 'visitor' or 'consultant'
    public ?string $body = null;
    public ?\DateTime $ts = null;

    public static function displayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'Antwort');
    }

    public static function lowerDisplayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'antwort');
    }

    public static function pluralDisplayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'Antworten');
    }

    public static function pluralLowerDisplayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'antworten');
    }

    public static function refHandle(): ?string
    {
        return 'antwort';
    }

    public static function hasContent(): bool
    {
        return false;
    }

    public static function hasTitles(): bool
    {
        return false;
    }

    public static function hasUris(): bool
    {
        return false;
    }

    public static function isLocalized(): bool
    {
        return false;
    }

    public static function hasStatuses(): bool
    {
        return false;
    }

    public static function find(): ElementQueryInterface
    {
        return new AntwortQuery(static::class);
    }

    protected static function defineSources(string $context = null): array
    {
        return [
            [
                'key' => '*',
                'label' => Craft::t('_craft-anonymous-portal', 'All antworten'),
            ],
        ];
    }

    protected static function defineActions(string $source = null): array
    {
        return [];
    }

    protected static function defineSearchableAttributes(): array
    {
        return ['body'];
    }

    protected static function defineSortOptions(): array
    {
        return [
            'ts' => Craft::t('_craft-anonymous-portal', 'Timestamp'),
            'dateCreated' => Craft::t('app', 'Date Created'),
        ];
    }

    protected static function defineTableAttributes(): array
    {
        return [
            'anfrageId' => ['label' => Craft::t('_craft-anonymous-portal', 'Anfrage ID')],
            'sender' => ['label' => Craft::t('_craft-anonymous-portal', 'Sender')],
            'body' => ['label' => Craft::t('_craft-anonymous-portal', 'Message')],
            'ts' => ['label' => Craft::t('_craft-anonymous-portal', 'Timestamp')],
        ];
    }

    protected static function defineDefaultTableAttributes(string $source): array
    {
        return ['anfrageId', 'sender', 'body', 'ts'];
    }

    public function getTableAttributeHtml(string $attribute): string
    {
        switch ($attribute) {
            case 'sender':
                $class = $this->sender === 'visitor' ? 'status green' : 'status blue';
                return '<span class="' . $class . '">' . ucfirst($this->sender) . '</span>';
            case 'body':
                return substr($this->body ?? '', 0, 100) . (strlen($this->body ?? '') > 100 ? '...' : '');
            case 'ts':
                return $this->ts ? Craft::$app->formatter->asDatetime($this->ts, 'short') : '';
            default:
                return parent::getTableAttributeHtml($attribute);
        }
    }

    public function getCpEditUrl(): ?string
    {
        return null; // No CP editing for security reasons
    }

    public function getUriFormat(): ?string
    {
        return null;
    }

    protected function defineRules(): array
    {
        $rules = parent::defineRules();
        $rules[] = [['anfrageId', 'sender', 'body'], 'required'];
        $rules[] = [['anfrageId'], 'integer'];
        $rules[] = [['sender'], 'in', 'range' => ['visitor', 'consultant']];
        $rules[] = [['body'], 'string'];
        $rules[] = [['ts'], 'datetime'];
        return $rules;
    }

    public function afterSave(bool $isNew): void
    {
        if ($isNew) {
            $record = new AntwortRecord();
            $record->id = (int)$this->id;
        } else {
            $record = AntwortRecord::findOne($this->id);
        }

        $record->anfrageId = $this->anfrageId;
        $record->sender = $this->sender;
        $record->body = $this->body;
        $record->ts = $this->ts ?: new \DateTime();

        $record->save(false);

        parent::afterSave($isNew);
    }

    public function afterDelete(): void
    {
        $record = AntwortRecord::findOne($this->id);
        if ($record) {
            $record->delete();
        }

        parent::afterDelete();
    }

    public static function create(Anfrage $anfrage, string $sender, string $body): static
    {
        $antwort = new static();
        $antwort->anfrageId = $anfrage->id;
        $antwort->sender = $sender;
        $antwort->body = $body;
        $antwort->ts = new \DateTime();

        if (!Craft::$app->elements->saveElement($antwort)) {
            throw new \Exception('Could not save Antwort: ' . implode(', ', $antwort->getErrorSummary(true)));
        }

        return $antwort;
    }
}
