<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use kldev\anonymousportal\elements\Anfrage;
use kldev\anonymousportal\elements\Antwort;
use kldev\anonymousportal\Plugin;

/**
 * Antwort service
 */
class AntwortService extends Component
{
    /**
     * Create a new message in a thread
     */
    public function createMessage(Anfrage $anfrage, string $sender, string $body): Antwort
    {
        $plugin = Plugin::getInstance();
        
        // Send to Matrix if from visitor
        if ($sender === 'visitor') {
            $plugin->matrixService->sendMessage($anfrage->roomId, $body, '@craftbridge:domain');
        }

        // Create Antwort element
        $antwort = Antwort::create($anfrage, $sender, $body);

        // Send email notification if from consultant and visitor has email
        if ($sender === 'consultant' && $anfrage->encryptedEmail) {
            $this->sendVisitorNotification($anfrage, $body);
        }

        return $antwort;
    }

    /**
     * Get all messages for an Anfrage
     */
    public function getMessages(Anfrage $anfrage): array
    {
        return Antwort::find()
            ->anfrageId($anfrage->id)
            ->orderBy('ts ASC')
            ->all();
    }

    /**
     * Send email notification to visitor
     */
    private function sendVisitorNotification(Anfrage $anfrage, string $message): void
    {
        if (!$anfrage->encryptedEmail) {
            return;
        }

        $settings = Plugin::getInstance()->getSettings();
        $visitorEmail = Plugin::getInstance()->anfrageService->decryptEmail($anfrage->encryptedEmail);
        
        $subject = 'Neue Antwort auf Ihre Beratungsanfrage - ID: ' . $anfrage->publicId;
        $body = "Sie haben eine neue Antwort auf Ihre Beratungsanfrage erhalten.\n\n";
        $body .= "Nachricht:\n" . $message . "\n\n";
        $body .= "Sie können Ihre Anfrage hier einsehen und antworten:\n";
        $site = Craft::$app->sites->currentSite;
        $baseUrl = $site->baseUrl ?: 'http://localhost';
        $body .= $baseUrl . '/anfrage/' . $anfrage->publicId . "\n\n";
        $body .= "Ihre Anfrage-ID: " . $anfrage->publicId;

        try {
            Craft::$app->mailer->compose()
                ->setFrom([$settings->fromEmail => 'Anonymous Portal'])
                ->setTo($visitorEmail)
                ->setSubject($subject)
                ->setTextBody($body)
                ->send();
        } catch (\Exception $e) {
            Craft::error('Failed to send visitor notification: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * Process incoming message from Matrix/Postmoogle webhook
     */
    public function processIncomingMessage(string $roomId, string $message, string $sender = 'consultant'): ?Antwort
    {
        $anfrage = Anfrage::find()->roomId($roomId)->one();
        
        if (!$anfrage) {
            Craft::warning('Received message for unknown room: ' . $roomId, __METHOD__);
            return null;
        }

        return $this->createMessage($anfrage, $sender, $message);
    }
}
