<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use kldev\anonymousportal\elements\Anfrage;
use kldev\anonymousportal\elements\Antwort;
use kldev\anonymousportal\Plugin;

/**
 * Antwort service
 */
class AntwortService extends Component
{
    /**
     * Create a new message in a thread
     */
    public function createMessage(Anfrage $anfrage, string $sender, string $body): Antwort
    {
        // Create Antwort element
        $antwort = Antwort::create($anfrage, $sender, $body);

        // Send email notification based on sender
        if ($sender === 'visitor') {
            // Visitor sent message - notify consultant
            $this->sendConsultantNotification($anfrage, $body);
        } elseif ($sender === 'consultant' && $anfrage->encryptedEmail) {
            // Consultant replied - notify visitor if they provided email
            $this->sendVisitorNotification($anfrage, $body);
        }

        return $antwort;
    }

    /**
     * Get all messages for an Anfrage
     */
    public function getMessages(Anfrage $anfrage): array
    {
        return Antwort::find()
            ->anfrageId($anfrage->id)
            ->orderBy('ts ASC')
            ->all();
    }

    /**
     * Send email notification to visitor
     */
    private function sendVisitorNotification(Anfrage $anfrage, string $message): void
    {
        if (!$anfrage->encryptedEmail) {
            return;
        }

        $settings = Plugin::getInstance()->getSettings();
        $visitorEmail = Plugin::getInstance()->anfrageService->decryptEmail($anfrage->encryptedEmail);
        
        $subject = 'Neue Antwort auf Ihre Beratungsanfrage - ID: ' . $anfrage->publicId;
        $body = "Sie haben eine neue Antwort auf Ihre Beratungsanfrage erhalten.\n\n";
        $body .= "Nachricht:\n" . $message . "\n\n";
        $body .= "Sie können Ihre Anfrage hier einsehen und antworten:\n";
        try {
            $site = Craft::$app->sites->currentSite;
            $baseUrl = $site->baseUrl ?: 'http://localhost';
        } catch (\Exception $e) {
            $baseUrl = 'http://localhost';
        }
        $body .= $baseUrl . '/anfrage/' . $anfrage->publicId . "\n\n";
        $body .= "Ihre Anfrage-ID: " . $anfrage->publicId;

        try {
            Craft::$app->mailer->compose()
                ->setFrom([$settings->fromEmail => 'Anonymous Portal'])
                ->setTo($visitorEmail)
                ->setSubject($subject)
                ->setTextBody($body)
                ->send();
        } catch (\Exception $e) {
            Craft::error('Failed to send visitor notification: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * Process incoming email reply from consultant
     */
    public function processIncomingEmail(string $replyToEmail, string $message, string $sender = 'consultant'): ?Antwort
    {
        $anfrage = Anfrage::find()->replyToEmail($replyToEmail)->one();

        if (!$anfrage) {
            Craft::warning('Received email for unknown reply-to address: ' . $replyToEmail, __METHOD__);
            return null;
        }

        return $this->createMessage($anfrage, $sender, $message);
    }

    /**
     * Send email notification to consultant about new visitor message
     */
    private function sendConsultantNotification(Anfrage $anfrage, string $message): void
    {
        $settings = Plugin::getInstance()->getSettings();

        $subject = $settings->emailSubjectPrefix . ' Neue Nachricht - ID: ' . $anfrage->publicId;
        $body = "Eine neue Nachricht ist in der Beratungsanfrage eingegangen.\n\n";
        $body .= "Anfrage-ID: " . $anfrage->publicId . "\n";
        $body .= "Nachricht:\n" . $message . "\n\n";
        $body .= "Um zu antworten, verwenden Sie die Reply-To Adresse oder antworten Sie direkt auf diese E-Mail.\n";
        $body .= "Portal-Link: " . $this->getPortalUrl() . "/anfrage/" . $anfrage->publicId;

        try {
            Craft::$app->mailer->compose()
                ->setFrom([$settings->fromEmail => 'Anonymous Portal'])
                ->setTo($settings->consultantEmail)
                ->setReplyTo($anfrage->replyToEmail)
                ->setSubject($subject)
                ->setTextBody($body)
                ->send();
        } catch (\Exception $e) {
            Craft::error('Failed to send consultant notification: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * Get portal URL
     */
    private function getPortalUrl(): string
    {
        try {
            $site = Craft::$app->sites->currentSite;
            return rtrim($site->baseUrl ?: 'http://localhost', '/');
        } catch (\Exception $e) {
            return 'http://localhost';
        }
    }
}
