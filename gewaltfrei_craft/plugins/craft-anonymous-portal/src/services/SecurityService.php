<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use kldev\anonymousportal\Plugin;

/**
 * Security service for rate limiting and session management
 */
class SecurityService extends Component
{
    private const RATE_LIMIT_KEY_PREFIX = 'anonymous_portal_rate_limit_';
    private const SESSION_KEY = 'anonymous_portal_thread_id';

    /**
     * Check if IP is rate limited for login attempts
     */
    public function isRateLimited(string $ip): bool
    {
        $settings = Plugin::getInstance()->getSettings();
        $key = self::RATE_LIMIT_KEY_PREFIX . 'ip_' . md5($ip);
        
        $cache = Craft::$app->cache;
        $attempts = $cache->get($key) ?: 0;
        
        return $attempts >= $settings->rateLimitAttempts;
    }

    /**
     * Record a failed login attempt
     */
    public function recordFailedAttempt(string $ip, string $publicId = null): void
    {
        $settings = Plugin::getInstance()->getSettings();
        $cache = Craft::$app->cache;
        $duration = $settings->rateLimitWindowMinutes * 60;

        // Rate limit by IP
        $ipKey = self::RATE_LIMIT_KEY_PREFIX . 'ip_' . md5($ip);
        $ipAttempts = $cache->get($ipKey) ?: 0;
        $cache->set($ipKey, $ipAttempts + 1, $duration);

        // Rate limit by publicId if provided
        if ($publicId) {
            $idKey = self::RATE_LIMIT_KEY_PREFIX . 'id_' . $publicId;
            $idAttempts = $cache->get($idKey) ?: 0;
            $cache->set($idKey, $idAttempts + 1, $duration);
        }
    }

    /**
     * Check if publicId is rate limited
     */
    public function isPublicIdRateLimited(string $publicId): bool
    {
        $key = self::RATE_LIMIT_KEY_PREFIX . 'id_' . $publicId;
        $cache = Craft::$app->cache;
        $attempts = $cache->get($key) ?: 0;
        
        return $attempts >= 5; // Stricter limit for specific IDs
    }

    /**
     * Clear rate limit for successful login
     */
    public function clearRateLimit(string $ip, string $publicId = null): void
    {
        $cache = Craft::$app->cache;
        
        $ipKey = self::RATE_LIMIT_KEY_PREFIX . 'ip_' . md5($ip);
        $cache->delete($ipKey);

        if ($publicId) {
            $idKey = self::RATE_LIMIT_KEY_PREFIX . 'id_' . $publicId;
            $cache->delete($idKey);
        }
    }

    /**
     * Set session for authenticated thread
     */
    public function setThreadSession(int $threadId): void
    {
        $session = Craft::$app->session;
        $session->set(self::SESSION_KEY, $threadId);
        
        // Set session timeout
        $settings = Plugin::getInstance()->getSettings();
        $session->setTimeout($settings->sessionTimeoutMinutes * 60);
    }

    /**
     * Get authenticated thread ID from session
     */
    public function getThreadSession(): ?int
    {
        return Craft::$app->session->get(self::SESSION_KEY);
    }

    /**
     * Clear thread session
     */
    public function clearThreadSession(): void
    {
        Craft::$app->session->remove(self::SESSION_KEY);
    }

    /**
     * Validate session for specific thread
     */
    public function validateThreadAccess(int $threadId, string $publicId): bool
    {
        $sessionThreadId = $this->getThreadSession();
        
        if (!$sessionThreadId || $sessionThreadId !== $threadId) {
            return false;
        }

        // Additional validation: ensure the thread exists and matches publicId
        $anfrage = \kldev\anonymousportal\elements\Anfrage::find()
            ->id($threadId)
            ->publicId($publicId)
            ->one();

        return $anfrage !== null;
    }

    /**
     * Generate secure random token for CSRF-like protection
     */
    public function generateSecureToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Sanitize user input
     */
    public function sanitizeInput(string $input): string
    {
        return trim(strip_tags($input));
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, array $context = []): void
    {
        $ip = Craft::$app->request->userIP;
        $userAgent = Craft::$app->request->userAgent;
        
        $logContext = array_merge($context, [
            'ip' => $ip,
            'userAgent' => $userAgent,
            'timestamp' => date('Y-m-d H:i:s'),
        ]);

        Craft::info("Security Event: {$event}", __METHOD__, $logContext);
    }
}
