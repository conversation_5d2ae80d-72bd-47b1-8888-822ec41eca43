<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use craft\db\Query;
use kldev\anonymousportal\Plugin;

/**
 * Installation service
 */
class InstallService extends Component
{
    /**
     * Perform post-installation tasks
     */
    public function afterInstall(): void
    {
        try {
            $this->createDefaultSettings();
            $this->logInstallation();
        } catch (\Exception $e) {
            Craft::error('Post-install tasks failed: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * Create default plugin settings
     */
    private function createDefaultSettings(): void
    {
        $plugin = Plugin::getInstance();
        $settings = $plugin->getSettings();

        // Set default values if not already configured
        if (empty($settings->sessionTimeoutMinutes)) {
            $settings->sessionTimeoutMinutes = 30;
        }

        if (empty($settings->cleanupDays)) {
            $settings->cleanupDays = 90;
        }

        if (empty($settings->rateLimitAttempts)) {
            $settings->rateLimitAttempts = 5;
        }

        if (empty($settings->rateLimitWindowMinutes)) {
            $settings->rateLimitWindowMinutes = 10;
        }

        // Save settings
        Craft::$app->plugins->savePluginSettings($plugin, $settings->toArray());
    }

    /**
     * Log successful installation
     */
    private function logInstallation(): void
    {
        Craft::info('Anonymous Portal plugin installed successfully', __METHOD__);
    }

    /**
     * Check if plugin is properly configured
     */
    public function isConfigured(): bool
    {
        $settings = Plugin::getInstance()->getSettings();

        return !empty($settings->consultantEmail) &&
               !empty($settings->fromEmail);
    }

    /**
     * Get configuration status
     */
    public function getConfigurationStatus(): array
    {
        $settings = Plugin::getInstance()->getSettings();

        return [
            'email_configured' => !empty($settings->consultantEmail) && !empty($settings->fromEmail),
            'fully_configured' => $this->isConfigured(),
        ];
    }

    /**
     * Test email configuration
     */
    public function testEmailConfiguration(): array
    {
        $settings = Plugin::getInstance()->getSettings();

        try {
            $subject = '[Test] Anonymous Portal Email Configuration';
            $body = "This is a test email from the Anonymous Portal plugin.\n\n";
            $body .= "If you receive this email, your email configuration is working correctly.\n";
            $body .= "Sent at: " . date('Y-m-d H:i:s');

            Craft::$app->mailer->compose()
                ->setFrom([$settings->fromEmail => 'Anonymous Portal'])
                ->setTo($settings->consultantEmail)
                ->setSubject($subject)
                ->setTextBody($body)
                ->send();

            return [
                'status' => 'success',
                'error' => null,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get plugin statistics
     */
    public function getStatistics(): array
    {
        $totalAnfragen = (new Query())
            ->from('{{%anonymousportal_anfragen}}')
            ->count();

        $totalAntworten = (new Query())
            ->from('{{%anonymousportal_antworten}}')
            ->count();

        $recentAnfragen = (new Query())
            ->from('{{%anonymousportal_anfragen}}')
            ->where(['>', 'dateCreated', new \DateTime('-7 days')])
            ->count();

        $settings = Plugin::getInstance()->getSettings();
        $cutoffDate = new \DateTime("-{$settings->cleanupDays} days");
        
        $oldAnfragen = (new Query())
            ->from('{{%anonymousportal_anfragen}}')
            ->where(['<', 'dateCreated', $cutoffDate])
            ->count();

        return [
            'total_consultations' => $totalAnfragen,
            'total_messages' => $totalAntworten,
            'recent_consultations' => $recentAnfragen,
            'old_consultations' => $oldAnfragen,
            'cleanup_days' => $settings->cleanupDays,
        ];
    }
}
