<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use craft\db\Query;
use kldev\anonymousportal\Plugin;

/**
 * Installation service
 */
class InstallService extends Component
{
    /**
     * Perform post-installation tasks
     */
    public function afterInstall(): void
    {
        try {
            $this->createDefaultSettings();
            $this->logInstallation();
        } catch (\Exception $e) {
            Craft::error('Post-install tasks failed: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * Create default plugin settings
     */
    private function createDefaultSettings(): void
    {
        $plugin = Plugin::getInstance();
        $settings = $plugin->getSettings();

        // Set default values if not already configured
        if (empty($settings->sessionTimeoutMinutes)) {
            $settings->sessionTimeoutMinutes = 30;
        }

        if (empty($settings->cleanupDays)) {
            $settings->cleanupDays = 90;
        }

        if (empty($settings->rateLimitAttempts)) {
            $settings->rateLimitAttempts = 5;
        }

        if (empty($settings->rateLimitWindowMinutes)) {
            $settings->rateLimitWindowMinutes = 10;
        }

        // Save settings
        Craft::$app->plugins->savePluginSettings($plugin, $settings->toArray());
    }

    /**
     * Log successful installation
     */
    private function logInstallation(): void
    {
        Craft::info('Anonymous Portal plugin installed successfully', __METHOD__);
    }

    /**
     * Check if plugin is properly configured
     */
    public function isConfigured(): bool
    {
        $settings = Plugin::getInstance()->getSettings();

        return !empty($settings->matrixHomeserver) &&
               !empty($settings->matrixAccessToken) &&
               !empty($settings->postmoogleApiUrl) &&
               !empty($settings->postmoogleApiKey) &&
               !empty($settings->consultantEmail) &&
               !empty($settings->fromEmail);
    }

    /**
     * Get configuration status
     */
    public function getConfigurationStatus(): array
    {
        $settings = Plugin::getInstance()->getSettings();
        
        return [
            'matrix_configured' => !empty($settings->matrixHomeserver) && !empty($settings->matrixAccessToken),
            'postmoogle_configured' => !empty($settings->postmoogleApiUrl) && !empty($settings->postmoogleApiKey),
            'email_configured' => !empty($settings->consultantEmail) && !empty($settings->fromEmail),
            'fully_configured' => $this->isConfigured(),
        ];
    }

    /**
     * Test all connections
     */
    public function testAllConnections(): array
    {
        $plugin = Plugin::getInstance();
        $results = [];

        // Test Matrix
        try {
            $results['matrix'] = [
                'status' => $plugin->matrixService->testConnection() ? 'success' : 'failed',
                'error' => null,
            ];
        } catch (\Exception $e) {
            $results['matrix'] = [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        // Test Postmoogle
        try {
            $results['postmoogle'] = [
                'status' => $plugin->postmoogleService->testConnection() ? 'success' : 'failed',
                'error' => null,
            ];
        } catch (\Exception $e) {
            $results['postmoogle'] = [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        return $results;
    }

    /**
     * Get plugin statistics
     */
    public function getStatistics(): array
    {
        $totalAnfragen = (new Query())
            ->from('{{%anonymousportal_anfragen}}')
            ->count();

        $totalAntworten = (new Query())
            ->from('{{%anonymousportal_antworten}}')
            ->count();

        $recentAnfragen = (new Query())
            ->from('{{%anonymousportal_anfragen}}')
            ->where(['>', 'dateCreated', new \DateTime('-7 days')])
            ->count();

        $settings = Plugin::getInstance()->getSettings();
        $cutoffDate = new \DateTime("-{$settings->cleanupDays} days");
        
        $oldAnfragen = (new Query())
            ->from('{{%anonymousportal_anfragen}}')
            ->where(['<', 'dateCreated', $cutoffDate])
            ->count();

        return [
            'total_consultations' => $totalAnfragen,
            'total_messages' => $totalAntworten,
            'recent_consultations' => $recentAnfragen,
            'old_consultations' => $oldAnfragen,
            'cleanup_days' => $settings->cleanupDays,
        ];
    }
}
