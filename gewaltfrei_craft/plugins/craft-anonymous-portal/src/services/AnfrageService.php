<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use kldev\anonymousportal\elements\Anfrage;
use kldev\anonymousportal\Plugin;

/**
 * Anfrage service
 */
class AnfrageService extends Component
{
    /**
     * Create a new Anfrage with generated credentials
     */
    public function createAnfrage(string $initialMessage = '', ?string $visitorEmail = null): array
    {
        $plugin = Plugin::getInstance();
        
        // Generate credentials
        $publicId = bin2hex(random_bytes(4)); // 8 hex characters
        $secret = rtrim(strtr(base64_encode(random_bytes(16)), '+/', '-_'), '='); // ~22 characters
        $secretHash = password_hash($secret, PASSWORD_ARGON2ID, [
            'memory_cost' => 1 << 13, // 8MB
            'time_cost' => 4,
            'threads' => 1,
        ]);

        // Generate unique reply-to email
        $settings = $plugin->getSettings();
        $replyToEmail = $settings->replyToPrefix . $publicId . '@' . $this->getDomain();

        // Create Anfrage element
        $anfrage = new Anfrage();
        $anfrage->publicId = $publicId;
        $anfrage->secretHash = $secretHash;
        $anfrage->replyToEmail = $replyToEmail;
        
        if ($visitorEmail) {
            $anfrage->encryptedEmail = $this->encryptEmail($visitorEmail);
        }

        if (!Craft::$app->elements->saveElement($anfrage)) {
            throw new \Exception('Could not save Anfrage: ' . implode(', ', $anfrage->getErrorSummary(true)));
        }

        // Send initial message if provided
        if ($initialMessage) {
            $plugin->antwortService->createMessage($anfrage, 'visitor', $initialMessage);
        }

        // Send notification email to consultants
        $this->sendConsultantNotification($anfrage, $initialMessage);

        return [
            'anfrage' => $anfrage,
            'secret' => $secret,
            'publicId' => $publicId,
        ];
    }

    /**
     * Verify credentials and return Anfrage if valid
     */
    public function verifyCredentials(string $publicId, string $secret): ?Anfrage
    {
        $anfrage = Anfrage::find()->publicId($publicId)->one();
        
        if (!$anfrage || !password_verify($secret, $anfrage->secretHash)) {
            return null;
        }

        return $anfrage;
    }

    /**
     * Send notification email to consultants
     */
    private function sendConsultantNotification(Anfrage $anfrage, string $initialMessage): void
    {
        $settings = Plugin::getInstance()->getSettings();

        $subject = $settings->emailSubjectPrefix . ' Neue Anfrage - ID: ' . $anfrage->publicId;
        $body = "Eine neue anonyme Beratungsanfrage ist eingegangen.\n\n";
        $body .= "Anfrage-ID: " . $anfrage->publicId . "\n";
        $body .= "Reply-To E-Mail: " . $anfrage->replyToEmail . "\n\n";

        if ($initialMessage) {
            $body .= "Erste Nachricht:\n" . $initialMessage . "\n\n";
        }

        $body .= "WICHTIG: Um zu antworten, verwenden Sie die Reply-To Adresse: " . $anfrage->replyToEmail . "\n";
        $body .= "Ihre Antwort wird automatisch an den Ratsuchenden weitergeleitet.\n\n";
        $body .= "Portal-Link: " . $this->getPortalUrl() . "/anfrage/" . $anfrage->publicId;

        try {
            Craft::$app->mailer->compose()
                ->setFrom([$settings->fromEmail => 'Anonymous Portal'])
                ->setTo($settings->consultantEmail)
                ->setReplyTo($anfrage->replyToEmail)
                ->setSubject($subject)
                ->setTextBody($body)
                ->send();
        } catch (\Exception $e) {
            Craft::error('Failed to send consultant notification: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * Encrypt visitor email for storage
     */
    private function encryptEmail(string $email): string
    {
        // Simple encryption - in production, use proper encryption
        return base64_encode($email);
    }

    /**
     * Decrypt visitor email
     */
    public function decryptEmail(string $encryptedEmail): string
    {
        return base64_decode($encryptedEmail);
    }

    /**
     * Clean up old Anfragen
     */
    public function cleanupOldAnfragen(int $days = 90): int
    {
        $cutoffDate = new \DateTime("-{$days} days");

        $anfragen = Anfrage::find()
            ->dateCreated('< ' . $cutoffDate->format('Y-m-d H:i:s'))
            ->all();

        $count = 0;
        foreach ($anfragen as $anfrage) {
            try {
                // Delete Anfrage (will cascade delete Antworten)
                Craft::$app->elements->deleteElement($anfrage);
                $count++;
            } catch (\Exception $e) {
                Craft::error('Failed to cleanup Anfrage ' . $anfrage->publicId . ': ' . $e->getMessage(), __METHOD__);
            }
        }

        return $count;
    }

    /**
     * Get domain for reply-to emails
     */
    private function getDomain(): string
    {
        try {
            $site = Craft::$app->sites->currentSite;
            $baseUrl = $site->baseUrl ?: 'http://localhost';
            return parse_url($baseUrl, PHP_URL_HOST) ?: 'localhost';
        } catch (\Exception $e) {
            return 'localhost';
        }
    }

    /**
     * Get portal URL
     */
    private function getPortalUrl(): string
    {
        try {
            $site = Craft::$app->sites->currentSite;
            return rtrim($site->baseUrl ?: 'http://localhost', '/');
        } catch (\Exception $e) {
            return 'http://localhost';
        }
    }
}
