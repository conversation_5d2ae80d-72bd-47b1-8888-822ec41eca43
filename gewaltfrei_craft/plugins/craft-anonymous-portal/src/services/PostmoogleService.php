<?php

namespace kldev\anonymousportal\services;

use Craft;
use craft\base\Component;
use kldev\anonymousportal\Plugin;

/**
 * Postmoogle service for mailbox management
 */
class PostmoogleService extends Component
{
    /**
     * Create a new mailbox for a thread
     */
    public function createMailbox(string $publicId): string
    {
        $settings = Plugin::getInstance()->getSettings();

        // Get domain safely (works in console and web context)
        try {
            $site = Craft::$app->sites->currentSite;
            $baseUrl = $site->baseUrl ?: 'http://localhost';
            $domain = parse_url($baseUrl, PHP_URL_HOST) ?: 'localhost';
        } catch (\Exception $e) {
            // Fallback for console context
            $domain = 'localhost';
        }

        $mailbox = $publicId . '@kontakt.' . $domain;
        
        $data = [
            'mailbox' => $mailbox,
            'room_id' => null, // Will be set later when we have the room ID
        ];

        $response = $this->makePostmoogleRequest('POST', '/mailbox', $data);
        
        if (!$response || !isset($response['mailbox'])) {
            throw new \Exception('Failed to create Postmoogle mailbox');
        }

        return $response['mailbox'];
    }

    /**
     * Link mailbox to Matrix room
     */
    public function linkMailboxToRoom(string $mailbox, string $roomId): bool
    {
        $data = [
            'mailbox' => $mailbox,
            'room_id' => $roomId,
        ];

        $response = $this->makePostmoogleRequest('PUT', '/mailbox', $data);
        
        return $response !== null;
    }

    /**
     * Delete a mailbox
     */
    public function deleteMailbox(string $mailbox): bool
    {
        try {
            $this->makePostmoogleRequest('DELETE', '/mailbox/' . urlencode($mailbox));
            return true;
        } catch (\Exception $e) {
            Craft::error('Failed to delete Postmoogle mailbox ' . $mailbox . ': ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * Get mailbox information
     */
    public function getMailboxInfo(string $mailbox): ?array
    {
        try {
            return $this->makePostmoogleRequest('GET', '/mailbox/' . urlencode($mailbox));
        } catch (\Exception $e) {
            Craft::error('Failed to get Postmoogle mailbox info for ' . $mailbox . ': ' . $e->getMessage(), __METHOD__);
            return null;
        }
    }

    /**
     * List all mailboxes (for admin purposes)
     */
    public function listMailboxes(): array
    {
        try {
            $response = $this->makePostmoogleRequest('GET', '/mailboxes');
            return $response['mailboxes'] ?? [];
        } catch (\Exception $e) {
            Craft::error('Failed to list Postmoogle mailboxes: ' . $e->getMessage(), __METHOD__);
            return [];
        }
    }

    /**
     * Make a request to the Postmoogle API
     */
    private function makePostmoogleRequest(string $method, string $endpoint, array $data = []): ?array
    {
        $settings = Plugin::getInstance()->getSettings();
        
        if (!$settings->postmoogleApiUrl || !$settings->postmoogleApiKey) {
            throw new \Exception('Postmoogle configuration is incomplete');
        }

        $url = rtrim($settings->postmoogleApiUrl, '/') . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $settings->postmoogleApiKey,
            'Content-Type: application/json',
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
        ]);

        if ($method !== 'GET' && !empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new \Exception('Postmoogle API request failed: ' . $error);
        }

        if ($httpCode >= 400) {
            $errorMsg = "Postmoogle API returned HTTP {$httpCode}";
            if ($response) {
                $errorData = json_decode($response, true);
                if (isset($errorData['error'])) {
                    $errorMsg .= ': ' . $errorData['error'];
                }
            }
            throw new \Exception($errorMsg);
        }

        return json_decode($response, true);
    }

    /**
     * Test Postmoogle connection
     */
    public function testConnection(): bool
    {
        try {
            $response = $this->makePostmoogleRequest('GET', '/health');
            return isset($response['status']) && $response['status'] === 'ok';
        } catch (\Exception $e) {
            Craft::error('Postmoogle connection test failed: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }
}
