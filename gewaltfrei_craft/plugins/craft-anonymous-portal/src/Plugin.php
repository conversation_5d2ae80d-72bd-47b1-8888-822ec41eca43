<?php

namespace kldev\anonymousportal;

use Craft;
use craft\base\Model;
use craft\base\Plugin as BasePlugin;
use craft\console\Application as ConsoleApplication;
use craft\events\RegisterComponentTypesEvent;
use craft\events\RegisterTemplateRootsEvent;
use craft\events\RegisterUrlRulesEvent;
use craft\services\Dashboard;
use craft\services\Elements;
use craft\web\View;
use craft\web\UrlManager;
use kldev\anonymousportal\console\controllers\CleanupController;
use kldev\anonymousportal\elements\Anfrage;
use kldev\anonymousportal\elements\Antwort;
use kldev\anonymousportal\models\Settings;
use kldev\anonymousportal\services\AnfrageService;
use kldev\anonymousportal\services\AntwortService;
use kldev\anonymousportal\services\EmailService;
use kldev\anonymousportal\services\InstallService;
use kldev\anonymousportal\services\SecurityService;
use kldev\anonymousportal\widgets\AnonymousPortalWidget;
use yii\base\Event;

/**
 * Anonymous Portal plugin
 *
 * @method static Plugin getInstance()
 * @method Settings getSettings()
 * @property-read AnfrageService $anfrageService
 * @property-read AntwortService $antwortService
 * @property-read InstallService $installService
 * @property-read SecurityService $securityService
 */
class Plugin extends BasePlugin
{
    public string $schemaVersion = '1.0.0';
    public bool $hasCpSettings = true;

    public static function config(): array
    {
        return [
            'components' => [
                'anfrageService' => AnfrageService::class,
                'antwortService' => AntwortService::class,
                'installService' => InstallService::class,
                'securityService' => SecurityService::class,
            ],
        ];
    }

    public function init(): void
    {
        parent::init();

        // Register console commands
        if (Craft::$app instanceof ConsoleApplication) {
            $this->controllerNamespace = 'kldev\\anonymousportal\\console\\controllers';
        }

        Craft::$app->onInit(function () {
            $this->_attachEventListeners();
        });
    }

    private function _attachEventListeners(): void
    {
        // Register element types
        Event::on(
            Elements::class,
            Elements::EVENT_REGISTER_ELEMENT_TYPES,
            function (RegisterComponentTypesEvent $event) {
                $event->types[] = Anfrage::class;
                $event->types[] = Antwort::class;
            }
        );

        // Register widget types
        Event::on(
            Dashboard::class,
            Dashboard::EVENT_REGISTER_WIDGET_TYPES,
            function (RegisterComponentTypesEvent $event) {
                $event->types[] = AnonymousPortalWidget::class;
            }
        );

        $this->_attachSiteEventListeners();
    }

    public function install(): void
    {
        parent::install();

        // Only run post-install tasks in web context to avoid console issues
        if (!Craft::$app instanceof ConsoleApplication) {
            $this->installService->afterInstall();
        }
    }

    protected function createSettingsModel(): ?Model
    {
        return Craft::createObject(Settings::class);
    }

    public function getSettingsResponse(): mixed
    {
        return Craft::$app
            ->controller
            ->asCpScreen()
            ->action('plugins/save-plugin-settings')
            ->title(Craft::t('_craft-anonymous-portal', 'Anonymous Portal Settings'))
            ->contentTemplate('_craft-anonymous-portal/_settings/_settings', [
                'plugin' => $this,
                'settings' => $this->getSettings(),
            ])
            ->redirectUrl('/admin/settings');
    }

    private function _attachSiteEventListeners(): void
    {
        Event::on(
            View::class,
            View::EVENT_REGISTER_SITE_TEMPLATE_ROOTS,
            function (RegisterTemplateRootsEvent $event) {
                $event->roots[$this->id] = $this->getBasePath() . DIRECTORY_SEPARATOR . 'templates/_frontend';
            }
        );

        Event::on(
            UrlManager::class,
            UrlManager::EVENT_REGISTER_SITE_URL_RULES,
            function (RegisterUrlRulesEvent $event) {
                $event->rules['anfrage'] = '_craft-anonymous-portal/auth/index';
                $event->rules['anfrage/send'] = '_craft-anonymous-portal/auth/send';
                $event->rules['anfrage/login'] = '_craft-anonymous-portal/auth/login';
                $event->rules['anfrage/logout'] = '_craft-anonymous-portal/auth/logout';
                $event->rules['anfrage/<id:\w{8}>'] = '_craft-anonymous-portal/thread/view';
                $event->rules['anfrage/<id:\w{8}>/reply'] = '_craft-anonymous-portal/thread/reply';
                $event->rules['anfrage/<id:\w{8}>/messages'] = '_craft-anonymous-portal/thread/messages';
                $event->rules['anfrage/<id:\w{8}>/download'] = '_craft-anonymous-portal/thread/download-pdf';

                // Webhook endpoints
                $event->rules['webhooks/anonymous-portal/email'] = '_craft-anonymous-portal/webhook/email';
                $event->rules['webhooks/anonymous-portal/health'] = '_craft-anonymous-portal/webhook/health';

                // Admin endpoints
                $event->rules['admin/anonymous-portal/cleanup'] = '_craft-anonymous-portal/admin/run-cleanup';
                $event->rules['admin/anonymous-portal/stats'] = '_craft-anonymous-portal/admin/stats';
                $event->rules['admin/anonymous-portal/test-email'] = '_craft-anonymous-portal/admin/test-email';
                $event->rules['admin/anonymous-portal/export'] = '_craft-anonymous-portal/admin/export-data';
            }
        );
    }
}
