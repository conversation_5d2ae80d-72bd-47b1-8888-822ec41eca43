<?php

namespace kldev\anonymousportal\records;

use craft\db\ActiveRecord;
use craft\records\Element;

/**
 * Antwort record
 *
 * @property int $id
 * @property int $anfrageId
 * @property string $sender
 * @property string $body
 * @property \DateTime $ts
 */
class AntwortRecord extends ActiveRecord
{
    public static function tableName(): string
    {
        return '{{%anonymousportal_antworten}}';
    }

    public function getElement()
    {
        return $this->hasOne(Element::class, ['id' => 'id']);
    }

    public function getAnfrage()
    {
        return $this->hasOne(AnfrageRecord::class, ['id' => 'anfrageId']);
    }
}
