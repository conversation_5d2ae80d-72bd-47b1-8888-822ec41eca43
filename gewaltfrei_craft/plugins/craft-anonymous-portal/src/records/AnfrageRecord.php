<?php

namespace kldev\anonymousportal\records;

use craft\db\ActiveRecord;
use craft\records\Element;

/**
 * Anfrage record
 *
 * @property int $id
 * @property string $publicId
 * @property string $secretHash
 * @property string|null $replyToEmail
 * @property string|null $encryptedEmail
 */
class AnfrageRecord extends ActiveRecord
{
    public static function tableName(): string
    {
        return '{{%anonymousportal_anfragen}}';
    }

    public function getElement()
    {
        return $this->hasOne(Element::class, ['id' => 'id']);
    }
}
