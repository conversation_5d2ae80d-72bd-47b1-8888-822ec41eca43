<?php

namespace kldev\anonymousportal\console\controllers;

use Craft;
use craft\console\Controller;
use craft\helpers\Console;
use kldev\anonymousportal\Plugin;
use yii\console\ExitCode;

/**
 * Cleanup controller for console commands
 */
class CleanupController extends Controller
{
    /**
     * @var int Number of days after which to delete consultations
     */
    public int $days = 90;

    /**
     * @var bool Whether to run in dry-run mode (don't actually delete)
     */
    public bool $dryRun = false;

    /**
     * @var bool Whether to force deletion without confirmation
     */
    public bool $force = false;

    public function options($actionID): array
    {
        return array_merge(parent::options($actionID), ['days', 'dryRun', 'force']);
    }

    public function optionAliases(): array
    {
        return array_merge(parent::optionAliases(), [
            'd' => 'days',
            'n' => 'dryRun',
            'f' => 'force',
        ]);
    }

    /**
     * Clean up old consultation threads
     */
    public function actionThreads(): int
    {
        $plugin = Plugin::getInstance();
        
        if (!$plugin) {
            $this->stderr("Anonymous Portal plugin is not installed.\n", Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }

        $this->stdout("Anonymous Portal Cleanup\n", Console::FG_CYAN);
        $this->stdout("========================\n\n");

        // Get settings
        $settings = $plugin->getSettings();
        $cleanupDays = $this->days ?: $settings->cleanupDays;

        $this->stdout("Cleanup configuration:\n");
        $this->stdout("- Days: {$cleanupDays}\n");
        $this->stdout("- Dry run: " . ($this->dryRun ? 'Yes' : 'No') . "\n");
        $this->stdout("- Force: " . ($this->force ? 'Yes' : 'No') . "\n\n");

        // Find old consultations
        $cutoffDate = new \DateTime("-{$cleanupDays} days");
        $anfragen = \kldev\anonymousportal\elements\Anfrage::find()
            ->dateCreated('< ' . $cutoffDate->format('Y-m-d H:i:s'))
            ->all();

        $count = count($anfragen);

        if ($count === 0) {
            $this->stdout("No consultations found for cleanup.\n", Console::FG_GREEN);
            return ExitCode::OK;
        }

        $this->stdout("Found {$count} consultation(s) to clean up:\n\n");

        // Show what will be deleted
        foreach ($anfragen as $anfrage) {
            $messageCount = \kldev\anonymousportal\elements\Antwort::find()
                ->anfrageId($anfrage->id)
                ->count();
            
            $this->stdout("- ID: {$anfrage->publicId} | Created: {$anfrage->dateCreated->format('Y-m-d H:i:s')} | Messages: {$messageCount}\n");
        }

        $this->stdout("\n");

        // Confirm deletion unless forced
        if (!$this->force && !$this->dryRun) {
            if (!$this->confirm("Are you sure you want to delete these {$count} consultation(s)?")) {
                $this->stdout("Cleanup cancelled.\n", Console::FG_YELLOW);
                return ExitCode::OK;
            }
        }

        if ($this->dryRun) {
            $this->stdout("DRY RUN: Would delete {$count} consultation(s).\n", Console::FG_YELLOW);
            return ExitCode::OK;
        }

        // Perform cleanup
        $this->stdout("Starting cleanup...\n");
        $deleted = 0;
        $errors = 0;

        foreach ($anfragen as $anfrage) {
            try {
                $this->stdout("Deleting consultation {$anfrage->publicId}... ");

                // Delete Matrix room
                $plugin->matrixService->deleteRoom($anfrage->roomId);
                
                // Delete Postmoogle mailbox
                $plugin->postmoogleService->deleteMailbox($anfrage->mailbox);
                
                // Delete Anfrage (will cascade delete Antworten)
                Craft::$app->elements->deleteElement($anfrage);
                
                $this->stdout("OK\n", Console::FG_GREEN);
                $deleted++;

            } catch (\Exception $e) {
                $this->stdout("ERROR: {$e->getMessage()}\n", Console::FG_RED);
                $errors++;
                
                Craft::error("Failed to cleanup consultation {$anfrage->publicId}: {$e->getMessage()}", __METHOD__);
            }
        }

        $this->stdout("\nCleanup completed:\n");
        $this->stdout("- Deleted: {$deleted}\n", Console::FG_GREEN);
        
        if ($errors > 0) {
            $this->stdout("- Errors: {$errors}\n", Console::FG_RED);
        }

        return $errors > 0 ? ExitCode::UNSPECIFIED_ERROR : ExitCode::OK;
    }

    /**
     * Show cleanup statistics
     */
    public function actionStats(): int
    {
        $plugin = Plugin::getInstance();
        
        if (!$plugin) {
            $this->stderr("Anonymous Portal plugin is not installed.\n", Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }

        $settings = $plugin->getSettings();
        $cleanupDays = $settings->cleanupDays;

        $this->stdout("Anonymous Portal Statistics\n", Console::FG_CYAN);
        $this->stdout("===========================\n\n");

        // Total consultations
        $totalAnfragen = \kldev\anonymousportal\elements\Anfrage::find()->count();
        $this->stdout("Total consultations: {$totalAnfragen}\n");

        // Total messages
        $totalAntworten = \kldev\anonymousportal\elements\Antwort::find()->count();
        $this->stdout("Total messages: {$totalAntworten}\n");

        // Consultations by age
        $cutoffDate = new \DateTime("-{$cleanupDays} days");
        $oldAnfragen = \kldev\anonymousportal\elements\Anfrage::find()
            ->dateCreated('< ' . $cutoffDate->format('Y-m-d H:i:s'))
            ->count();
        
        $recentAnfragen = $totalAnfragen - $oldAnfragen;

        $this->stdout("Recent consultations (< {$cleanupDays} days): {$recentAnfragen}\n");
        $this->stdout("Old consultations (>= {$cleanupDays} days): {$oldAnfragen}\n");

        if ($oldAnfragen > 0) {
            $this->stdout("\nConsultations ready for cleanup: {$oldAnfragen}\n", Console::FG_YELLOW);
            $this->stdout("Run 'php craft anonymous-portal/cleanup/threads' to clean them up.\n");
        }

        return ExitCode::OK;
    }

    /**
     * Test Matrix and Postmoogle connections
     */
    public function actionTestConnections(): int
    {
        $plugin = Plugin::getInstance();
        
        if (!$plugin) {
            $this->stderr("Anonymous Portal plugin is not installed.\n", Console::FG_RED);
            return ExitCode::UNSPECIFIED_ERROR;
        }

        $this->stdout("Testing Connections\n", Console::FG_CYAN);
        $this->stdout("==================\n\n");

        $errors = 0;

        // Test Matrix
        $this->stdout("Testing Matrix connection... ");
        try {
            if ($plugin->matrixService->testConnection()) {
                $this->stdout("OK\n", Console::FG_GREEN);
            } else {
                $this->stdout("FAILED\n", Console::FG_RED);
                $errors++;
            }
        } catch (\Exception $e) {
            $this->stdout("ERROR: {$e->getMessage()}\n", Console::FG_RED);
            $errors++;
        }

        // Test Postmoogle
        $this->stdout("Testing Postmoogle connection... ");
        try {
            if ($plugin->postmoogleService->testConnection()) {
                $this->stdout("OK\n", Console::FG_GREEN);
            } else {
                $this->stdout("FAILED\n", Console::FG_RED);
                $errors++;
            }
        } catch (\Exception $e) {
            $this->stdout("ERROR: {$e->getMessage()}\n", Console::FG_RED);
            $errors++;
        }

        if ($errors === 0) {
            $this->stdout("\nAll connections are working properly.\n", Console::FG_GREEN);
        } else {
            $this->stdout("\n{$errors} connection(s) failed. Check your configuration.\n", Console::FG_RED);
        }

        return $errors > 0 ? ExitCode::UNSPECIFIED_ERROR : ExitCode::OK;
    }
}
