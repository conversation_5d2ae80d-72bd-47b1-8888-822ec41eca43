<?php

namespace kldev\anonymousportal\controllers;

use Craft;
use craft\web\Controller;
use craft\web\Response;
use kldev\anonymousportal\Plugin;
use yii\web\BadRequestHttpException;

/**
 * Webhook controller for Matrix/Postmoogle integration
 */
class WebhookController extends Controller
{
    protected array|int|bool $allowAnonymous = true;
    public bool $enableCsrfValidation = false;

    /**
     * Handle incoming email replies from consultants
     */
    public function actionEmail(): Response
    {
        $this->requirePostRequest();

        $plugin = Plugin::getInstance();
        $request = Craft::$app->request;

        try {
            $data = json_decode($request->getRawBody(), true);

            if (!$data || !isset($data['to'], $data['body'])) {
                throw new BadRequestHttpException('Invalid webhook data');
            }

            $replyToEmail = $data['to'];
            $message = $data['body'];
            $subject = $data['subject'] ?? '';
            $from = $data['from'] ?? '';

            // Find the corresponding Anfrage
            $antwort = $plugin->antwortService->processIncomingEmail($replyToEmail, $message, 'consultant');

            if ($antwort) {
                return $this->asJson(['status' => 'processed', 'message_id' => $antwort->id]);
            } else {
                return $this->asJson(['status' => 'no_thread_found']);
            }

        } catch (\Exception $e) {
            Craft::error('Email webhook error: ' . $e->getMessage(), __METHOD__);
            return $this->asErrorJson('Webhook processing failed');
        }
    }



    /**
     * Health check endpoint
     */
    public function actionHealth(): Response
    {
        return $this->asJson([
            'status' => 'ok',
            'timestamp' => time(),
            'plugin' => 'anonymous-portal',
            'version' => Plugin::getInstance()->schemaVersion,
        ]);
    }


}
