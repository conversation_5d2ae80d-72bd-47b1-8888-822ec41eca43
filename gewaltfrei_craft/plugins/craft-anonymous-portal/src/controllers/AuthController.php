<?php

namespace kldev\anonymousportal\controllers;

use Craft;
use craft\web\Controller;
use craft\web\Response;
use kldev\anonymousportal\Plugin;
use yii\web\BadRequestHttpException;

/**
 * Authentication controller
 */
class AuthController extends Controller
{
    protected array|int|bool $allowAnonymous = true;

    /**
     * Show login form
     */
    public function actionIndex(): Response
    {
        return $this->renderTemplate('_craft-anonymous-portal/_frontend/auth/index');
    }

    /**
     * Create new consultation request
     */
    public function actionSend(): Response
    {
        $this->requirePostRequest();
        
        $plugin = Plugin::getInstance();
        $request = Craft::$app->request;
        $ip = $request->userIP;

        // Check rate limiting
        if ($plugin->securityService->isRateLimited($ip)) {
            $plugin->securityService->logSecurityEvent('rate_limit_exceeded', ['action' => 'send']);
            return $this->asErrorJson('Too many requests. Please try again later.');
        }

        try {
            $message = $plugin->securityService->sanitizeInput(
                $request->getRequiredBodyParam('message')
            );
            $email = $request->getBodyParam('email');
            
            if ($email) {
                $email = $plugin->securityService->sanitizeInput($email);
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    return $this->asErrorJson('Invalid email address.');
                }
            }

            if (strlen($message) < 10) {
                return $this->asErrorJson('Message must be at least 10 characters long.');
            }

            if (strlen($message) > 5000) {
                return $this->asErrorJson('Message is too long (max 5000 characters).');
            }

            // Create new consultation request
            $result = $plugin->anfrageService->createAnfrage($message, $email);
            
            $plugin->securityService->logSecurityEvent('anfrage_created', [
                'publicId' => $result['publicId']
            ]);

            return $this->renderTemplate('_craft-anonymous-portal/_frontend/auth/success', [
                'publicId' => $result['publicId'],
                'secret' => $result['secret'],
            ]);

        } catch (\Exception $e) {
            Craft::error('Failed to create consultation request: ' . $e->getMessage(), __METHOD__);
            return $this->asErrorJson('Failed to create consultation request. Please try again.');
        }
    }

    /**
     * Process login attempt
     */
    public function actionLogin(): Response
    {
        $this->requirePostRequest();
        
        $plugin = Plugin::getInstance();
        $request = Craft::$app->request;
        $ip = $request->userIP;

        try {
            $publicId = $plugin->securityService->sanitizeInput(
                $request->getRequiredBodyParam('id')
            );
            $secret = $request->getRequiredBodyParam('secret');

            // Validate input format
            if (!preg_match('/^[a-f0-9]{8}$/', $publicId)) {
                throw new BadRequestHttpException('Invalid ID format.');
            }

            if (strlen($secret) < 20 || strlen($secret) > 30) {
                throw new BadRequestHttpException('Invalid secret format.');
            }

            // Check rate limiting
            if ($plugin->securityService->isRateLimited($ip) || 
                $plugin->securityService->isPublicIdRateLimited($publicId)) {
                
                $plugin->securityService->logSecurityEvent('rate_limit_exceeded', [
                    'action' => 'login',
                    'publicId' => $publicId
                ]);
                
                Craft::$app->session->setFlash('error', 'Too many failed attempts. Please try again later.');
                return $this->redirect('/anfrage');
            }

            // Verify credentials
            $anfrage = $plugin->anfrageService->verifyCredentials($publicId, $secret);
            
            if (!$anfrage) {
                // Record failed attempt
                $plugin->securityService->recordFailedAttempt($ip, $publicId);
                $plugin->securityService->logSecurityEvent('login_failed', [
                    'publicId' => $publicId
                ]);
                
                Craft::$app->session->setFlash('error', 'Invalid ID or secret.');
                return $this->redirect('/anfrage');
            }

            // Successful login
            $plugin->securityService->clearRateLimit($ip, $publicId);
            $plugin->securityService->setThreadSession($anfrage->id);
            $plugin->securityService->logSecurityEvent('login_success', [
                'publicId' => $publicId
            ]);

            return $this->redirect("/anfrage/{$publicId}");

        } catch (BadRequestHttpException $e) {
            $plugin->securityService->recordFailedAttempt($ip);
            Craft::$app->session->setFlash('error', $e->getMessage());
            return $this->redirect('/anfrage');
        } catch (\Exception $e) {
            Craft::error('Login error: ' . $e->getMessage(), __METHOD__);
            Craft::$app->session->setFlash('error', 'An error occurred. Please try again.');
            return $this->redirect('/anfrage');
        }
    }

    /**
     * Logout and clear session
     */
    public function actionLogout(): Response
    {
        $plugin = Plugin::getInstance();
        $plugin->securityService->clearThreadSession();
        $plugin->securityService->logSecurityEvent('logout');
        
        Craft::$app->session->setFlash('notice', 'You have been logged out.');
        return $this->redirect('/anfrage');
    }
}
