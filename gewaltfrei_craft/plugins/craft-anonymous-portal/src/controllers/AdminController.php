<?php

namespace kldev\anonymousportal\controllers;

use Craft;
use craft\web\Controller;
use craft\web\Response;
use kldev\anonymousportal\Plugin;

/**
 * Admin controller for CP functionality
 */
class AdminController extends Controller
{
    protected array|int|bool $allowAnonymous = false;

    /**
     * Run cleanup manually from CP
     */
    public function actionRunCleanup(): Response
    {
        $this->requirePostRequest();
        $this->requirePermission('utility:system-report');
        
        $plugin = Plugin::getInstance();
        $request = Craft::$app->request;
        
        $days = $request->getBodyParam('days', $plugin->getSettings()->cleanupDays);
        $dryRun = $request->getBodyParam('dryRun', false);

        try {
            if ($dryRun) {
                // Just count what would be deleted
                $cutoffDate = new \DateTime("-{$days} days");
                $count = \kldev\anonymousportal\elements\Anfrage::find()
                    ->dateCreated('< ' . $cutoffDate->format('Y-m-d H:i:s'))
                    ->count();
                
                return $this->asJson([
                    'success' => true,
                    'message' => "Would delete {$count} consultation(s)",
                    'count' => $count,
                    'dryRun' => true,
                ]);
            } else {
                $count = $plugin->anfrageService->cleanupOldAnfragen($days);
                
                return $this->asJson([
                    'success' => true,
                    'message' => "Successfully deleted {$count} consultation(s)",
                    'count' => $count,
                    'dryRun' => false,
                ]);
            }
        } catch (\Exception $e) {
            Craft::error('Manual cleanup failed: ' . $e->getMessage(), __METHOD__);
            return $this->asErrorJson('Cleanup failed: ' . $e->getMessage());
        }
    }

    /**
     * Get plugin statistics for dashboard
     */
    public function actionStats(): Response
    {
        $this->requireAcceptsJson();
        
        $plugin = Plugin::getInstance();
        
        try {
            $stats = $plugin->installService->getStatistics();
            $config = $plugin->installService->getConfigurationStatus();
            
            return $this->asJson([
                'success' => true,
                'stats' => $stats,
                'config' => $config,
            ]);
        } catch (\Exception $e) {
            return $this->asErrorJson('Failed to get statistics: ' . $e->getMessage());
        }
    }

    /**
     * Test email configuration
     */
    public function actionTestEmail(): Response
    {
        $this->requirePostRequest();
        $this->requireAcceptsJson();

        $plugin = Plugin::getInstance();
        $settings = $plugin->getSettings();

        try {
            $subject = '[Test] Anonymous Portal Email Configuration';
            $body = "This is a test email from the Anonymous Portal plugin.\n\n";
            $body .= "If you receive this email, your email configuration is working correctly.\n";
            $body .= "Sent at: " . date('Y-m-d H:i:s');

            Craft::$app->mailer->compose()
                ->setFrom([$settings->fromEmail => 'Anonymous Portal'])
                ->setTo($settings->consultantEmail)
                ->setSubject($subject)
                ->setTextBody($body)
                ->send();

            return $this->asJson([
                'success' => true,
                'message' => 'Test email sent successfully to ' . $settings->consultantEmail,
            ]);
        } catch (\Exception $e) {
            return $this->asJson([
                'success' => false,
                'error' => 'Email test failed: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Export consultation data (for admin purposes)
     */
    public function actionExportData(): Response
    {
        $this->requirePostRequest();
        $this->requirePermission('utility:system-report');
        
        $request = Craft::$app->request;
        $format = $request->getBodyParam('format', 'json');
        $days = $request->getBodyParam('days', 30);

        try {
            $cutoffDate = new \DateTime("-{$days} days");
            $anfragen = \kldev\anonymousportal\elements\Anfrage::find()
                ->dateCreated('>= ' . $cutoffDate->format('Y-m-d H:i:s'))
                ->all();

            $data = [];
            foreach ($anfragen as $anfrage) {
                $messages = \kldev\anonymousportal\elements\Antwort::find()
                    ->anfrageId($anfrage->id)
                    ->orderBy('ts ASC')
                    ->all();

                $data[] = [
                    'id' => $anfrage->publicId,
                    'created' => $anfrage->dateCreated->format('c'),
                    'message_count' => count($messages),
                    'messages' => array_map(function($msg) {
                        return [
                            'sender' => $msg->sender,
                            'timestamp' => $msg->ts->format('c'),
                            'length' => strlen($msg->body),
                        ];
                    }, $messages),
                ];
            }

            if ($format === 'csv') {
                // Return CSV format
                $csv = "ID,Created,Message Count,Last Activity\n";
                foreach ($data as $row) {
                    $lastActivity = end($row['messages'])['timestamp'] ?? $row['created'];
                    $csv .= "{$row['id']},{$row['created']},{$row['message_count']},{$lastActivity}\n";
                }
                
                $response = Craft::$app->response;
                $response->headers->set('Content-Type', 'text/csv');
                $response->headers->set('Content-Disposition', 'attachment; filename="anonymous-portal-export.csv"');
                return $response->data = $csv;
            } else {
                // Return JSON format
                return $this->asJson([
                    'success' => true,
                    'data' => $data,
                    'total' => count($data),
                    'period_days' => $days,
                ]);
            }
        } catch (\Exception $e) {
            return $this->asErrorJson('Export failed: ' . $e->getMessage());
        }
    }
}
