<?php

namespace kldev\anonymousportal\controllers;

use Craft;
use craft\web\Controller;
use craft\web\Response;
use kldev\anonymousportal\elements\Anfrage;
use kldev\anonymousportal\Plugin;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;

/**
 * Thread controller
 */
class ThreadController extends Controller
{
    protected array|int|bool $allowAnonymous = true;

    /**
     * View thread messages
     */
    public function actionView(string $id): Response
    {
        $plugin = Plugin::getInstance();
        $threadId = $plugin->securityService->getThreadSession();

        if (!$threadId) {
            Craft::$app->session->setFlash('error', 'Please log in to view your consultation.');
            return $this->redirect('/anfrage');
        }

        // Validate access
        if (!$plugin->securityService->validateThreadAccess($threadId, $id)) {
            throw new NotFoundHttpException('Thread not found or access denied.');
        }

        $anfrage = Anfrage::find()->id($threadId)->publicId($id)->one();
        if (!$anfrage) {
            throw new NotFoundHttpException('Thread not found.');
        }

        $messages = $plugin->antwortService->getMessages($anfrage);

        return $this->renderTemplate('_craft-anonymous-portal/_frontend/thread/view', [
            'anfrage' => $anfrage,
            'messages' => $messages,
        ]);
    }

    /**
     * Send reply to thread
     */
    public function actionReply(string $id): Response
    {
        $this->requirePostRequest();
        
        $plugin = Plugin::getInstance();
        $request = Craft::$app->request;
        $threadId = $plugin->securityService->getThreadSession();

        if (!$threadId) {
            return $this->asErrorJson('Authentication required.');
        }

        // Validate access
        if (!$plugin->securityService->validateThreadAccess($threadId, $id)) {
            return $this->asErrorJson('Access denied.');
        }

        $anfrage = Anfrage::find()->id($threadId)->publicId($id)->one();
        if (!$anfrage) {
            return $this->asErrorJson('Thread not found.');
        }

        try {
            $body = $plugin->securityService->sanitizeInput(
                $request->getRequiredBodyParam('body')
            );

            if (strlen($body) < 1) {
                return $this->asErrorJson('Message cannot be empty.');
            }

            if (strlen($body) > 5000) {
                return $this->asErrorJson('Message is too long (max 5000 characters).');
            }

            // Create the reply
            $antwort = $plugin->antwortService->createMessage($anfrage, 'visitor', $body);

            $plugin->securityService->logSecurityEvent('message_sent', [
                'publicId' => $id,
                'messageLength' => strlen($body)
            ]);

            // Return the new message for HTMX
            if ($request->headers->get('HX-Request')) {
                return $this->renderTemplate('_craft-anonymous-portal/_frontend/thread/_message', [
                    'message' => $antwort,
                ]);
            }

            return $this->asJson(['success' => true]);

        } catch (BadRequestHttpException $e) {
            return $this->asErrorJson($e->getMessage());
        } catch (\Exception $e) {
            Craft::error('Failed to send reply: ' . $e->getMessage(), __METHOD__);
            return $this->asErrorJson('Failed to send message. Please try again.');
        }
    }

    /**
     * Get new messages (for polling/HTMX)
     */
    public function actionMessages(string $id): Response
    {
        $plugin = Plugin::getInstance();
        $request = Craft::$app->request;
        $threadId = $plugin->securityService->getThreadSession();

        if (!$threadId) {
            return $this->asErrorJson('Authentication required.');
        }

        // Validate access
        if (!$plugin->securityService->validateThreadAccess($threadId, $id)) {
            return $this->asErrorJson('Access denied.');
        }

        $anfrage = Anfrage::find()->id($threadId)->publicId($id)->one();
        if (!$anfrage) {
            return $this->asErrorJson('Thread not found.');
        }

        $since = $request->getParam('since');
        $query = $plugin->antwortService->getMessages($anfrage);
        
        if ($since) {
            $sinceDate = new \DateTime($since);
            $messages = array_filter($query, function($message) use ($sinceDate) {
                return $message->ts > $sinceDate;
            });
        } else {
            $messages = $query;
        }

        if ($request->headers->get('HX-Request')) {
            return $this->renderTemplate('_craft-anonymous-portal/_frontend/thread/_messages', [
                'messages' => $messages,
            ]);
        }

        return $this->asJson([
            'messages' => array_map(function($message) {
                return [
                    'id' => $message->id,
                    'sender' => $message->sender,
                    'body' => $message->body,
                    'ts' => $message->ts->format('c'),
                ];
            }, $messages)
        ]);
    }

    /**
     * Download thread as PDF (optional feature)
     */
    public function actionDownloadPdf(string $id): Response
    {
        $plugin = Plugin::getInstance();
        $threadId = $plugin->securityService->getThreadSession();

        if (!$threadId) {
            throw new NotFoundHttpException('Authentication required.');
        }

        // Validate access
        if (!$plugin->securityService->validateThreadAccess($threadId, $id)) {
            throw new NotFoundHttpException('Access denied.');
        }

        $anfrage = Anfrage::find()->id($threadId)->publicId($id)->one();
        if (!$anfrage) {
            throw new NotFoundHttpException('Thread not found.');
        }

        $messages = $plugin->antwortService->getMessages($anfrage);

        // Generate PDF content
        $html = $this->renderTemplate('_craft-anonymous-portal/_frontend/thread/_pdf', [
            'anfrage' => $anfrage,
            'messages' => $messages,
        ]);

        // For now, return HTML - in production, use a PDF library like TCPDF or wkhtmltopdf
        $response = Craft::$app->response;
        $response->headers->set('Content-Type', 'text/html');
        $response->headers->set('Content-Disposition', 'attachment; filename="consultation-' . $id . '.html"');
        
        return $response->data = $html;
    }
}
