{% import "_includes/forms" as forms %}

<div class="anonymous-portal-settings">
    
    <h2>{{ 'Matrix Configuration'|t('_craft-anonymous-portal') }}</h2>
    
    {{ forms.textField({
        label: 'Matrix Homeserver URL'|t('_craft-anonymous-portal'),
        instructions: 'The URL of your Matrix homeserver (e.g., https://matrix.example.com)'|t('_craft-anonymous-portal'),
        id: 'matrixHomeserver',
        name: 'matrixHomeserver',
        value: settings.matrixHomeserver,
        required: true,
        placeholder: 'https://matrix.example.com'
    }) }}

    {{ forms.passwordField({
        label: 'Matrix Access Token'|t('_craft-anonymous-portal'),
        instructions: 'Access token for the Matrix bot user'|t('_craft-anonymous-portal'),
        id: 'matrixAccessToken',
        name: 'matrixAccessToken',
        value: settings.matrixAccessToken,
        required: true
    }) }}

    <hr>

    <h2>{{ 'Postmoogle Configuration'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.textField({
        label: 'Postmoogle API URL'|t('_craft-anonymous-portal'),
        instructions: 'The URL of your Postmoogle API endpoint'|t('_craft-anonymous-portal'),
        id: 'postmoogleApiUrl',
        name: 'postmoogleApiUrl',
        value: settings.postmoogleApiUrl,
        required: true,
        placeholder: 'https://postmoogle.example.com/api'
    }) }}

    {{ forms.passwordField({
        label: 'Postmoogle API Key'|t('_craft-anonymous-portal'),
        instructions: 'API key for Postmoogle authentication'|t('_craft-anonymous-portal'),
        id: 'postmoogleApiKey',
        name: 'postmoogleApiKey',
        value: settings.postmoogleApiKey,
        required: true
    }) }}

    <hr>

    <h2>{{ 'Email Configuration'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.textField({
        label: 'Consultant Email'|t('_craft-anonymous-portal'),
        instructions: 'Email address where new consultation notifications are sent'|t('_craft-anonymous-portal'),
        id: 'consultantEmail',
        name: 'consultantEmail',
        value: settings.consultantEmail,
        type: 'email',
        required: true
    }) }}

    {{ forms.textField({
        label: 'From Email'|t('_craft-anonymous-portal'),
        instructions: 'Email address used as sender for notifications'|t('_craft-anonymous-portal'),
        id: 'fromEmail',
        name: 'fromEmail',
        value: settings.fromEmail,
        type: 'email',
        required: true
    }) }}

    <hr>

    <h2>{{ 'Security Settings'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.numberField({
        label: 'Session Timeout (Minutes)'|t('_craft-anonymous-portal'),
        instructions: 'How long user sessions remain active'|t('_craft-anonymous-portal'),
        id: 'sessionTimeoutMinutes',
        name: 'sessionTimeoutMinutes',
        value: settings.sessionTimeoutMinutes,
        min: 1,
        max: 1440
    }) }}

    {{ forms.numberField({
        label: 'Rate Limit Attempts'|t('_craft-anonymous-portal'),
        instructions: 'Maximum failed login attempts before rate limiting'|t('_craft-anonymous-portal'),
        id: 'rateLimitAttempts',
        name: 'rateLimitAttempts',
        value: settings.rateLimitAttempts,
        min: 1,
        max: 100
    }) }}

    {{ forms.numberField({
        label: 'Rate Limit Window (Minutes)'|t('_craft-anonymous-portal'),
        instructions: 'Time window for rate limiting'|t('_craft-anonymous-portal'),
        id: 'rateLimitWindowMinutes',
        name: 'rateLimitWindowMinutes',
        value: settings.rateLimitWindowMinutes,
        min: 1,
        max: 1440
    }) }}

    <hr>

    <h2>{{ 'Cleanup Settings'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.numberField({
        label: 'Cleanup Days'|t('_craft-anonymous-portal'),
        instructions: 'Number of days after which consultations are automatically deleted'|t('_craft-anonymous-portal'),
        id: 'cleanupDays',
        name: 'cleanupDays',
        value: settings.cleanupDays,
        min: 1,
        max: 365
    }) }}

    <hr>

    <h2>{{ 'Connection Tests'|t('_craft-anonymous-portal') }}</h2>
    
    <div class="connection-tests">
        <button type="button" class="btn" id="test-matrix">{{ 'Test Matrix Connection'|t('_craft-anonymous-portal') }}</button>
        <button type="button" class="btn" id="test-postmoogle">{{ 'Test Postmoogle Connection'|t('_craft-anonymous-portal') }}</button>
        <div id="test-results" class="mt-3"></div>
    </div>

</div>

<script>
document.getElementById('test-matrix').addEventListener('click', function() {
    testConnection('matrix');
});

document.getElementById('test-postmoogle').addEventListener('click', function() {
    testConnection('postmoogle');
});

function testConnection(service) {
    const button = document.getElementById('test-' + service);
    const results = document.getElementById('test-results');

    button.disabled = true;
    button.textContent = 'Testing...';

    // Use admin endpoint for better error handling
    fetch('/admin/anonymous-portal/test-connections', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '{{ craft.app.request.csrfToken }}'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const connection = data.connections[service];
                const status = connection.status === 'success' ? 'success' : 'error';
                const message = connection.status === 'success'
                    ? service.charAt(0).toUpperCase() + service.slice(1) + ' connection successful!'
                    : 'Connection failed: ' + (connection.error || 'Unknown error');

                results.innerHTML = `<div class="alert alert-${status}">${message}</div>`;
            } else {
                results.innerHTML = `<div class="alert alert-error">Test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            results.innerHTML = `<div class="alert alert-error">Connection test failed: ${error.message}</div>`;
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = 'Test ' + service.charAt(0).toUpperCase() + service.slice(1) + ' Connection';
        });
}
</script>

<style>
.anonymous-portal-settings h2 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.anonymous-portal-settings hr {
    margin: 2rem 0;
    border-color: #e3e5e8;
}

.connection-tests {
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 4px;
}

.connection-tests .btn {
    margin-right: 1rem;
}

.alert {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-top: 1rem;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}
</style>
