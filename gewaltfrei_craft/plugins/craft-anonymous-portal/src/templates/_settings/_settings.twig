{% import "_includes/forms" as forms %}

<div class="anonymous-portal-settings">
    


    <h2>{{ 'Email Configuration'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.textField({
        label: 'Consultant Email'|t('_craft-anonymous-portal'),
        instructions: 'Email address where new consultation notifications are sent'|t('_craft-anonymous-portal'),
        id: 'consultantEmail',
        name: 'consultantEmail',
        value: settings.consultantEmail,
        type: 'email',
        required: true
    }) }}

    {{ forms.textField({
        label: 'From Email'|t('_craft-anonymous-portal'),
        instructions: 'Email address used as sender for notifications'|t('_craft-anonymous-portal'),
        id: 'fromEmail',
        name: 'fromEmail',
        value: settings.fromEmail,
        type: 'email',
        required: true
    }) }}

    {{ forms.textField({
        label: 'Reply-To Prefix'|t('_craft-anonymous-portal'),
        instructions: 'Prefix for reply-to email addresses (e.g., "beratung-" creates <EMAIL>)'|t('_craft-anonymous-portal'),
        id: 'replyToPrefix',
        name: 'replyToPrefix',
        value: settings.replyToPrefix ?: 'beratung-',
        placeholder: 'beratung-'
    }) }}

    {{ forms.textField({
        label: 'Email Subject Prefix'|t('_craft-anonymous-portal'),
        instructions: 'Prefix for email subjects to identify consultation emails'|t('_craft-anonymous-portal'),
        id: 'emailSubjectPrefix',
        name: 'emailSubjectPrefix',
        value: settings.emailSubjectPrefix ?: '[Anonyme Beratung]',
        placeholder: '[Anonyme Beratung]'
    }) }}

    <hr>

    <h2>{{ 'Security Settings'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.textField({
        label: 'Session Timeout (Minutes)'|t('_craft-anonymous-portal'),
        instructions: 'How long user sessions remain active'|t('_craft-anonymous-portal'),
        id: 'sessionTimeoutMinutes',
        name: 'sessionTimeoutMinutes',
        value: settings.sessionTimeoutMinutes,
        type: 'number',
        min: 1,
        max: 1440
    }) }}

    {{ forms.textField({
        label: 'Rate Limit Attempts'|t('_craft-anonymous-portal'),
        instructions: 'Maximum failed login attempts before rate limiting'|t('_craft-anonymous-portal'),
        id: 'rateLimitAttempts',
        name: 'rateLimitAttempts',
        value: settings.rateLimitAttempts,
        type: 'number',
        min: 1,
        max: 100
    }) }}

    {{ forms.textField({
        label: 'Rate Limit Window (Minutes)'|t('_craft-anonymous-portal'),
        instructions: 'Time window for rate limiting'|t('_craft-anonymous-portal'),
        id: 'rateLimitWindowMinutes',
        name: 'rateLimitWindowMinutes',
        value: settings.rateLimitWindowMinutes,
        type: 'number',
        min: 1,
        max: 1440
    }) }}

    <hr>

    <h2>{{ 'Cleanup Settings'|t('_craft-anonymous-portal') }}</h2>

    {{ forms.textField({
        label: 'Cleanup Days'|t('_craft-anonymous-portal'),
        instructions: 'Number of days after which consultations are automatically deleted'|t('_craft-anonymous-portal'),
        id: 'cleanupDays',
        name: 'cleanupDays',
        value: settings.cleanupDays,
        type: 'number',
        min: 1,
        max: 365
    }) }}

    <hr>

    <h2>{{ 'Email Test'|t('_craft-anonymous-portal') }}</h2>

    <div class="connection-tests">
        <button type="button" class="btn" id="test-email">{{ 'Send Test Email'|t('_craft-anonymous-portal') }}</button>
        <div id="test-results" class="mt-3"></div>
    </div>

</div>

<script>
document.getElementById('test-email').addEventListener('click', function() {
    testEmail();
});

function testEmail() {
    const button = document.getElementById('test-email');
    const results = document.getElementById('test-results');

    button.disabled = true;
    button.textContent = 'Sending...';

    fetch('/admin/anonymous-portal/test-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '{{ craft.app.request.csrfToken }}'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                results.innerHTML = `<div class="alert alert-success">Test email sent successfully!</div>`;
            } else {
                results.innerHTML = `<div class="alert alert-error">Email test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            results.innerHTML = `<div class="alert alert-error">Email test failed: ${error.message}</div>`;
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = 'Send Test Email';
        });
}
</script>

<style>
.anonymous-portal-settings h2 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

.anonymous-portal-settings hr {
    margin: 2rem 0;
    border-color: #e3e5e8;
}

.connection-tests {
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 4px;
}

.connection-tests .btn {
    margin-right: 1rem;
}

.alert {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-top: 1rem;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}
</style>
