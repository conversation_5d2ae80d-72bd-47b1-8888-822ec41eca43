<div class="anonymous-portal-widget">
    
    <!-- Configuration Status -->
    <div class="status-section">
        <h3>{{ 'Configuration'|t('_craft-anonymous-portal') }}</h3>
        <div class="status-grid">
            <div class="status-item">
                <span class="status-label">{{ 'Email'|t('_craft-anonymous-portal') }}</span>
                <span class="status-indicator {{ config.email_configured ? 'success' : 'error' }}">
                    {{ config.email_configured ? 'Configured'|t('_craft-anonymous-portal') : 'Not Configured'|t('_craft-anonymous-portal') }}
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">{{ 'Plugin'|t('_craft-anonymous-portal') }}</span>
                <span class="status-indicator {{ config.fully_configured ? 'success' : 'error' }}">
                    {{ config.fully_configured ? 'Ready'|t('_craft-anonymous-portal') : 'Needs Configuration'|t('_craft-anonymous-portal') }}
                </span>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="status-section">
        <h3>{{ 'Statistics'|t('_craft-anonymous-portal') }}</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{{ stats.total_consultations }}</div>
                <div class="stat-label">{{ 'Total Consultations'|t('_craft-anonymous-portal') }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ stats.total_messages }}</div>
                <div class="stat-label">{{ 'Total Messages'|t('_craft-anonymous-portal') }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ stats.recent_consultations }}</div>
                <div class="stat-label">{{ 'Recent (7 days)'|t('_craft-anonymous-portal') }}</div>
            </div>
            {% if stats.old_consultations > 0 %}
            <div class="stat-item warning">
                <div class="stat-number">{{ stats.old_consultations }}</div>
                <div class="stat-label">{{ 'Ready for Cleanup'|t('_craft-anonymous-portal') }}</div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="status-section">
        <div class="actions">
            <a href="{{ cpUrl('settings/plugins/_craft-anonymous-portal') }}" class="btn small">
                {{ 'Settings'|t('_craft-anonymous-portal') }}
            </a>
            {% if stats.old_consultations > 0 %}
            <button type="button" class="btn small secondary" onclick="runCleanup()">
                {{ 'Run Cleanup'|t('_craft-anonymous-portal') }}
            </button>
            {% endif %}
        </div>
    </div>

</div>

<script>
function runCleanup() {
    if (confirm('{{ "Are you sure you want to run the cleanup process?"|t("_craft-anonymous-portal") }}')) {
        // This would need to be implemented as an AJAX endpoint
        alert('{{ "Cleanup functionality would be implemented here"|t("_craft-anonymous-portal") }}');
    }
}
</script>

<style>
.anonymous-portal-widget {
    padding: 0;
}

.status-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e3e5e8;
}

.status-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.status-section h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #3f4d5a;
}

.status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.status-label {
    font-size: 13px;
    color: #596673;
}

.status-indicator {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 3px;
    font-weight: 500;
}

.status-indicator.success {
    background: #d4edda;
    color: #155724;
}

.status-indicator.error {
    background: #f8d7da;
    color: #721c24;
}

.status-indicator small {
    display: block;
    font-weight: normal;
    opacity: 0.8;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.stat-item.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #3f4d5a;
    line-height: 1;
}

.stat-label {
    font-size: 11px;
    color: #596673;
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.actions {
    display: flex;
    gap: 10px;
}

.actions .btn {
    flex: 1;
    text-align: center;
}
</style>
