{% extends "_layouts/_page/_page.twig" %}

{% block content %}
<div class="anonymous-portal">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                
                {% if craft.app.session.hasFlash('error') %}
                    <div class="alert alert-danger">
                        {{ craft.app.session.getFlash('error') }}
                    </div>
                {% endif %}

                {% if craft.app.session.hasFlash('notice') %}
                    <div class="alert alert-info">
                        {{ craft.app.session.getFlash('notice') }}
                    </div>
                {% endif %}

                <div class="card">
                    <div class="card-header">
                        <h2>{{ 'Anonymous Consultation Portal'|t('_craft-anonymous-portal') }}</h2>
                    </div>
                    <div class="card-body">
                        
                        <!-- Login Form -->
                        <div id="login-form" class="mb-4">
                            <h3>{{ 'Access Your Consultation'|t('_craft-anonymous-portal') }}</h3>
                            <p class="text-muted">{{ 'Enter your ID and secret to access your consultation thread.'|t('_craft-anonymous-portal') }}</p>
                            
                            <form method="post" action="/anfrage/login">
                                {{ csrfInput() }}
                                
                                <div class="mb-3">
                                    <label for="id" class="form-label">{{ 'Your ID'|t('_craft-anonymous-portal') }}</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="id" 
                                           name="id" 
                                           placeholder="ab12cd34"
                                           pattern="[a-f0-9]{8}"
                                           maxlength="8"
                                           required>
                                    <div class="form-text">{{ '8 character hexadecimal ID'|t('_craft-anonymous-portal') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="secret" class="form-label">{{ 'Your Secret'|t('_craft-anonymous-portal') }}</label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="secret" 
                                           name="secret" 
                                           placeholder="Your secret key"
                                           required>
                                    <div class="form-text">{{ 'The secret key you received when creating your consultation'|t('_craft-anonymous-portal') }}</div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">{{ 'Access Consultation'|t('_craft-anonymous-portal') }}</button>
                            </form>
                        </div>

                        <hr>

                        <!-- New Consultation Form -->
                        <div id="new-consultation">
                            <h3>{{ 'Start New Consultation'|t('_craft-anonymous-portal') }}</h3>
                            <p class="text-muted">{{ 'Begin a new anonymous consultation. You will receive an ID and secret to access your thread.'|t('_craft-anonymous-portal') }}</p>
                            
                            <form method="post" action="/anfrage/send">
                                {{ csrfInput() }}
                                
                                <div class="mb-3">
                                    <label for="message" class="form-label">{{ 'Your Message'|t('_craft-anonymous-portal') }} <span class="text-danger">*</span></label>
                                    <textarea class="form-control" 
                                              id="message" 
                                              name="message" 
                                              rows="6" 
                                              placeholder="Describe your situation or question..."
                                              minlength="10"
                                              maxlength="5000"
                                              required></textarea>
                                    <div class="form-text">{{ 'Minimum 10 characters, maximum 5000 characters'|t('_craft-anonymous-portal') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">{{ 'Email (Optional)'|t('_craft-anonymous-portal') }}</label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           placeholder="<EMAIL>">
                                    <div class="form-text">{{ 'Optional: Receive notifications when consultants reply'|t('_craft-anonymous-portal') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacy" required>
                                        <label class="form-check-label" for="privacy">
                                            {{ 'I understand that this is an anonymous consultation service and I agree to the terms of use.'|t('_craft-anonymous-portal') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-success">{{ 'Start Consultation'|t('_craft-anonymous-portal') }}</button>
                            </form>
                        </div>

                    </div>
                </div>

                <!-- Security Notice -->
                <div class="alert alert-info mt-4">
                    <h5>{{ 'Security & Privacy'|t('_craft-anonymous-portal') }}</h5>
                    <ul class="mb-0">
                        <li>{{ 'Your consultation is completely anonymous'|t('_craft-anonymous-portal') }}</li>
                        <li>{{ 'Keep your ID and secret safe - they cannot be recovered'|t('_craft-anonymous-portal') }}</li>
                        <li>{{ 'Consultations are automatically deleted after 90 days'|t('_craft-anonymous-portal') }}</li>
                        <li>{{ 'Use the panic button (Escape key) to quickly close this page'|t('_craft-anonymous-portal') }}</li>
                    </ul>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
// Panic button functionality
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        window.location.href = 'about:blank';
    }
});

// Form validation
document.getElementById('id').addEventListener('input', function(e) {
    e.target.value = e.target.value.toLowerCase().replace(/[^a-f0-9]/g, '');
});
</script>

<style>
.anonymous-portal {
    padding: 2rem 0;
}

.anonymous-portal .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.anonymous-portal .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.anonymous-portal .btn {
    padding: 0.5rem 1.5rem;
}

.anonymous-portal hr {
    margin: 2rem 0;
}
</style>
{% endblock %}
