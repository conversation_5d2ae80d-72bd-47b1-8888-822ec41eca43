{% extends "_layouts/_page/_page.twig" %}

{% block content %}
<div class="anonymous-portal">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h2 class="mb-0">{{ 'Consultation Created Successfully'|t('_craft-anonymous-portal') }}</h2>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-warning">
                            <strong>{{ 'Important: Save These Credentials!'|t('_craft-anonymous-portal') }}</strong><br>
                            {{ 'This information will only be shown once. Without both your ID and secret, you cannot access your consultation.'|t('_craft-anonymous-portal') }}
                        </div>

                        <div class="credentials-display">
                            <div class="credential-item">
                                <label>{{ 'Your ID:'|t('_craft-anonymous-portal') }}</label>
                                <div class="credential-value">
                                    <code id="public-id">{{ publicId }}</code>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('public-id')">
                                        {{ 'Copy'|t('_craft-anonymous-portal') }}
                                    </button>
                                </div>
                            </div>
                            
                            <div class="credential-item">
                                <label>{{ 'Your Secret:'|t('_craft-anonymous-portal') }}</label>
                                <div class="credential-value">
                                    <code id="secret-key">{{ secret }}</code>
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('secret-key')">
                                        {{ 'Copy'|t('_craft-anonymous-portal') }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h5>{{ 'What happens next?'|t('_craft-anonymous-portal') }}</h5>
                            <ol>
                                <li>{{ 'Your message has been sent to our consultation team'|t('_craft-anonymous-portal') }}</li>
                                <li>{{ 'You will receive a response via email (if provided) or check back here'|t('_craft-anonymous-portal') }}</li>
                                <li>{{ 'Use your ID and secret to access your consultation thread'|t('_craft-anonymous-portal') }}</li>
                            </ol>
                        </div>

                        <div class="mt-4">
                            <a href="/anfrage/{{ publicId }}" class="btn btn-primary">
                                {{ 'Access Your Consultation'|t('_craft-anonymous-portal') }}
                            </a>
                            <button type="button" class="btn btn-secondary ms-2" onclick="downloadCredentials()">
                                {{ 'Download Credentials'|t('_craft-anonymous-portal') }}
                            </button>
                        </div>

                    </div>
                </div>

                <!-- Security Reminder -->
                <div class="alert alert-info mt-4">
                    <h5>{{ 'Security Reminders'|t('_craft-anonymous-portal') }}</h5>
                    <ul class="mb-0">
                        <li>{{ 'Store your credentials in a safe place'|t('_craft-anonymous-portal') }}</li>
                        <li>{{ 'Do not share your credentials with anyone'|t('_craft-anonymous-portal') }}</li>
                        <li>{{ 'Your consultation will be automatically deleted after 90 days'|t('_craft-anonymous-portal') }}</li>
                        <li>{{ 'Use private/incognito browsing for additional privacy'|t('_craft-anonymous-portal') }}</li>
                    </ul>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        // Show success feedback
        const button = element.nextElementSibling;
        const originalText = button.textContent;
        button.textContent = '{{ "Copied!"|t("_craft-anonymous-portal") }}';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        setTimeout(function() {
            button.textContent = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

function downloadCredentials() {
    const content = `Anonymous Consultation Credentials

Your ID: {{ publicId }}
Your Secret: {{ secret }}

Access URL: {{ craft.app.sites.currentSite.baseUrl }}/anfrage/{{ publicId }}

IMPORTANT:
- Keep these credentials safe
- Do not share with anyone
- This consultation will be deleted after 90 days
- Generated on: ${new Date().toLocaleString()}
`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'consultation-credentials-{{ publicId }}.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Panic button functionality
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        window.location.href = 'about:blank';
    }
});
</script>

<style>
.anonymous-portal {
    padding: 2rem 0;
}

.credentials-display {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.375rem;
    margin: 1rem 0;
}

.credential-item {
    margin-bottom: 1rem;
}

.credential-item:last-child {
    margin-bottom: 0;
}

.credential-item label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.credential-value {
    display: flex;
    align-items: center;
}

.credential-value code {
    background: #e9ecef;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 1.1rem;
    font-family: 'Courier New', monospace;
    flex: 1;
    margin-right: 0.5rem;
}

.anonymous-portal .btn {
    padding: 0.5rem 1.5rem;
}
</style>
{% endblock %}
