<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ 'Consultation Thread'|t('_craft-anonymous-portal') }} - {{ anfrage.publicId }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
        }
        
        .header .meta {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .message {
            margin-bottom: 25px;
            padding: 15px;
            border-left: 4px solid #ddd;
        }
        
        .message.visitor {
            border-left-color: #2196F3;
            background: #f8f9ff;
        }
        
        .message.consultant {
            border-left-color: #9C27B0;
            background: #faf8ff;
        }
        
        .message-header {
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
        }
        
        .message-time {
            float: right;
            font-size: 12px;
            color: #888;
        }
        
        .message-body {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ 'Anonymous Consultation Thread'|t('_craft-anonymous-portal') }}</h1>
        <div class="meta">
            <strong>{{ 'Thread ID:'|t('_craft-anonymous-portal') }}</strong> {{ anfrage.publicId }}<br>
            <strong>{{ 'Created:'|t('_craft-anonymous-portal') }}</strong> {{ anfrage.dateCreated|datetime('medium') }}<br>
            <strong>{{ 'Exported:'|t('_craft-anonymous-portal') }}</strong> {{ 'now'|date('medium') }}<br>
            <strong>{{ 'Total Messages:'|t('_craft-anonymous-portal') }}</strong> {{ messages|length }}
        </div>
    </div>

    <div class="messages">
        {% for message in messages %}
            <div class="message {{ message.sender }}">
                <div class="message-header">
                    {% if message.sender == 'visitor' %}
                        {{ 'You'|t('_craft-anonymous-portal') }}
                    {% else %}
                        {{ 'Consultant'|t('_craft-anonymous-portal') }}
                    {% endif %}
                    <span class="message-time">{{ message.ts|datetime('medium') }}</span>
                </div>
                <div class="message-body">{{ message.body|e }}</div>
            </div>
        {% endfor %}
    </div>

    <div class="footer">
        <p>{{ 'This is an export of your anonymous consultation thread.'|t('_craft-anonymous-portal') }}</p>
        <p>{{ 'Generated by Anonymous Portal'|t('_craft-anonymous-portal') }} - {{ craft.app.sites.currentSite.name }}</p>
    </div>
</body>
</html>
