<div class="message {{ message.sender == 'visitor' ? 'message-visitor' : 'message-consultant' }}">
    <div class="message-header">
        <span class="message-sender">
            {% if message.sender == 'visitor' %}
                {{ 'You'|t('_craft-anonymous-portal') }}
            {% else %}
                {{ 'Consultant'|t('_craft-anonymous-portal') }}
            {% endif %}
        </span>
        <span class="message-time">{{ message.ts|datetime('short') }}</span>
    </div>
    <div class="message-body">
        {{ message.body|e|nl2br }}
    </div>
</div>

<style>
.message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    max-width: 80%;
}

.message-visitor {
    background: #e3f2fd;
    margin-left: auto;
    border: 1px solid #bbdefb;
}

.message-consultant {
    background: #f3e5f5;
    margin-right: auto;
    border: 1px solid #e1bee7;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.message-sender {
    font-weight: 600;
    color: #495057;
}

.message-time {
    color: #6c757d;
    font-size: 0.8rem;
}

.message-body {
    color: #212529;
    line-height: 1.5;
    word-wrap: break-word;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .message {
        max-width: 95%;
    }
    
    .message-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .message-time {
        margin-top: 0.25rem;
    }
}
</style>
