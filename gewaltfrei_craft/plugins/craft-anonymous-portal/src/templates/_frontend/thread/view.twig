{% extends "_layouts/_page/_page.twig" %}

{% block content %}
<div class="anonymous-portal thread-view">
    <div class="container">
        <div class="row">
            <div class="col-12">
                
                <!-- Header -->
                <div class="thread-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>{{ 'Consultation Thread'|t('_craft-anonymous-portal') }}</h2>
                            <p class="text-muted mb-0">
                                {{ 'ID:'|t('_craft-anonymous-portal') }} <code>{{ anfrage.publicId }}</code> | 
                                {{ 'Created:'|t('_craft-anonymous-portal') }} {{ anfrage.dateCreated|datetime('short') }}
                            </p>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="refreshMessages()">
                                {{ 'Refresh'|t('_craft-anonymous-portal') }}
                            </button>
                            <a href="/anfrage/{{ anfrage.publicId }}/download" class="btn btn-outline-primary btn-sm">
                                {{ 'Download'|t('_craft-anonymous-portal') }}
                            </a>
                            <a href="/anfrage/logout" class="btn btn-outline-danger btn-sm">
                                {{ 'Logout'|t('_craft-anonymous-portal') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Messages Container -->
                <div class="messages-container" id="messages-container">
                    <div id="messages">
                        {% include '_craft-anonymous-portal/_frontend/thread/_messages.twig' with { messages: messages } %}
                    </div>
                </div>

                <!-- Reply Form -->
                <div class="reply-form">
                    <form hx-post="/anfrage/{{ anfrage.publicId }}/reply"
                          hx-target="#messages"
                          hx-swap="beforeend"
                          hx-on::after-request="clearReplyForm()">
                        {{ csrfInput() }}
                        
                        <div class="mb-3">
                            <label for="reply-body" class="form-label">{{ 'Your Reply'|t('_craft-anonymous-portal') }}</label>
                            <textarea class="form-control" 
                                      id="reply-body" 
                                      name="body" 
                                      rows="4" 
                                      placeholder="Type your message here..."
                                      maxlength="5000"
                                      required></textarea>
                            <div class="form-text">{{ 'Maximum 5000 characters'|t('_craft-anonymous-portal') }}</div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-text">
                                <span id="char-count">0</span> / 5000 {{ 'characters'|t('_craft-anonymous-portal') }}
                            </div>
                            <button type="submit" class="btn btn-primary">
                                {{ 'Send Reply'|t('_craft-anonymous-portal') }}
                            </button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>

<!-- Panic Button Notice -->
<div class="panic-notice">
    {{ 'Press ESC to quickly close this page'|t('_craft-anonymous-portal') }}
</div>

<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
// Auto-refresh messages every 5 seconds
setInterval(function() {
    htmx.ajax('GET', '/anfrage/{{ anfrage.publicId }}/messages', '#messages');
}, 5000);

// Character counter
document.getElementById('reply-body').addEventListener('input', function(e) {
    const count = e.target.value.length;
    document.getElementById('char-count').textContent = count;
    
    if (count > 4500) {
        document.getElementById('char-count').style.color = '#dc3545';
    } else {
        document.getElementById('char-count').style.color = '#6c757d';
    }
});

// Clear form after successful submission
function clearReplyForm() {
    document.getElementById('reply-body').value = '';
    document.getElementById('char-count').textContent = '0';
    document.getElementById('char-count').style.color = '#6c757d';
    
    // Scroll to bottom of messages
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

// Refresh messages manually
function refreshMessages() {
    htmx.ajax('GET', '/anfrage/{{ anfrage.publicId }}/messages', '#messages');
}

// Panic button functionality
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        window.location.href = 'about:blank';
    }
});

// Auto-scroll to bottom on load
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
});
</script>

<style>
.anonymous-portal.thread-view {
    padding: 1rem 0;
    min-height: 100vh;
}

.thread-header {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.messages-container {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    height: 60vh;
    overflow-y: auto;
    padding: 1rem;
    margin-bottom: 1rem;
}

.reply-form {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
}

.panic-notice {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    z-index: 1000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .thread-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .thread-header .d-flex > div:last-child {
        margin-top: 1rem;
    }
    
    .messages-container {
        height: 50vh;
    }
}
</style>
{% endblock %}
