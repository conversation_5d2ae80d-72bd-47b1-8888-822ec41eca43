<?php

namespace kldev\anonymousportal\migrations;

use Craft;
use craft\db\Migration;
use craft\helpers\MigrationHelper;

/**
 * Install migration.
 */
class Install extends Migration
{
    public function safeUp(): bool
    {
        $this->createTables();
        $this->createIndexes();
        $this->addForeignKeys();

        return true;
    }

    public function safeDown(): bool
    {
        $this->dropTableIfExists('{{%anonymousportal_antworten}}');
        $this->dropTableIfExists('{{%anonymousportal_anfragen}}');

        return true;
    }

    protected function createTables(): void
    {
        // Create anfragen table
        $this->createTable('{{%anonymousportal_anfragen}}', [
            'id' => $this->primaryKey(),
            'publicId' => $this->string(8)->notNull(),
            'secretHash' => $this->text()->notNull(),
            'roomId' => $this->string()->notNull(),
            'mailbox' => $this->string()->notNull(),
            'encryptedEmail' => $this->text(),
            'dateCreated' => $this->dateTime()->notNull(),
            'dateUpdated' => $this->dateTime()->notNull(),
            'uid' => $this->uid(),
        ]);

        // Create antworten table
        $this->createTable('{{%anonymousportal_antworten}}', [
            'id' => $this->primaryKey(),
            'anfrageId' => $this->integer()->notNull(),
            'sender' => $this->enum('sender', ['visitor', 'consultant'])->notNull(),
            'body' => $this->text()->notNull(),
            'ts' => $this->dateTime()->notNull(),
            'dateCreated' => $this->dateTime()->notNull(),
            'dateUpdated' => $this->dateTime()->notNull(),
            'uid' => $this->uid(),
        ]);
    }

    protected function createIndexes(): void
    {
        // Anfragen indexes
        $this->createIndex(null, '{{%anonymousportal_anfragen}}', 'publicId', true);
        $this->createIndex(null, '{{%anonymousportal_anfragen}}', 'roomId');
        $this->createIndex(null, '{{%anonymousportal_anfragen}}', 'mailbox');
        $this->createIndex(null, '{{%anonymousportal_anfragen}}', 'dateCreated');

        // Antworten indexes
        $this->createIndex(null, '{{%anonymousportal_antworten}}', 'anfrageId');
        $this->createIndex(null, '{{%anonymousportal_antworten}}', 'sender');
        $this->createIndex(null, '{{%anonymousportal_antworten}}', 'ts');
    }

    protected function addForeignKeys(): void
    {
        // Anfragen foreign keys
        $this->addForeignKey(null, '{{%anonymousportal_anfragen}}', 'id', '{{%elements}}', 'id', 'CASCADE');

        // Antworten foreign keys
        $this->addForeignKey(null, '{{%anonymousportal_antworten}}', 'id', '{{%elements}}', 'id', 'CASCADE');
        $this->addForeignKey(null, '{{%anonymousportal_antworten}}', 'anfrageId', '{{%anonymousportal_anfragen}}', 'id', 'CASCADE');
    }
}
