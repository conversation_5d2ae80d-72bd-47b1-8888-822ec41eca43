<?php

namespace kldev\anonymousportal\models;

use craft\base\Model;

/**
 * Anonymous Portal settings
 */
class Settings extends Model
{
    public string $matrixHomeserver = '';
    public string $matrixAccessToken = '';
    public string $postmoogleApiUrl = '';
    public string $postmoogleApiKey = '';
    public string $consultantEmail = '';
    public string $fromEmail = '';
    public int $sessionTimeoutMinutes = 30;
    public int $cleanupDays = 90;
    public int $rateLimitAttempts = 5;
    public int $rateLimitWindowMinutes = 10;

    public function defineRules(): array
    {
        return [
            [['matrixHomeserver', 'matrixAccessToken', 'postmoogleApiUrl', 'postmoogleApiKey', 'consultantEmail', 'fromEmail'], 'required'],
            [['matrixHomeserver', 'matrixAccessToken', 'postmoogleApiUrl', 'postmoogleApiKey', 'consultantEmail', 'fromEmail'], 'string'],
            [['sessionTimeoutMinutes', 'cleanupDays', 'rateLimitAttempts', 'rateLimitWindowMinutes'], 'integer', 'min' => 1],
            [['consultantEmail', 'fromEmail'], 'email'],
        ];
    }
}
