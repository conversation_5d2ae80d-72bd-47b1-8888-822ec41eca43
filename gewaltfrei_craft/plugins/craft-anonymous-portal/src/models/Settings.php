<?php

namespace kldev\anonymousportal\models;

use craft\base\Model;

/**
 * Anonymous Portal settings
 */
class Settings extends Model
{
    public string $consultantEmail = '';
    public string $fromEmail = '';
    public string $replyToPrefix = 'beratung-';
    public string $emailSubjectPrefix = '[Anonyme Beratung]';
    public int $sessionTimeoutMinutes = 30;
    public int $cleanupDays = 90;
    public int $rateLimitAttempts = 5;
    public int $rateLimitWindowMinutes = 10;

    public function defineRules(): array
    {
        return [
            [['consultantEmail', 'fromEmail'], 'required'],
            [['consultantEmail', 'fromEmail', 'replyToPrefix', 'emailSubjectPrefix'], 'string'],
            [['sessionTimeoutMinutes', 'cleanupDays', 'rateLimitAttempts', 'rateLimitWindowMinutes'], 'integer', 'min' => 1],
            [['consultantEmail', 'fromEmail'], 'email'],
        ];
    }
}
