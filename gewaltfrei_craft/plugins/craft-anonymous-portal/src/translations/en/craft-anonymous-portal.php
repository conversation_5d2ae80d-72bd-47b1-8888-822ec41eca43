<?php

return [
    // General
    'Anonymous Portal' => 'Anonymous Portal',
    'Anonymous Consultation Portal' => 'Anonymous Consultation Portal',
    'Anfrage' => 'Request',
    'anfrage' => 'request',
    'Anfragen' => 'Requests',
    'anfragen' => 'requests',
    'Antwort' => 'Reply',
    'antwort' => 'reply',
    'Antworten' => 'Replies',
    'antworten' => 'replies',

    // Auth page
    'Access Your Consultation' => 'Access Your Consultation',
    'Enter your ID and secret to access your consultation thread.' => 'Enter your ID and secret to access your consultation thread.',
    'Your ID' => 'Your ID',
    'Your Secret' => 'Your Secret',
    '8 character hexadecimal ID' => '8 character hexadecimal ID',
    'The secret key you received when creating your consultation' => 'The secret key you received when creating your consultation',
    'Access Consultation' => 'Access Consultation',
    'Start New Consultation' => 'Start New Consultation',
    'Begin a new anonymous consultation. You will receive an ID and secret to access your thread.' => 'Begin a new anonymous consultation. You will receive an ID and secret to access your thread.',
    'Your Message' => 'Your Message',
    'Describe your situation or question...' => 'Describe your situation or question...',
    'Minimum 10 characters, maximum 5000 characters' => 'Minimum 10 characters, maximum 5000 characters',
    'Email (Optional)' => 'Email (Optional)',
    'Optional: Receive notifications when consultants reply' => 'Optional: Receive notifications when consultants reply',
    'I understand that this is an anonymous consultation service and I agree to the terms of use.' => 'I understand that this is an anonymous consultation service and I agree to the terms of use.',
    'Start Consultation' => 'Start Consultation',

    // Security notices
    'Security & Privacy' => 'Security & Privacy',
    'Your consultation is completely anonymous' => 'Your consultation is completely anonymous',
    'Keep your ID and secret safe - they cannot be recovered' => 'Keep your ID and secret safe - they cannot be recovered',
    'Consultations are automatically deleted after 90 days' => 'Consultations are automatically deleted after 90 days',
    'Use the panic button (Escape key) to quickly close this page' => 'Use the panic button (Escape key) to quickly close this page',

    // Success page
    'Consultation Created Successfully' => 'Consultation Created Successfully',
    'Important: Save These Credentials!' => 'Important: Save These Credentials!',
    'This information will only be shown once. Without both your ID and secret, you cannot access your consultation.' => 'This information will only be shown once. Without both your ID and secret, you cannot access your consultation.',
    'Your ID:' => 'Your ID:',
    'Your Secret:' => 'Your Secret:',
    'Copy' => 'Copy',
    'Copied!' => 'Copied!',
    'What happens next?' => 'What happens next?',
    'Your message has been sent to our consultation team' => 'Your message has been sent to our consultation team',
    'You will receive a response via email (if provided) or check back here' => 'You will receive a response via email (if provided) or check back here',
    'Use your ID and secret to access your consultation thread' => 'Use your ID and secret to access your consultation thread',
    'Download Credentials' => 'Download Credentials',

    // Security reminders
    'Security Reminders' => 'Security Reminders',
    'Store your credentials in a safe place' => 'Store your credentials in a safe place',
    'Do not share your credentials with anyone' => 'Do not share your credentials with anyone',
    'Your consultation will be automatically deleted after 90 days' => 'Your consultation will be automatically deleted after 90 days',
    'Use private/incognito browsing for additional privacy' => 'Use private/incognito browsing for additional privacy',

    // Thread view
    'Consultation Thread' => 'Consultation Thread',
    'ID:' => 'ID:',
    'Created:' => 'Created:',
    'Refresh' => 'Refresh',
    'Download' => 'Download',
    'Logout' => 'Logout',
    'Your Reply' => 'Your Reply',
    'Type your message here...' => 'Type your message here...',
    'Maximum 5000 characters' => 'Maximum 5000 characters',
    'characters' => 'characters',
    'Send Reply' => 'Send Reply',
    'You' => 'You',
    'Consultant' => 'Consultant',
    'Press ESC to quickly close this page' => 'Press ESC to quickly close this page',

    // Settings
    'Anonymous Portal Settings' => 'Anonymous Portal Settings',
    'Matrix Configuration' => 'Matrix Configuration',
    'Matrix Homeserver URL' => 'Matrix Homeserver URL',
    'The URL of your Matrix homeserver (e.g., https://matrix.example.com)' => 'The URL of your Matrix homeserver (e.g., https://matrix.example.com)',
    'Matrix Access Token' => 'Matrix Access Token',
    'Access token for the Matrix bot user' => 'Access token for the Matrix bot user',
    'Postmoogle Configuration' => 'Postmoogle Configuration',
    'Postmoogle API URL' => 'Postmoogle API URL',
    'The URL of your Postmoogle API endpoint' => 'The URL of your Postmoogle API endpoint',
    'Postmoogle API Key' => 'Postmoogle API Key',
    'API key for Postmoogle authentication' => 'API key for Postmoogle authentication',
    'Email Configuration' => 'Email Configuration',
    'Consultant Email' => 'Consultant Email',
    'Email address where new consultation notifications are sent' => 'Email address where new consultation notifications are sent',
    'From Email' => 'From Email',
    'Email address used as sender for notifications' => 'Email address used as sender for notifications',
    'Security Settings' => 'Security Settings',
    'Session Timeout (Minutes)' => 'Session Timeout (Minutes)',
    'How long user sessions remain active' => 'How long user sessions remain active',
    'Rate Limit Attempts' => 'Rate Limit Attempts',
    'Maximum failed login attempts before rate limiting' => 'Maximum failed login attempts before rate limiting',
    'Rate Limit Window (Minutes)' => 'Rate Limit Window (Minutes)',
    'Time window for rate limiting' => 'Time window for rate limiting',
    'Cleanup Settings' => 'Cleanup Settings',
    'Cleanup Days' => 'Cleanup Days',
    'Number of days after which consultations are automatically deleted' => 'Number of days after which consultations are automatically deleted',
    'Connection Tests' => 'Connection Tests',
    'Test Matrix Connection' => 'Test Matrix Connection',
    'Test Postmoogle Connection' => 'Test Postmoogle Connection',

    // Table attributes
    'Public ID' => 'Public ID',
    'Mailbox' => 'Mailbox',
    'Anfrage ID' => 'Request ID',
    'Sender' => 'Sender',
    'Message' => 'Message',
    'Timestamp' => 'Timestamp',
    'All anfragen' => 'All requests',
    'All antworten' => 'All replies',

    // PDF export
    'Anonymous Consultation Thread' => 'Anonymous Consultation Thread',
    'Thread ID:' => 'Thread ID:',
    'Exported:' => 'Exported:',
    'Total Messages:' => 'Total Messages:',
    'This is an export of your anonymous consultation thread.' => 'This is an export of your anonymous consultation thread.',
    'Generated by Anonymous Portal' => 'Generated by Anonymous Portal',
];
