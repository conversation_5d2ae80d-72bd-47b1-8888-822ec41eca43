<?php

namespace kldev\anonymousportal\widgets;

use Craft;
use craft\base\Widget;
use kldev\anonymousportal\Plugin;

/**
 * Anonymous Portal widget
 */
class AnonymousPortalWidget extends Widget
{
    public static function displayName(): string
    {
        return Craft::t('_craft-anonymous-portal', 'Anonymous Portal Status');
    }

    public static function iconPath(): ?string
    {
        return null;
    }

    public static function maxColspan(): ?int
    {
        return 2;
    }

    public function getBodyHtml(): ?string
    {
        $plugin = Plugin::getInstance();
        
        if (!$plugin) {
            return '<p>Plugin not available</p>';
        }

        $stats = $plugin->installService->getStatistics();
        $config = $plugin->installService->getConfigurationStatus();
        $connections = $plugin->installService->testAllConnections();

        return Craft::$app->view->renderTemplate('_craft-anonymous-portal/_widgets/status', [
            'stats' => $stats,
            'config' => $config,
            'connections' => $connections,
        ]);
    }
}
