{"name": "craftcms/craft", "description": "Craft CMS", "keywords": ["craft", "cms", "craftcms", "project"], "license": "0BSD", "homepage": "https://craftcms.com/", "type": "project", "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/cms/issues", "forum": "https://craftcms.stackexchange.com/", "source": "https://github.com/craftcms/cms", "docs": "https://craftcms.com/docs", "rss": "https://craftcms.com/changelog.rss"}, "require": {"adigital/cookie-consent-banner": "2.0.1", "codemonauts/craft-instagram-feed": "2.2.1", "craftcms/ckeditor": "3.11.1", "craftcms/cms": "********", "craftpulse/craft-colour-swatches": "4.5.3", "jalendport/craft-preparse": "2.1.2", "kldev/craft-ai-tools": "dev-master", "kldev/craft-anonymous-portal": "dev-main", "kldev/craft-deepl-translation": "dev-main", "kldev/craft-email-marketing": "dev-main", "kldev/craft-googlefonts-field-type": "dev-master", "kldev/craft-iconify": "dev-master", "kldev/craft-kldev-bundle": "dev-main", "kldev/craft-marketing-analytics": "dev-main", "kldev/craft-og-tag-parser": "dev-main", "kldev/craft-section-select-field": "dev-main", "kldev/craft-translation-manager": "dev-master", "mailerlite/mailerlite-php": "^1.0", "nystudio107/craft-twigpack": "4.0.0", "skynettechnologies/craft-allinoneaccessibility": "2.2.4", "verbb/bugsnag": "4.0.1", "verbb/image-resizer": "3.0.13", "verbb/super-table": "3.0.15", "vlucas/phpdotenv": "^5.4.0"}, "require-dev": {"craftcms/generator": "^1.3.0", "yiisoft/yii2-shell": "^2.0.3"}, "autoload": {"psr-4": {"modules\\": "modules/"}}, "config": {"allow-plugins": {"craftcms/plugin-installer": true, "yiisoft/yii2-composer": true, "php-http/discovery": true}, "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "8.0.2"}}, "scripts": {"post-create-project-cmd": ["@php -r \"file_exists('.env') || copy('.env.example.dev', '.env');\"", "@php -r \"unlink('composer.json');\"", "@php -r \"rename('composer.json.default', 'composer.json');\"", "@composer dump-autoload -o", "@php craft setup/welcome"]}, "repositories": [{"type": "path", "url": "plugins/craft-ai-tools", "options": {"symlink": true}}, {"type": "composer", "url": "https://composer.craftcms.com", "canonical": false}, {"type": "path", "url": "plugins/craft-googlefonts-field-type"}, {"type": "path", "url": "plugins/craft-iconify"}, {"type": "path", "url": "plugins/craft-deepl-translation"}, {"type": "path", "url": "plugins/craft-translation-manager"}, {"type": "path", "url": "plugins/craft-section-select-field"}, {"type": "path", "url": "plugins/craft-email-marketing", "options": {"symlink": true}}, {"type": "path", "url": "plugins/kldev-bundle"}, {"type": "path", "url": "plugins/craft-og-tag-parser"}, {"type": "path", "url": "plugins/marketing-analytics"}, {"type": "path", "url": "plugins/craft-anonymous-portal"}], "minimum-stability": "dev", "prefer-stable": true}