<?php return array(
    'root' => array(
        'name' => 'craftcms/craft',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => NULL,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'adigital/cookie-consent-banner' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '385905fddedcb220175ae3a0b167a24d83c55023',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../adigital/cookie-consent-banner',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bower-asset/inputmask' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '5.0.9',
            ),
        ),
        'bower-asset/jquery' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.6.1',
            ),
        ),
        'bower-asset/punycode' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '^1.4',
            ),
        ),
        'bower-asset/yii2-pjax' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '~2.0.1',
            ),
        ),
        'bugsnag/bugsnag' => array(
            'pretty_version' => 'v3.29.1',
            'version' => '3.29.1.0',
            'reference' => '7fff8512b237a57323f600975ada6376e2b912c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bugsnag/bugsnag',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cebe/markdown' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '9bac5e971dd391e2802dca5400bbeacbaea9eb86',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cebe/markdown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clue/stream-filter' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'reference' => '049509fef80032cb3f051595029ab75b49a3c2f7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/stream-filter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'codemonauts/craft-instagram-feed' => array(
            'pretty_version' => '2.2.1',
            'version' => '*******',
            'reference' => '4195a2bd903f24cf7c7ffa909add36c2fe6bc4d1',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../codemonauts/craft-instagram-feed',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'commerceguys/addressing' => array(
            'pretty_version' => 'v1.4.2',
            'version' => '*******',
            'reference' => '406c7b5f0fbe4f6a64155c0fe03b1adb34d01308',
            'type' => 'library',
            'install_path' => __DIR__ . '/../commerceguys/addressing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.6',
            'version' => '*******',
            'reference' => 'f65c239c970e7f072f067ab78646e9f0b2935175',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/class-map-generator' => array(
            'pretty_version' => '1.6.1',
            'version' => '*******',
            'reference' => '134b705ddb0025d397d8318a75825fe3c9d1da34',
            'type' => 'library',
            'install_path' => __DIR__ . '/./class-map-generator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/composer' => array(
            'pretty_version' => '2.8.8',
            'version' => '*******',
            'reference' => '85ff84d6c5260ba21740a7c5c9a111890805d6e7',
            'type' => 'library',
            'install_path' => __DIR__ . '/./composer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/metadata-minifier' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'c549d23829536f0d0e984aaabbf02af91f443207',
            'type' => 'library',
            'install_path' => __DIR__ . '/./metadata-minifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/spdx-licenses' => array(
            'pretty_version' => '1.5.8',
            'version' => '1.5.8.0',
            'reference' => '560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a',
            'type' => 'library',
            'install_path' => __DIR__ . '/./spdx-licenses',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/xdebug-handler' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => '6c1925561632e83d60a44492e0b344cf48ab85ef',
            'type' => 'library',
            'install_path' => __DIR__ . '/./xdebug-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftcms/ckeditor' => array(
            'pretty_version' => '3.11.1',
            'version' => '3.11.1.0',
            'reference' => '6fe26999a782a6c60a96bd5725c96fa4a6fca942',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../craftcms/ckeditor',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftcms/cms' => array(
            'pretty_version' => '4.15.0.2',
            'version' => '4.15.0.2',
            'reference' => '8e95b134e96e45c4ee3318bc1f738497723cfedd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../craftcms/cms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftcms/craft' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => NULL,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftcms/generator' => array(
            'pretty_version' => '1.8.0',
            'version' => '1.8.0.0',
            'reference' => '3427ee72fdf58066bbf0e3607bf8bf44f77517a2',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../craftcms/generator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'craftcms/html-field' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => 'b28e5d27ce38874111597e32782f1ce95f4de16a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../craftcms/html-field',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftcms/plugin-installer' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'reference' => 'bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../craftcms/plugin-installer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftcms/server-check' => array(
            'pretty_version' => '2.1.10',
            'version' => '2.1.10.0',
            'reference' => 'fbf31237d80d0fcf3615c0d405de346839c896c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../craftcms/server-check',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'craftpulse/craft-colour-swatches' => array(
            'pretty_version' => '4.5.3',
            'version' => '4.5.3.0',
            'reference' => 'bb0f476e43767df122843bf4fc9fb3f5fa5f8a42',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../craftpulse/craft-colour-swatches',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'creocoder/yii2-nested-sets' => array(
            'pretty_version' => '0.9.0',
            'version' => '0.9.0.0',
            'reference' => 'cb8635a459b6246e5a144f096b992dcc30cf9954',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../creocoder/yii2-nested-sets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'danielstjules/stringy' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '~3.0',
            ),
        ),
        'defuse/php-encryption' => array(
            'pretty_version' => 'v2.4.0',
            'version' => '2.4.0.0',
            'reference' => 'f53396c2d34225064647a05ca76c1da9d99e5828',
            'type' => 'library',
            'install_path' => __DIR__ . '/../defuse/php-encryption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/collections' => array(
            'pretty_version' => '1.8.0',
            'version' => '1.8.0.0',
            'reference' => '2b44dd4cbca8b5744327de78bafef5945c7e7b5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '3.2.6',
            'version' => '3.2.6.0',
            'reference' => 'e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'elvanto/litemoji' => array(
            'pretty_version' => '4.3.0',
            'version' => '4.3.0.0',
            'reference' => 'f13cf10686f7110a3b17d09de03050d0708840b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../elvanto/litemoji',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'enshrined/svg-sanitize' => array(
            'pretty_version' => '0.19.0',
            'version' => '0.19.0.0',
            'reference' => 'e95cd17be68e45f523cbfb0fe50cdd891b0cf20e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../enshrined/svg-sanitize',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.18.0',
            'version' => '4.18.0.0',
            'reference' => 'cb56001e54359df7ae76dc522d08845dc741621b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/collections' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '9.52.16.0',
            'reference' => 'd3710b0b244bfc62c288c1a87eaa62dd28352d1f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/collections',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/conditionable' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '9.52.16.0',
            'reference' => 'bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/conditionable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/contracts' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '9.52.16.0',
            'reference' => '44f65d723b13823baa02ff69751a5948bde60c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/macroable' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '9.52.16.0',
            'reference' => 'e3bfaf6401742a9c6abca61b9b10e998e5b6449a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../illuminate/macroable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jalendport/craft-preparse' => array(
            'pretty_version' => '2.1.2',
            'version' => '2.1.2.0',
            'reference' => 'b51508be3f1b012e005fd90cf06c27c23b2ab177',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../jalendport/craft-preparse',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'justinrainbow/json-schema' => array(
            'pretty_version' => '6.4.1',
            'version' => '6.4.1.0',
            'reference' => '35d262c94959571e8736db1e5c9bc36ab94ae900',
            'type' => 'library',
            'install_path' => __DIR__ . '/../justinrainbow/json-schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-ai-tools' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '61d71fa001b5b623087086dedcbee5a34fdeb5f2',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-ai-tools',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-anonymous-portal' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'b87c15b079b8e3c0bdd874a96b6c7292a3938d42',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-anonymous-portal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-deepl-translation' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '040c6ab8045fc0a92a1cc7fcb43350aa9a5b1c06',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-deepl-translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-email-marketing' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'af9d3602bf9d4f446917503a5041a6e605a5e233',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-email-marketing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-googlefonts-field-type' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'c2abbf247147d0d17e708c0d6b892eefaabe4da7',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-googlefonts-field-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-iconify' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'b4843c41790520168e212eec95842193e3876d4a',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-iconify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-kldev-bundle' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '181adebe442e0edc5cb9f8b57b3a99026bacb89e',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-kldev-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-marketing-analytics' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'a8a676ac0a06ddadaf7c43e10c962d3b2c53e291',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-marketing-analytics',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-og-tag-parser' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'c77c19d843482a43b6168fe579a6432c9bfc7ecd',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-og-tag-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-section-select-field' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => 'b59daa2a3c14aafe0a8ed1111dc3ac0715cec24f',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-section-select-field',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kldev/craft-translation-manager' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '921bea8b255cb07b3fa043148a1550756a12e5d4',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../kldev/craft-translation-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/html-to-markdown' => array(
            'pretty_version' => '5.1.1',
            'version' => '5.1.1.0',
            'reference' => '0b4066eede55c48f38bcee4fb8f0aa85654390fd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/html-to-markdown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lsolesen/pel' => array(
            'pretty_version' => '0.9.12',
            'version' => '0.9.12.0',
            'reference' => 'b95fe29cdacf9d36330da277f10910a13648c84c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lsolesen/pel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mailchimp/marketing' => array(
            'pretty_version' => '3.0.80',
            'version' => '3.0.80.0',
            'reference' => 'c1a38f7248d8de7de412418fed8dae759b9e4b97',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mailchimp/marketing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mailerlite/mailerlite-php' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'b33a65e4f1b28895212e825ffd8ba639501ef444',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mailerlite/mailerlite-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'marc-mabe/php-enum' => array(
            'pretty_version' => 'v4.7.1',
            'version' => '4.7.1.0',
            'reference' => '7159809e5cfa041dca28e61f7f7ae58063aae8ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../marc-mabe/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikehaertl/php-shellcommand' => array(
            'pretty_version' => '1.7.0',
            'version' => '1.7.0.0',
            'reference' => 'e79ea528be155ffdec6f3bf1a4a46307bb49e545',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikehaertl/php-shellcommand',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'moneyphp/money' => array(
            'pretty_version' => 'v4.4.0',
            'version' => '4.4.0.0',
            'reference' => '5e60aebf09f709dd4ea16bf85e66d65301c0d172',
            'type' => 'library',
            'install_path' => __DIR__ . '/../moneyphp/money',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.10.0',
            'version' => '2.10.0.0',
            'reference' => '5cf826f2991858b54d5c3809bee745560a1042a7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/php-generator' => array(
            'pretty_version' => 'v4.1.5',
            'version' => '4.1.5.0',
            'reference' => '690b00d81d42d5633e4457c43ef9754573b6f9d6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/php-generator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd3ad0aa3b9f934602cb3e3902ebccf10be34d218',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.19.1',
            'version' => '4.19.1.0',
            'reference' => '4e1b88d21c69391150ace211e9eaf05810858d0b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nystudio107/craft-code-editor' => array(
            'pretty_version' => '1.0.22',
            'version' => '1.0.22.0',
            'reference' => '170edf71355b659e1db9ede12980b17c20eb3d1f',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../nystudio107/craft-code-editor',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nystudio107/craft-twigpack' => array(
            'pretty_version' => '4.0.0',
            'version' => '4.0.0.0',
            'reference' => '210f0f36728761380f51d5fde4092582efc4021d',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../nystudio107/craft-twigpack',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-common' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => '1e19c059b0e4d5f717bf5d524d616165aeab0612',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/client-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.19.4',
            'version' => '1.19.4.0',
            'reference' => '0700efda8d7526335132360167315fdab3aeb599',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/httplug' => array(
            'pretty_version' => '2.4.0',
            'version' => '2.4.0.0',
            'reference' => '625ad742c360c8ac580fcc647a1541d29e257f67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message' => array(
            'pretty_version' => '1.16.1',
            'version' => '1.16.1.0',
            'reference' => '5997f3289332c699fa2545c427826272498a2088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/promise' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'fc85b1fba37c169a69a07ef0d5a8075770cc1f83',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/reflection-common' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '1d01c49d4ed62f25aa84a747ad35d5a16924662b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/reflection-docblock' => array(
            'pretty_version' => '5.6.2',
            'version' => '5.6.2.0',
            'reference' => '92dde6a5919e34835c506ac8c523ef095a95ed62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/reflection-docblock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpdocumentor/type-resolver' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '679e3ce485b99e84c775d28e2e96fade9a7fb50a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpdocumentor/type-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpstan/phpdoc-parser' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '9b30d6fd026b2c132b3985ce6b23bec09ab3aa68',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpstan/phpdoc-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pixelandtonic/imagine' => array(
            'pretty_version' => '1.3.3.1',
            'version' => '1.3.3.1',
            'reference' => '4d9bb596ff60504e37ccf9103c0bb705dba7fec6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pixelandtonic/imagine',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
                1 => '1.0|2.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.11.22',
            'version' => '0.11.22.0',
            'reference' => '128fa1b608be651999ed9789c95e6e2a31b5802b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'react/promise' => array(
            'pretty_version' => 'v3.2.0',
            'version' => '3.2.0.0',
            'reference' => '8a164643313c71354582dc850b42b33fa12a4b63',
            'type' => 'library',
            'install_path' => __DIR__ . '/../react/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'samdark/yii2-psr-log-target' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => '5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../samdark/yii2-psr-log-target',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'seld/cli-prompt' => array(
            'pretty_version' => '1.0.4',
            'version' => '1.0.4.0',
            'reference' => 'b8dfcf02094b8c03b40322c229493bb2884423c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/cli-prompt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'seld/jsonlint' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '1748aaf847fc731cfad7725aec413ee46f0cc3a2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/jsonlint',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'seld/phar-utils' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => 'ea2f4014f163c1be4c601b9b7bd6af81ba8d701c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/phar-utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'seld/signal-handler' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98',
            'type' => 'library',
            'install_path' => __DIR__ . '/../seld/signal-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'skynettechnologies/craft-allinoneaccessibility' => array(
            'pretty_version' => '2.2.4',
            'version' => '2.2.4.0',
            'reference' => '8b7892a7dea0ddf8a57c041b51decb97312bac0f',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../skynettechnologies/craft-allinoneaccessibility',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v5.4.47',
            'version' => '5.4.47.0',
            'reference' => 'c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '26954b3d62a6c5fd0ea8a2a00c0353a14978d05c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => '2eaf8e63bc5b8cefabd4a800157f0d0c094f677a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '7bc61cc2db649b4637d331240c5346dcc7708051',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '57c8294ed37d4a055b77057827c67f9558c95c54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => '63741784cd7b9967975eec610b256eed3ede022b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client' => array(
            'pretty_version' => 'v6.0.20',
            'version' => '6.0.20.0',
            'reference' => '541c04560da1875f62c963c3aab6ea12a7314e11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '4184b9b63af1edaf35b6a7974c6f1f9f33294129',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0',
            ),
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'cd60799210c488f545ddde2444dc1aa548322872',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'd7052547a0070cbeadd474e172b527a00d657301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => '6a180d1c45e0d9797470ca9eb46215692de00fa3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '48becf00c920479ca2e910c22a5a39e5d47ca956',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce',
            'type' => 'metapackage',
            'install_path' => NULL,
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v5.4.47',
            'version' => '5.4.47.0',
            'reference' => '5d1662fb32ebc94f17ddb8d635454a776066733d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'd78d39c1599bd1188b8e26bb341da52c3c6d8a66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '6.0.19.0',
            'reference' => 'd9e72497367c23e08bf94176d2be45b00a9d232a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v5.4.48',
            'version' => '5.4.48.0',
            'reference' => '42f18f170aa86d612c3559cfb3bd11a375df32c8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'reference' => 'a454d47278cc16a5db371fe73ae66a78a633371e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theiconic/name-parser' => array(
            'pretty_version' => 'v1.2.11',
            'version' => '1.2.11.0',
            'reference' => '9a54a713bf5b2e7fd990828147d42de16bf8a253',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theiconic/name-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.15.0',
            'version' => '3.15.0.0',
            'reference' => '2d5b3964cc21d0188633d7ddce732dc8e874db02',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'verbb/base' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '47a2671ef8862236b19609fe8409f11843aee1cf',
            'type' => 'yii-module',
            'install_path' => __DIR__ . '/../verbb/base',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'verbb/bugsnag' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'e5ce978f4d2c6535bd1caf9e9b215f6ddcfe2841',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../verbb/bugsnag',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'verbb/image-resizer' => array(
            'pretty_version' => '3.0.13',
            'version' => '3.0.13.0',
            'reference' => '33ba642897780b5fb0a150abd7482a9d890918c4',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../verbb/image-resizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'verbb/super-table' => array(
            'pretty_version' => '3.0.15',
            'version' => '3.0.15.0',
            'reference' => 'ce8830aeb0cc5e8b5065d4e19925a60ddc3f3d4a',
            'type' => 'craft-plugin',
            'install_path' => __DIR__ . '/../verbb/super-table',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.1',
            'version' => '5.6.1.0',
            'reference' => 'a59a13791077fe3d44f90e7133eb68e7d22eaff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/anti-xss' => array(
            'pretty_version' => '4.1.42',
            'version' => '4.1.42.0',
            'reference' => 'bca1f8607e55a3c5077483615cd93bd8f11bd675',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/anti-xss',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/arrayy' => array(
            'pretty_version' => '7.9.6',
            'version' => '7.9.6.0',
            'reference' => '0e20b8c6eef7fc46694a2906e0eae2f9fc11cade',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/arrayy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/email-check' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => '6ea842920bbef6758b8c1e619fd1710e7a1a2cac',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/email-check',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-utf8' => array(
            'pretty_version' => '6.0.13',
            'version' => '6.0.13.0',
            'reference' => 'b8ce36bf26593e5c2e81b1850ef0ffb299d2043f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-utf8',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/stop-words' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8e63c0af20f800b1600783764e0ce19e53969f71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/stop-words',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/stringy' => array(
            'pretty_version' => '6.5.3',
            'version' => '6.5.3.0',
            'reference' => 'c453c88fbff298f042c836ef44306f8703b2d537',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/stringy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/urlify' => array(
            'pretty_version' => '5.0.7',
            'version' => '5.0.7.0',
            'reference' => '014b2074407b5db5968f836c27d8731934b330e4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/urlify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webonyx/graphql-php' => array(
            'pretty_version' => 'v14.11.10',
            'version' => '14.11.10.0',
            'reference' => 'd9c2fdebc6aa01d831bc2969da00e8588cffef19',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webonyx/graphql-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yii2tech/ar-softdelete' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.4',
            ),
        ),
        'yiisoft/yii2' => array(
            'pretty_version' => '2.0.52',
            'version' => '2.0.52.0',
            'reference' => '540e7387d934c52e415614aa081fb38d04c72d9a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yiisoft/yii2',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-composer' => array(
            'pretty_version' => '2.0.11',
            'version' => '2.0.11.0',
            'reference' => 'b684b01ecb119c8287721def726a0e24fec2fef2',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../yiisoft/yii2-composer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-debug' => array(
            'pretty_version' => '2.1.26',
            'version' => '2.1.26.0',
            'reference' => 'e4b28a1d295fc977d8399db544336dd5b2764397',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-debug',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-queue' => array(
            'pretty_version' => '2.3.7',
            'version' => '2.3.7.0',
            'reference' => 'dbc9d4a7b2a6995cd19c3e334227482ef55e559b',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-queue',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yiisoft/yii2-shell' => array(
            'pretty_version' => '2.0.5',
            'version' => '2.0.5.0',
            'reference' => '358d4651ce1f54db0f1add026c202ac2e47db06b',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-shell',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'yiisoft/yii2-symfonymailer' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '82f5902551a160633c4734b5096977ce76a809d9',
            'type' => 'yii2-extension',
            'install_path' => __DIR__ . '/../yiisoft/yii2-symfonymailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
