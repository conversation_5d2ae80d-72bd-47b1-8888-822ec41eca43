{"packages": [{"name": "adigital/cookie-consent-banner", "version": "2.0.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/a-digital/cookie-consent-banner/zipball/385905fddedcb220175ae3a0b167a24d83c55023", "reference": "385905fddedcb220175ae3a0b167a24d83c55023", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0"}, "time": "2024-02-27T09:38:26+00:00", "type": "craft-plugin", "extra": {"name": "<PERSON><PERSON>", "handle": "cookie-consent-banner", "hasCpSettings": true, "hasCpSection": false, "changelogUrl": "https://github.com/a-digital/cookie-consent-banner/blob/master/CHANGELOG.md", "class": "adigital\\cookieconsentbanner\\CookieConsentBanner"}, "installation-source": "dist", "autoload": {"psr-4": {"adigital\\cookieconsentbanner\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Mark @ A Digital", "homepage": "https://adigital.agency"}], "description": "Add a configurable cookie consent banner to the website.", "keywords": ["cms", "cookie consent banner", "craft", "craft-plugin", "craftcms"], "support": {"docs": "https://github.com/a-digital/cookie-consent-banner/blob/master/README.md", "issues": "https://github.com/a-digital/cookie-consent-banner/issues"}, "install-path": "../adigital/cookie-consent-banner"}, {"name": "bugsnag/bugsnag", "version": "v3.29.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/bugsnag/bugsnag-php/zipball/7fff8512b237a57323f600975ada6376e2b912c1", "reference": "7fff8512b237a57323f600975ada6376e2b912c1", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "guzzlehttp/guzzle": "^5.0|^6.0|^7.0", "php": ">=5.5"}, "time": "2023-05-10T11:07:22+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.20-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Bugsnag\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://bugsnag.com"}], "description": "Official Bugsnag notifier for PHP applications.", "homepage": "https://github.com/bugsnag/bugsnag-php", "keywords": ["bugsnag", "errors", "exceptions", "logging", "tracking"], "install-path": "../bugsnag/bugsnag"}, {"name": "cebe/markdown", "version": "1.2.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "time": "2018-03-26T11:24:36+00:00", "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "install-path": "../cebe/markdown"}, {"name": "clue/stream-filter", "version": "v1.7.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "time": "2023-12-20T15:40:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "install-path": "../clue/stream-filter"}, {"name": "codemonauts/craft-instagram-feed", "version": "2.2.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/codemonauts/craft-instagram-feed/zipball/4195a2bd903f24cf7c7ffa909add36c2fe6bc4d1", "reference": "4195a2bd903f24cf7c7ffa909add36c2fe6bc4d1", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "ext-json": "*"}, "time": "2024-01-08T16:14:37+00:00", "type": "craft-plugin", "extra": {"handle": "instagramfeed", "class": "codemonauts\\instagramfeed\\InstagramFeed", "name": "Instagram Feed"}, "installation-source": "dist", "autoload": {"psr-4": {"codemonauts\\instagramfeed\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "codemonauts", "homepage": "https://codemonauts.com"}], "description": "Craft CMS plugin to receive Instagram feed data as variable in templates.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "instagram"], "support": {"source": "https://github.com/codemonauts/craft-instagram-feed", "docs": "https://plugins.codemonauts.com/plugins/instagramfeed/Introduction.html", "issues": "https://github.com/codemonauts/craft-instagram-feed/issues"}, "install-path": "../codemonauts/craft-instagram-feed"}, {"name": "commerceguys/addressing", "version": "v1.4.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/commerceguys/addressing/zipball/406c7b5f0fbe4f6a64155c0fe03b1adb34d01308", "reference": "406c7b5f0fbe4f6a64155c0fe03b1adb34d01308", "shasum": ""}, "require": {"doctrine/collections": "^1.2 || ^2.0", "php": ">=7.3"}, "suggest": {"symfony/validator": "to validate addresses"}, "time": "2023-02-15T10:11:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"CommerceGuys\\Addressing\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}], "description": "Addressing library powered by CLDR and Google's address data.", "keywords": ["address", "internationalization", "localization", "postal"], "install-path": "../commerceguys/addressing"}, {"name": "composer/ca-bundle", "version": "1.5.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "f65c239c970e7f072f067ab78646e9f0b2935175"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/f65c239c970e7f072f067ab78646e9f0b2935175", "reference": "f65c239c970e7f072f067ab78646e9f0b2935175", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "time": "2025-03-06T14:30:56+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.6"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./ca-bundle"}, {"name": "composer/class-map-generator", "version": "1.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/134b705ddb0025d397d8318a75825fe3c9d1da34", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpunit/phpunit": "^8", "symfony/filesystem": "^5.4 || ^6"}, "time": "2025-03-24T13:50:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.6.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./class-map-generator"}, {"name": "composer/composer", "version": "2.8.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "85ff84d6c5260ba21740a7c5c9a111890805d6e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/85ff84d6c5260ba21740a7c5c9a111890805d6e7", "reference": "85ff84d6c5260ba21740a7c5c9a111890805d6e7", "shasum": ""}, "require": {"composer/ca-bundle": "^1.5", "composer/class-map-generator": "^1.4.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2.2 || ^3.2", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^6.3.1", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.11 || ^3.2", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "seld/signal-handler": "^2.0", "symfony/console": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/filesystem": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/finder": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "symfony/process": "^5.4.35 || ^6.3.12 || ^7.0.3"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-symfony": "^1.4.0", "symfony/phpunit-bridge": "^6.4.3 || ^7.0.1"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "time": "2025-04-04T14:56:46+00:00", "bin": ["bin/composer"], "type": "library", "extra": {"phpstan": {"includes": ["phpstan/rules.neon"]}, "branch-alias": {"dev-main": "2.8-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "security": "https://github.com/composer/composer/security/policy", "source": "https://github.com/composer/composer/tree/2.8.8"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./composer"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "time": "2021-04-07T13:37:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "install-path": "./metadata-minifier"}, {"name": "composer/pcre", "version": "3.3.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "time": "2024-11-12T16:29:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "install-path": "./pcre"}, {"name": "composer/semver", "version": "3.4.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "time": "2024-09-19T14:15:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "install-path": "./semver"}, {"name": "composer/spdx-licenses", "version": "1.5.8", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "reference": "560bdcf8deb88ae5d611c80a2de8ea9d0358cc0a", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "time": "2023-11-20T07:44:33+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "install-path": "./spdx-licenses"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "time": "2024-05-06T16:37:16+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "install-path": "./xdebug-handler"}, {"name": "craftcms/ckeditor", "version": "3.11.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/ckeditor/zipball/6fe26999a782a6c60a96bd5725c96fa4a6fca942", "reference": "6fe26999a782a6c60a96bd5725c96fa4a6fca942", "shasum": ""}, "require": {"craftcms/cms": "^4.5.0-beta.2", "craftcms/html-field": "^2.0.0-alpha.2", "nystudio107/craft-code-editor": ">=1.0.8 <=1.0.13 || ^1.0.16", "php": "^8.0.2"}, "time": "2025-02-18T22:55:06+00:00", "type": "craft-plugin", "extra": {"name": "CKEditor", "handle": "ckeditor", "documentationUrl": "https://github.com/craftcms/ckeditor/blob/master/README.md"}, "installation-source": "dist", "autoload": {"psr-4": {"craft\\ckeditor\\": "src/"}}, "license": ["GPL-3.0-or-later"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Edit rich text content in Craft CMS using CKEditor.", "keywords": ["ckeditor", "cms", "craftcms", "html", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/ckeditor/issues?state=open", "source": "https://github.com/craftcms/ckeditor", "docs": "https://github.com/craftcms/ckeditor/blob/master/README.md", "rss": "https://github.com/craftcms/ckeditor/commits/master.atom"}, "install-path": "../craftcms/ckeditor"}, {"name": "craftcms/cms", "version": "********", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/cms/zipball/8e95b134e96e45c4ee3318bc1f738497723cfedd", "reference": "8e95b134e96e45c4ee3318bc1f738497723cfedd", "shasum": ""}, "require": {"commerceguys/addressing": "^1.2", "composer/composer": "^2.7.0", "craftcms/plugin-installer": "~1.6.0", "craftcms/server-check": "~2.1.2", "creocoder/yii2-nested-sets": "~0.9.0", "elvanto/litemoji": "~4.3.0", "enshrined/svg-sanitize": "~0.19.0", "ext-bcmath": "*", "ext-curl": "*", "ext-dom": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-zip": "*", "guzzlehttp/guzzle": "^7.2.0", "illuminate/collections": "^9.1.0", "mikehaertl/php-shellcommand": "^1.6.3", "moneyphp/money": "^4.0", "monolog/monolog": "^2.3", "php": "^8.0.2", "pixelandtonic/imagine": "~*******", "samdark/yii2-psr-log-target": "^1.1.3", "seld/cli-prompt": "^1.0.4", "symfony/http-client": "^6.0.3|^7.0", "symfony/var-dumper": "^5.0|^6.0", "symfony/yaml": "^5.2.3", "theiconic/name-parser": "^1.2", "twig/twig": "~3.15.0", "voku/stringy": "^6.4.0", "webonyx/graphql-php": "~14.11.10", "yiisoft/yii2": "~********", "yiisoft/yii2-debug": "~********", "yiisoft/yii2-queue": "~2.3.2", "yiisoft/yii2-symfonymailer": "^2.0.0"}, "provide": {"bower-asset/inputmask": "5.0.9", "bower-asset/jquery": "3.6.1", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "yii2tech/ar-softdelete": "1.0.4"}, "suggest": {"ext-exif": "Adds support for parsing image EXIF data.", "ext-iconv": "Adds support for more character encodings than PHP’s built-in mb_convert_encoding() function, which Craft will take advantage of when converting strings to UTF-8.", "ext-imagick": "Adds support for more image processing formats and options."}, "time": "2025-04-17T16:30:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"craft\\": "src/", "yii2tech\\ar\\softdelete\\": "lib/ar-softdelete/src/"}}, "license": ["proprietary"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/cms/issues?state=open", "forum": "https://craftcms.stackexchange.com/", "source": "https://github.com/craftcms/cms", "docs": "https://craftcms.com/docs/4.x/", "rss": "https://github.com/craftcms/cms/releases.atom"}, "install-path": "../craftcms/cms"}, {"name": "craftcms/generator", "version": "1.8.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/generator/zipball/3427ee72fdf58066bbf0e3607bf8bf44f77517a2", "reference": "3427ee72fdf58066bbf0e3607bf8bf44f77517a2", "shasum": ""}, "require": {"craftcms/cms": "^4.4.11", "nette/php-generator": "^4.0", "nikic/php-parser": "^4.15", "php": "^8.0.2"}, "time": "2024-06-19T14:35:36+00:00", "type": "yii2-extension", "extra": {"bootstrap": "craft\\generator\\Extension"}, "installation-source": "dist", "autoload": {"psr-4": {"craft\\generator\\": "src/"}}, "license": ["mit"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Craft CMS component generator", "homepage": "https://craftcms.com", "keywords": ["cms", "craftcms", "yii2"], "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/generator/issues?state=open", "source": "https://github.com/craftcms/generator", "rss": "https://github.com/craftcms/generator/releases.atom"}, "install-path": "../craftcms/generator"}, {"name": "craftcms/html-field", "version": "2.2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/html-field/zipball/b28e5d27ce38874111597e32782f1ce95f4de16a", "reference": "b28e5d27ce38874111597e32782f1ce95f4de16a", "shasum": ""}, "require": {"craftcms/cms": "^4.2.0", "league/html-to-markdown": "^5.1", "php": "^8.0.2"}, "time": "2025-02-14T19:24:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"craft\\htmlfield\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Pixel & Tonic", "homepage": "https://pixelandtonic.com/"}], "description": "Base class for Craft CMS field types with HTML values.", "support": {"email": "<EMAIL>", "issues": "https://github.com/craftcms/html-field/issues?state=open", "source": "https://github.com/craftcms/html-field", "docs": "https://github.com/craftcms/html-field/blob/main/README.md", "rss": "https://github.com/craftcms/html-field/commits/main.atom"}, "install-path": "../craftcms/html-field"}, {"name": "craftcms/plugin-installer", "version": "1.6.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/plugin-installer/zipball/bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "reference": "bd1650e8da6d5ca7a8527068d3e51c34bc7b6b4f", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.4"}, "time": "2023-02-22T13:17:00+00:00", "type": "composer-plugin", "extra": {"class": "craft\\composer\\Plugin"}, "installation-source": "dist", "autoload": {"psr-4": {"craft\\composer\\": "src/"}}, "license": ["MIT"], "description": "Craft CMS Plugin Installer", "homepage": "https://craftcms.com/", "keywords": ["cms", "composer", "craftcms", "installer", "plugin"], "install-path": "../craftcms/plugin-installer"}, {"name": "craftcms/server-check", "version": "2.1.10", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/craftcms/server-check.git", "reference": "fbf31237d80d0fcf3615c0d405de346839c896c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/craftcms/server-check/zipball/fbf31237d80d0fcf3615c0d405de346839c896c4", "reference": "fbf31237d80d0fcf3615c0d405de346839c896c4", "shasum": ""}, "time": "2025-02-11T20:01:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["server/requirements"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Craft CMS Server Check", "homepage": "https://craftcms.com/", "keywords": ["cms", "craftcms", "requirements", "yii2"], "support": {"docs": "https://github.com/craftcms/docs", "email": "<EMAIL>", "forum": "https://craftcms.stackexchange.com/", "issues": "https://github.com/craftcms/server-check/issues?state=open", "rss": "https://github.com/craftcms/server-check/releases.atom", "source": "https://github.com/craftcms/server-check"}, "install-path": "../craftcms/server-check"}, {"name": "craftpulse/craft-colour-swatches", "version": "4.5.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/craftpulse/craft-colour-swatches/zipball/bb0f476e43767df122843bf4fc9fb3f5fa5f8a42", "reference": "bb0f476e43767df122843bf4fc9fb3f5fa5f8a42", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "time": "2024-09-23T12:07:29+00:00", "type": "craft-plugin", "extra": {"name": "Colour Swatches", "handle": "colour-swatches", "description": "Adding custom selectable colour palettes, gradients, classes and more to your field types.", "developer": "craftpulse", "developerUrl": "https://github.com/craftpulse", "documentationUrl": "https://github.com/craftpulse/craft-colour-swatches/blob/v4/README.md", "changelogUrl": "https://github.com/craftpulse/craft-colour-swatches/blob/v4/CHANGELOG.md", "class": "percipiolondon\\colourswatches\\ColourSwatches"}, "installation-source": "dist", "autoload": {"psr-4": {"percipiolondon\\colourswatches\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Craft Pulse", "homepage": "https://github.com/craftpulse"}], "description": "Let clients choose from a predefined set of colours and utilise associated colour codes and class names in your templates.", "keywords": ["cms", "color-swatches", "colors", "colour-swatches", "colours", "craft", "craft-plugin", "craftcms", "swatches"], "support": {"email": "<EMAIL>", "docs": "https://github.com/percipioglobal/craft-colour-swatches/blob/v4/README.md", "issues": "https://github.com/percipioglobal/craft-colour-swatches/issues"}, "install-path": "../craftpulse/craft-colour-swatches"}, {"name": "creocoder/yii2-nested-sets", "version": "0.9.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/creocoder/yii2-nested-sets/zipball/cb8635a459b6246e5a144f096b992dcc30cf9954", "reference": "cb8635a459b6246e5a144f096b992dcc30cf9954", "shasum": ""}, "require": {"yiisoft/yii2": "*"}, "time": "2015-01-27T10:53:51+00:00", "type": "yii2-extension", "installation-source": "dist", "autoload": {"psr-4": {"creocoder\\nestedsets\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The nested sets behavior for the Yii framework", "keywords": ["nested sets", "yii2"], "install-path": "../creocoder/yii2-nested-sets"}, {"name": "defuse/php-encryption", "version": "v2.4.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/f53396c2d34225064647a05ca76c1da9d99e5828", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "time": "2023-06-19T06:10:36+00:00", "bin": ["bin/generate-defuse-key"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "install-path": "../defuse/php-encryption"}, {"name": "doctrine/collections", "version": "1.8.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "reference": "2b44dd4cbca8b5744327de78bafef5945c7e7b5e", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "time": "2022-09-01T20:12:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "install-path": "../doctrine/collections"}, {"name": "doctrine/deprecations", "version": "1.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "time": "2025-04-07T20:06:18+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "install-path": "../doctrine/deprecations"}, {"name": "doctrine/lexer", "version": "2.1.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "time": "2024-02-05T11:35:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "install-path": "../doctrine/lexer"}, {"name": "egulias/email-validator", "version": "3.2.6", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "time": "2023-06-01T07:04:22+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "install-path": "../egulias/email-validator"}, {"name": "elvanto/litemoji", "version": "4.3.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/elvanto/litemoji/zipball/f13cf10686f7110a3b17d09de03050d0708840b8", "reference": "f13cf10686f7110a3b17d09de03050d0708840b8", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.3"}, "time": "2022-10-28T02:32:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"LitEmoji\\": "src/"}}, "license": ["MIT"], "description": "A PHP library simplifying the conversion of unicode, HTML and shortcode emoji.", "keywords": ["emoji", "php-emoji"], "install-path": "../elvanto/litemoji"}, {"name": "enshrined/svg-sanitize", "version": "0.19.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/e95cd17be68e45f523cbfb0fe50cdd891b0cf20e", "reference": "e95cd17be68e45f523cbfb0fe50cdd891b0cf20e", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "time": "2024-06-18T10:27:15+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "install-path": "../enshrined/svg-sanitize"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "time": "2024-11-01T03:51:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "install-path": "../ezyang/htmlpurifier"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "time": "2024-07-20T21:45:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "install-path": "../graham-campbell/result-type"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2025-03-27T13:37:11+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2025-03-27T13:27:01+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2025-03-27T12:30:47+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "illuminate/collections", "version": "v9.52.16", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/collections/zipball/d3710b0b244bfc62c288c1a87eaa62dd28352d1f", "reference": "d3710b0b244bfc62c288c1a87eaa62dd28352d1f", "shasum": ""}, "require": {"illuminate/conditionable": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "php": "^8.0.2"}, "suggest": {"symfony/var-dumper": "Required to use the dump method (^6.0)."}, "time": "2023-06-11T21:17:10+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["helpers.php"], "psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Collections package.", "homepage": "https://laravel.com", "install-path": "../illuminate/collections"}, {"name": "illuminate/conditionable", "version": "v9.52.16", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/conditionable/zipball/bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364", "reference": "bea24daa0fa84b7e7b0d5b84f62c71b7e2dc3364", "shasum": ""}, "require": {"php": "^8.0.2"}, "time": "2023-02-01T21:42:32+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Conditionable package.", "homepage": "https://laravel.com", "install-path": "../illuminate/conditionable"}, {"name": "illuminate/contracts", "version": "v9.52.16", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/contracts/zipball/44f65d723b13823baa02ff69751a5948bde60c22", "reference": "44f65d723b13823baa02ff69751a5948bde60c22", "shasum": ""}, "require": {"php": "^8.0.2", "psr/container": "^1.1.1|^2.0.1", "psr/simple-cache": "^1.0|^2.0|^3.0"}, "time": "2023-02-08T14:36:30+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Contracts\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Contracts package.", "homepage": "https://laravel.com", "install-path": "../illuminate/contracts"}, {"name": "illuminate/macroable", "version": "v9.52.16", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://api.github.com/repos/illuminate/macroable/zipball/e3bfaf6401742a9c6abca61b9b10e998e5b6449a", "reference": "e3bfaf6401742a9c6abca61b9b10e998e5b6449a", "shasum": ""}, "require": {"php": "^8.0.2"}, "time": "2022-08-09T13:29:29+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Illuminate\\Support\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Illuminate Macroable package.", "homepage": "https://laravel.com", "install-path": "../illuminate/macroable"}, {"name": "jalendport/craft-preparse", "version": "2.1.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/jalendport/craft-preparse/zipball/b51508be3f1b012e005fd90cf06c27c23b2ab177", "reference": "b51508be3f1b012e005fd90cf06c27c23b2ab177", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "nystudio107/craft-code-editor": "^1.0.0", "php": "^8.0.2"}, "time": "2024-07-12T17:26:05+00:00", "type": "craft-plugin", "extra": {"name": "Preparse", "handle": "preparse-field", "schemaVersion": "1.1.0", "hasCpSettings": false, "hasCpSection": false, "changelogUrl": "https://github.com/jalendport/craft-preparse/blob/master/CHANGELOG.md", "components": {"preparseFieldService": "jalendport\\preparse\\services\\PreparseFieldService"}, "class": "jalendport\\preparse\\PreparseField"}, "installation-source": "dist", "autoload": {"psr-4": {"jalendport\\preparse\\": "src/", "aelvan\\preparsefield\\": "src/", "besteadfast\\preparsefield\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://jalendport.com/"}, {"name": "<PERSON>", "homepage": "https://www.vaersaagod.no"}], "description": "A fieldtype that parses <PERSON><PERSON> when an element is saved and saves the result as plain text.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "fieldtype", "preparse", "twig"], "support": {"docs": "https://github.com/jalendport/craft-preparse/blob/master/README.md", "issues": "https://github.com/jalendport/craft-preparse/issues"}, "install-path": "../jalendport/craft-preparse"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/35d262c94959571e8736db1e5c9bc36ab94ae900", "reference": "35d262c94959571e8736db1e5c9bc36ab94ae900", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "time": "2025-04-04T13:08:07+00:00", "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.1"}, "install-path": "../justin<PERSON>bow/json-schema"}, {"name": "kldev/craft-ai-tools", "version": "dev-master", "version_normalized": "dev-master", "dist": {"type": "path", "url": "plugins/craft-ai-tools", "reference": "61d71fa001b5b623087086dedcbee5a34fdeb5f2"}, "require": {"craftcms/cms": "^4.4.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-ai-tools", "name": "AI Content Tools", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\craftaitools\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "a bundle of ai functionality for content creation and marketing", "transport-options": {"relative": true}, "install-path": "../kldev/craft-ai-tools"}, {"name": "kldev/craft-anonymous-portal", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/craft-anonymous-portal", "reference": "b87c15b079b8e3c0bdd874a96b6c7292a3938d42"}, "require": {"craftcms/cms": "^4.5.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-anonymous-portal", "name": "Anonymous Portal", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\anonymousportal\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "Anonymous consultation portal with Matrix integration for secure visitor communication", "transport-options": {"relative": true}, "install-path": "../kldev/craft-anonymous-portal"}, {"name": "kldev/craft-deepl-translation", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/craft-deepl-translation", "reference": "040c6ab8045fc0a92a1cc7fcb43350aa9a5b1c06"}, "require": {"craftcms/cms": "^4.5.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-deepl-translation", "name": "DeepL Translation", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\deepl\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "Automated translation of pages, posts and blocks to target languages", "transport-options": {"relative": true}, "install-path": "../kldev/craft-deepl-translation"}, {"name": "kldev/craft-email-marketing", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/craft-email-marketing", "reference": "af9d3602bf9d4f446917503a5041a6e605a5e233"}, "require": {"craftcms/cms": "^4.5.0", "mailchimp/marketing": "^3.0", "mailerlite/mailerlite-php": "^1.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-email-marketing", "name": "Craft E-Mail Marketing", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\emailmarketing\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "plugin containing form section and integration of various email marketing services", "transport-options": {"symlink": true, "relative": true}, "install-path": "../kldev/craft-email-marketing"}, {"name": "kldev/craft-googlefonts-field-type", "version": "dev-master", "version_normalized": "dev-master", "dist": {"type": "path", "url": "plugins/craft-googlefonts-field-type", "reference": "c2abbf247147d0d17e708c0d6b892eefaabe4da7"}, "require": {"craftcms/cms": "^4.5.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-googlefonts-field-type", "name": "Google Fonts Field", "developer": "kldev", "documentationUrl": "", "class": "kldev\\googlefonts\\GoogleFonts"}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\googlefonts\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "adds a googlefonts field to choose a google font with preview", "transport-options": {"relative": true}, "install-path": "../kldev/craft-googlefonts-field-type"}, {"name": "kldev/craft-iconify", "version": "dev-master", "version_normalized": "dev-master", "dist": {"type": "path", "url": "plugins/craft-iconify", "reference": "b4843c41790520168e212eec95842193e3876d4a"}, "require": {"craftcms/cms": "^4.5.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-iconify", "name": "Iconify Extension", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\iconify\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "adds a custom field to select icon set and changes the icons in templates accordingly", "transport-options": {"relative": true}, "install-path": "../kldev/craft-iconify"}, {"name": "kldev/craft-kldev-bundle", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/kldev-bundle", "reference": "181adebe442e0edc5cb9f8b57b3a99026bacb89e"}, "require": {"craftcms/cms": "^4.7.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_kldev-bundle", "name": "Kldev Bundle", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\bundle\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "bundle for all shared logic accross customer craft cms", "transport-options": {"relative": true}, "install-path": "../kldev/craft-kldev-bundle"}, {"name": "kldev/craft-marketing-analytics", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/marketing-analytics", "reference": "a8a676ac0a06ddadaf7c43e10c962d3b2c53e291"}, "require": {"craftcms/cms": "^4.10.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_marketing-analytics", "name": "Marketing Analytics", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\analytics\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "plugin for marketing analytics services", "transport-options": {"relative": true}, "install-path": "../kldev/craft-marketing-analytics"}, {"name": "kldev/craft-og-tag-parser", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/craft-og-tag-parser", "reference": "c77c19d843482a43b6168fe579a6432c9bfc7ecd"}, "require": {"craftcms/cms": "^4.8.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-og-tag-parser", "name": "craft-og-tag-parser", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"ogtagparser\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "parses og tags from urls and makes them accessible to twig templates", "transport-options": {"relative": true}, "install-path": "../kldev/craft-og-tag-parser"}, {"name": "kldev/craft-section-select-field", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "plugins/craft-section-select-field", "reference": "b59daa2a3c14aafe0a8ed1111dc3ac0715cec24f"}, "require": {"craftcms/cms": "^4.5.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-section-select-field", "name": "CraftSectionSelectField", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\sectionselect\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "select fields for all sections added in the cms", "transport-options": {"relative": true}, "install-path": "../kldev/craft-section-select-field"}, {"name": "kldev/craft-translation-manager", "version": "dev-master", "version_normalized": "dev-master", "dist": {"type": "path", "url": "plugins/craft-translation-manager", "reference": "921bea8b255cb07b3fa043148a1550756a12e5d4"}, "require": {"craftcms/cms": "^4.5.0", "php": ">=8.0.2"}, "require-dev": {"craftcms/ecs": "dev-main", "craftcms/phpstan": "dev-main"}, "type": "craft-plugin", "extra": {"handle": "_craft-translation-manager", "name": "Craft Translation Manager", "developer": "kldev", "documentationUrl": ""}, "installation-source": "dist", "autoload": {"psr-4": {"kldev\\translationmanager\\": "src/"}}, "scripts": {"check-cs": ["ecs check --ansi"], "fix-cs": ["ecs check --ansi --fix"], "phpstan": ["phpstan --memory-limit=1G"]}, "description": "A plugin for static message translation in cp", "transport-options": {"relative": true}, "install-path": "../kldev/craft-translation-manager"}, {"name": "league/html-to-markdown", "version": "5.1.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/html-to-markdown/zipball/0b4066eede55c48f38bcee4fb8f0aa85654390fd", "reference": "0b4066eede55c48f38bcee4fb8f0aa85654390fd", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": "^7.2.5 || ^8.0"}, "time": "2023-07-12T21:21:09+00:00", "bin": ["bin/html-to-markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.2-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"League\\HTMLToMarkdown\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://modernnerd.net", "role": "Original Author"}], "description": "An HTML-to-markdown conversion helper for PHP", "homepage": "https://github.com/thephpleague/html-to-markdown", "keywords": ["html", "markdown"], "install-path": "../league/html-to-markdown"}, {"name": "lsolesen/pel", "version": "0.9.12", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/pel/pel/zipball/b95fe29cdacf9d36330da277f10910a13648c84c", "reference": "b95fe29cdacf9d36330da277f10910a13648c84c", "shasum": ""}, "require": {"php": ">=7.1.0"}, "time": "2022-02-18T13:20:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"lsolesen\\pel\\": "src/"}}, "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://intraface.dk", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://geisler.net", "role": "Developer"}], "description": "PHP Exif Library. A library for reading and writing Exif headers in JPEG and TIFF images using PHP.", "homepage": "http://pel.github.com/pel/", "keywords": ["exif", "image"], "install-path": "../lsolesen/pel"}, {"name": "mailchimp/marketing", "version": "3.0.80", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/mailchimp/mailchimp-marketing-php/zipball/c1a38f7248d8de7de412418fed8dae759b9e4b97", "reference": "c1a38f7248d8de7de412418fed8dae759b9e4b97", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/guzzle": "^6.4 || ^7.2", "guzzlehttp/psr7": "^1.7 || ^2.0", "php": ">=7.2"}, "time": "2022-11-02T19:19:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MailchimpMarketing\\": "lib/"}}, "license": ["proprietary"], "authors": [{"name": "Mailchimp", "homepage": "https://github.com/mailchimp/mailchimp-marketing-php"}], "homepage": "http://swagger.io", "keywords": ["api", "php", "sdk", "swagger"], "install-path": "../mailchimp/marketing"}, {"name": "mailerlite/mailerlite-php", "version": "v1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mailerlite/mailerlite-php.git", "reference": "b33a65e4f1b28895212e825ffd8ba639501ef444"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mailerlite/mailerlite-php/zipball/b33a65e4f1b28895212e825ffd8ba639501ef444", "reference": "b33a65e4f1b28895212e825ffd8ba639501ef444", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4|^8.0", "php-http/client-common": "^2.2", "php-http/discovery": "^1.9", "php-http/httplug": "^2.1", "psr/http-client-implementation": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.18", "guzzlehttp/psr7": "^2.6.1", "http-interop/http-factory-guzzle": "^1.0", "mockery/mockery": "^0.9.4", "php-http/guzzle7-adapter": "^0.1", "php-http/message": "^1.0", "php-http/mock-client": "^1.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^7.5.15 || ^8.4 || ^9.0"}, "time": "2024-02-13T14:18:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"MailerLite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "MailerLite PHP SDK", "homepage": "https://github.com/mailerlite/mailerlite-php", "keywords": ["email", "mailerlite", "marketing", "sdk"], "support": {"issues": "https://github.com/mailerlite/mailerlite-php/issues", "source": "https://github.com/mailerlite/mailerlite-php/tree/v1.0.3"}, "install-path": "../mailerlite/mailerlite-php"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "time": "2024-11-28T04:54:44+00:00", "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "install-path": "../marc-mabe/php-enum"}, {"name": "mikehaertl/php-shellcommand", "version": "1.7.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/e79ea528be155ffdec6f3bf1a4a46307bb49e545", "reference": "e79ea528be155ffdec6f3bf1a4a46307bb49e545", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "time": "2023-04-19T08:25:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"], "install-path": "../mikehaertl/php-shellcommand"}, {"name": "moneyphp/money", "version": "v4.4.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/5e60aebf09f709dd4ea16bf85e66d65301c0d172", "reference": "5e60aebf09f709dd4ea16bf85e66d65301c0d172", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-filter": "*", "ext-json": "*", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "time": "2024-01-24T08:29:16+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Money\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "install-path": "../moneyphp/money"}, {"name": "monolog/monolog", "version": "2.10.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/5cf826f2991858b54d5c3809bee745560a1042a7", "reference": "5cf826f2991858b54d5c3809bee745560a1042a7", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2024-11-12T12:43:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "install-path": "../monolog/monolog"}, {"name": "nette/php-generator", "version": "v4.1.5", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/690b00d81d42d5633e4457c43ef9754573b6f9d6", "reference": "690b00d81d42d5633e4457c43ef9754573b6f9d6", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.3"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "time": "2024-05-12T17:31:02+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.3 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "install-path": "../nette/php-generator"}, {"name": "nette/utils", "version": "v4.0.4", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "reference": "d3ad0aa3b9f934602cb3e3902ebccf10be34d218", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "time": "2024-01-17T16:50:36+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "install-path": "../nette/utils"}, {"name": "nikic/php-parser", "version": "v4.19.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/4e1b88d21c69391150ace211e9eaf05810858d0b", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "time": "2024-03-17T08:10:35+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "install-path": "../nikic/php-parser"}, {"name": "nystudio107/craft-code-editor", "version": "1.0.22", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-code-editor/zipball/170edf71355b659e1db9ede12980b17c20eb3d1f", "reference": "170edf71355b659e1db9ede12980b17c20eb3d1f", "shasum": ""}, "require": {"craftcms/cms": "^3.0.0 || ^4.0.0 || ^5.0.0", "phpdocumentor/reflection-docblock": "^5.0.0"}, "time": "2024-09-23T17:20:25+00:00", "type": "yii2-extension", "extra": {"bootstrap": "nystudio107\\codeeditor\\CodeEditor"}, "installation-source": "dist", "autoload": {"psr-4": {"nystudio107\\codeeditor\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com"}], "description": "Provides a code editor field with Twig & Craft API autocomplete", "keywords": ["Craft", "Monaco", "cms", "code", "craftcms", "css", "editor", "javascript", "markdown", "twig"], "install-path": "../nystudio107/craft-code-editor"}, {"name": "nystudio107/craft-twigpack", "version": "4.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/nystudio107/craft-twigpack/zipball/210f0f36728761380f51d5fde4092582efc4021d", "reference": "210f0f36728761380f51d5fde4092582efc4021d", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0"}, "time": "2024-04-06T03:04:42+00:00", "type": "craft-plugin", "extra": {"class": "nystudio107\\twigpack\\Twigpack", "handle": "twigpack", "name": "Twigpack"}, "installation-source": "dist", "autoload": {"psr-4": {"nystudio107\\twigpack\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "nystudio107", "homepage": "https://nystudio107.com/"}], "description": "Twigpack is a bridge between Twig and webpack, with manifest.json & webpack-dev-server HMR support", "keywords": ["craft-plugin", "craftcms", "hmr-support", "manifest-json", "twig", "webpack", "webpack-dev-server"], "support": {"docs": "https://nystudio107.com/docs/twigpack/", "issues": "https://nystudio107.com/plugins/twigpack/support", "source": "https://github.com/nystudio107/craft-twigpack"}, "install-path": "../nystudio107/craft-twigpack"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "version_normalized": "**********", "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2020-10-15T08:29:30+00:00", "type": "library", "installation-source": "dist", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "install-path": "../paragonie/random_compat"}, {"name": "php-http/client-common", "version": "2.7.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/1e19c059b0e4d5f717bf5d524d616165aeab0612", "reference": "1e19c059b0e4d5f717bf5d524d616165aeab0612", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0 || ^7.0", "symfony/polyfill-php80": "^1.17"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "time": "2023-11-30T10:31:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "install-path": "../php-http/client-common"}, {"name": "php-http/discovery", "version": "1.19.4", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/0700efda8d7526335132360167315fdab3aeb599", "reference": "0700efda8d7526335132360167315fdab3aeb599", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "time": "2024-03-29T13:00:05+00:00", "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "installation-source": "dist", "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "install-path": "../php-http/discovery"}, {"name": "php-http/httplug", "version": "2.4.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-04-14T15:10:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "install-path": "../php-http/httplug"}, {"name": "php-http/message", "version": "1.16.1", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/5997f3289332c699fa2545c427826272498a2088", "reference": "5997f3289332c699fa2545c427826272498a2088", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "time": "2024-03-07T13:22:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "install-path": "../php-http/message"}, {"name": "php-http/promise", "version": "1.3.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "time": "2024-03-15T13:55:21+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "install-path": "../php-http/promise"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2020-06-27T09:03:43+00:00", "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "install-path": "../phpdocumentor/reflection-common"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "time": "2025-04-13T19:20:35+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "install-path": "../phpdocumentor/reflection-docblock"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "time": "2024-11-09T15:12:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "install-path": "../phpdocumentor/type-resolver"}, {"name": "phpoption/phpoption", "version": "1.9.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "time": "2024-07-20T21:41:07+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "install-path": "../phpoption/phpoption"}, {"name": "phpstan/phpdoc-parser", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "reference": "9b30d6fd026b2c132b3985ce6b23bec09ab3aa68", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "time": "2025-02-19T13:28:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.1.0"}, "install-path": "../phpstan/phpdoc-parser"}, {"name": "pixelandtonic/imagine", "version": "*******", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/pixelandtonic/Imagine/zipball/4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "reference": "4d9bb596ff60504e37ccf9103c0bb705dba7fec6", "shasum": ""}, "require": {"php": ">=5.5"}, "suggest": {"ext-exif": "to read EXIF metadata", "ext-gd": "to use the GD implementation", "ext-gmagick": "to use the Gmagick implementation", "ext-imagick": "to use the Imagick implementation"}, "time": "2023-01-03T19:18:06+00:00", "type": "library", "extra": {"branch-alias": {"dev-develop": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Imagine\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://avalanche123.com"}], "description": "Image processing for PHP 5.3", "homepage": "http://imagine.readthedocs.org/", "keywords": ["drawing", "graphics", "image manipulation", "image processing"], "install-path": "../pixelandtonic/imagine"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.1.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "time": "2024-04-15T12:06:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:54:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "install-path": "../psr/simple-cache"}, {"name": "psy/psysh", "version": "v0.11.22", "version_normalized": "*********", "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/128fa1b608be651999ed9789c95e6e2a31b5802b", "reference": "128fa1b608be651999ed9789c95e6e2a31b5802b", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^4.0 || ^3.1", "php": "^8.0 || ^7.0.8", "symfony/console": "^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history."}, "time": "2023-10-14T21:56:36+00:00", "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-0.11": "0.11.x-dev"}, "bamarni-bin": {"bin-links": false, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "install-path": "../psy/psysh"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "install-path": "../ralouphie/getallheaders"}, {"name": "react/promise", "version": "v3.2.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "time": "2024-05-24T10:39:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "install-path": "../react/promise"}, {"name": "samdark/yii2-psr-log-target", "version": "1.1.4", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/samdark/yii2-psr-log-target/zipball/5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "reference": "5f14f21d5ee4294fe9eb3e723ec8a3908ca082ea", "shasum": ""}, "require": {"psr/log": "~1.0.2|~1.1.0|~3.0.0", "yiisoft/yii2": "~2.0.0"}, "time": "2023-11-23T14:11:29+00:00", "type": "yii2-extension", "installation-source": "dist", "autoload": {"psr-4": {"samdark\\log\\": "src", "samdark\\log\\tests\\": "tests"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Yii 2 log target which uses PSR-3 compatible logger", "homepage": "https://github.com/samdark/yii2-psr-log-target", "keywords": ["extension", "log", "psr-3", "yii"], "install-path": "../samdark/yii2-psr-log-target"}, {"name": "seld/cli-prompt", "version": "1.0.4", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/cli-prompt/zipball/b8dfcf02094b8c03b40322c229493bb2884423c5", "reference": "b8dfcf02094b8c03b40322c229493bb2884423c5", "shasum": ""}, "require": {"php": ">=5.3"}, "time": "2020-12-15T21:32:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Seld\\CliPrompt\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Allows you to prompt for user input on the command line, and optionally hide the characters they type", "keywords": ["cli", "console", "hidden", "input", "prompt"], "install-path": "../seld/cli-prompt"}, {"name": "seld/jsonlint", "version": "1.11.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/1748aaf847fc731cfad7725aec413ee46f0cc3a2", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "time": "2024-07-11T14:55:45+00:00", "bin": ["bin/jsonlint"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "install-path": "../seld/jsonlint"}, {"name": "seld/phar-utils", "version": "1.2.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "time": "2022-08-31T10:31:18+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "install-path": "../seld/phar-utils"}, {"name": "seld/signal-handler", "version": "2.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2023-09-03T09:24:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "install-path": "../seld/signal-handler"}, {"name": "skynettechnologies/craft-allinoneaccessibility", "version": "2.2.4", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/skynetindia/craft-allinoneaccessibility/zipball/8b7892a7dea0ddf8a57c041b51decb97312bac0f", "reference": "8b7892a7dea0ddf8a57c041b51decb97312bac0f", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0"}, "time": "2025-01-22T12:54:58+00:00", "type": "craft-plugin", "extra": {"name": "All in One Accessibility™", "handle": "allinone-accessibility", "class": "skynettechnologies\\craftallinoneaccessibility\\CraftAllinoneaccessibility"}, "installation-source": "dist", "autoload": {"psr-4": {"skynettechnologies\\craftallinoneaccessibility\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "Skynet Technologies USA LLC"}], "description": "All in One Accessibility widget improves website ADA compliance and browser experience for ADA, WCAG 2.0, 2.1 & 2.2, Section 508, Australian DDA, European EAA EN 301 549, UK Equality Act (EA), Israeli Standard 5568, California Unruh, Ontario AODA, Canada ACA, German BITV, France RGAA, Brazilian Inclusion Law (LBI 13.146/2015), Spain UNE 139803:2012, JIS X 8341 (Japan), Italian Stanca Act and Switzerland DDA Standards. All in One Accessibility Widget complies with GDPR, COPPA regulations.", "keywords": ["ADA", "ATAG", "Australian DDA", "California Unruh", "Canada ACA", "European EAA EN 301 549", "France RGAA", "German BITV", "Israeli Standard 5568", "Ontario AODA", "Section 508", "UK Equality Act", "WCAG", "WCAG 2.1", "accessibe alternative", "accessibility compliance", "accessibility plugin", "accessibility rules", "accessibility widget", "accessible website", "compliance", "craft", "digital accessibility compliance", "digital accessibility plugin", "digital accessibility widget", "disability friendly site", "userways alternative", "web accessibility", "web accessibility compliance", "web accessibility plugin", "web accessibility widget"], "install-path": "../skynettechnologies/craft-allinoneaccessibility"}, {"name": "symfony/console", "version": "v5.4.47", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "reference": "c4ba980ca61a9eb18ee6bcc73f28e475852bb1ed", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "time": "2024-11-06T11:30:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "install-path": "../symfony/console"}, {"name": "symfony/deprecation-contracts", "version": "v3.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "shasum": ""}, "require": {"php": ">=8.0.2"}, "time": "2022-01-02T09:55:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/event-dispatcher", "version": "v6.0.19", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "reference": "2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/event-dispatcher-contracts": "^2|^3"}, "conflict": {"symfony/dependency-injection": "<5.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "time": "2023-01-01T08:36:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "time": "2022-01-02T09:55:41+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/filesystem", "version": "v5.4.45", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/57c8294ed37d4a055b77057827c67f9558c95c54", "reference": "57c8294ed37d4a055b77057827c67f9558c95c54", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "time": "2024-10-22T13:05:35+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "install-path": "../symfony/filesystem"}, {"name": "symfony/finder", "version": "v5.4.45", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/63741784cd7b9967975eec610b256eed3ede022b", "reference": "63741784cd7b9967975eec610b256eed3ede022b", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-php80": "^1.16"}, "time": "2024-09-28T13:32:08+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "install-path": "../symfony/finder"}, {"name": "symfony/http-client", "version": "v6.0.20", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/541c04560da1875f62c963c3aab6ea12a7314e11", "reference": "541c04560da1875f62c963c3aab6ea12a7314e11", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "time": "2023-01-30T15:41:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "install-path": "../symfony/http-client"}, {"name": "symfony/http-client-contracts", "version": "v3.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/4184b9b63af1edaf35b6a7974c6f1f9f33294129", "reference": "4184b9b63af1edaf35b6a7974c6f1f9f33294129", "shasum": ""}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/http-client-implementation": ""}, "time": "2022-04-12T16:11:42+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "install-path": "../symfony/http-client-contracts"}, {"name": "symfony/mailer", "version": "v6.0.19", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/cd60799210c488f545ddde2444dc1aa548322872", "reference": "cd60799210c488f545ddde2444dc1aa548322872", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.0.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<5.4"}, "time": "2023-01-11T11:50:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "install-path": "../symfony/mailer"}, {"name": "symfony/mime", "version": "v6.0.19", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/d7052547a0070cbeadd474e172b527a00d657301", "reference": "d7052547a0070cbeadd474e172b527a00d657301", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<5.4.14|>=6.0,<6.0.14|>=6.1,<6.1.6"}, "time": "2023-01-11T11:50:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "install-path": "../symfony/mime"}, {"name": "symfony/options-resolver", "version": "v6.0.19", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/6a180d1c45e0d9797470ca9eb46215692de00fa3", "reference": "6a180d1c45e0d9797470ca9eb46215692de00fa3", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.1|^3"}, "time": "2023-01-01T08:36:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "install-path": "../symfony/options-resolver"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-iconv", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/48becf00c920479ca2e910c22a5a39e5d47ca956", "reference": "48becf00c920479ca2e910c22a5a39e5d47ca956", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-iconv"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-intl-grapheme"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/c36586dcf89a12315939e00ec9b4474adcb1d773", "reference": "c36586dcf89a12315939e00ec9b4474adcb1d773", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php72", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "metapackage", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "install-path": null}, {"name": "symfony/polyfill-php73", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-php73"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-php80"}, {"name": "symfony/polyfill-php81", "version": "v1.31.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "reference": "4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "install-path": "../symfony/polyfill-php81"}, {"name": "symfony/process", "version": "v5.4.47", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5d1662fb32ebc94f17ddb8d635454a776066733d", "reference": "5d1662fb32ebc94f17ddb8d635454a776066733d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-php80": "^1.16"}, "time": "2024-11-06T11:36:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "install-path": "../symfony/process"}, {"name": "symfony/service-contracts", "version": "v3.0.2", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/container": "^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "time": "2022-05-30T19:17:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "install-path": "../symfony/service-contracts"}, {"name": "symfony/string", "version": "v6.0.19", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/d9e72497367c23e08bf94176d2be45b00a9d232a", "reference": "d9e72497367c23e08bf94176d2be45b00a9d232a", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "time": "2023-01-01T08:36:10+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "install-path": "../symfony/string"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/42f18f170aa86d612c3559cfb3bd11a375df32c8", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "time": "2024-11-08T15:21:10+00:00", "bin": ["Resources/bin/var-dump-server"], "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "install-path": "../symfony/var-dumper"}, {"name": "symfony/yaml", "version": "v5.4.45", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/a454d47278cc16a5db371fe73ae66a78a633371e", "reference": "a454d47278cc16a5db371fe73ae66a78a633371e", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.3"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "time": "2024-09-25T14:11:13+00:00", "bin": ["Resources/bin/yaml-lint"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "install-path": "../symfony/yaml"}, {"name": "theiconic/name-parser", "version": "v1.2.11", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/theiconic/name-parser/zipball/9a54a713bf5b2e7fd990828147d42de16bf8a253", "reference": "9a54a713bf5b2e7fd990828147d42de16bf8a253", "shasum": ""}, "require": {"php": ">=7.1"}, "time": "2019-11-14T14:08:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"TheIconic\\NameParser\\": ["src/", "tests/"]}}, "license": ["MIT"], "authors": [{"name": "The Iconic", "email": "<EMAIL>"}], "description": "PHP library for parsing a string containing a full name into its parts", "install-path": "../theiconic/name-parser"}, {"name": "twig/twig", "version": "v3.15.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/2d5b3964cc21d0188633d7ddce732dc8e874db02", "reference": "2d5b3964cc21d0188633d7ddce732dc8e874db02", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php81": "^1.29"}, "time": "2024-11-17T15:59:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "install-path": "../twig/twig"}, {"name": "verbb/base", "version": "2.0.10", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/verbb-base/zipball/47a2671ef8862236b19609fe8409f11843aee1cf", "reference": "47a2671ef8862236b19609fe8409f11843aee1cf", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2"}, "time": "2024-11-13T00:05:42+00:00", "type": "yii-module", "installation-source": "dist", "autoload": {"psr-4": {"verbb\\base\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Common utilities and building-blocks for Verbb plugins for Craft CMS.", "install-path": "../verbb/base"}, {"name": "verbb/bugsnag", "version": "4.0.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/bugsnag/zipball/e5ce978f4d2c6535bd1caf9e9b215f6ddcfe2841", "reference": "e5ce978f4d2c6535bd1caf9e9b215f6ddcfe2841", "shasum": ""}, "require": {"bugsnag/bugsnag": "^3.16.0", "craftcms/cms": "^4.0.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "time": "2023-10-25T00:18:22+00:00", "type": "craft-plugin", "extra": {"name": "Bugsnag", "handle": "bugsnag", "changelogUrl": "https://raw.githubusercontent.com/verbb/bugsnag/craft-4/CHANGELOG.md", "class": "verbb\\bugsnag\\Bugsnag"}, "installation-source": "dist", "autoload": {"psr-4": {"verbb\\bugsnag\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}, {"name": "Superbig", "homepage": "https://superbig.co"}], "description": "Log errors/exceptions to Bugsnag.", "keywords": ["bugsnag", "cms", "craft", "craft-plugin", "craftcms"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/bugsnag/issues?state=open", "source": "https://github.com/verbb/bugsnag", "docs": "https://github.com/verbb/bugsnag", "rss": "https://github.com/verbb/bugsnag/commits/v2.atom"}, "install-path": "../verbb/bugsnag"}, {"name": "verbb/image-resizer", "version": "3.0.13", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/image-resizer/zipball/33ba642897780b5fb0a150abd7482a9d890918c4", "reference": "33ba642897780b5fb0a150abd7482a9d890918c4", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "lsolesen/pel": "^0.9.6", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "time": "2025-03-04T01:58:43+00:00", "type": "craft-plugin", "extra": {"name": "Image Resizer", "handle": "image-resizer", "description": "Image Resizer resizes your assets when they are uploaded.", "documentationUrl": "https://github.com/verbb/image-resizer", "changelogUrl": "https://raw.githubusercontent.com/verbb/image-resizer/craft-4/CHANGELOG.md", "class": "verbb\\imageresizer\\ImageResizer"}, "installation-source": "dist", "autoload": {"psr-4": {"verbb\\imageresizer\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Resize assets when they are uploaded.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "example"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/image-resizer/issues?state=open", "source": "https://github.com/verbb/image-resizer", "docs": "https://github.com/verbb/image-resizer", "rss": "https://github.com/verbb/image-resizer/commits/v2.atom"}, "install-path": "../verbb/image-resizer"}, {"name": "verbb/super-table", "version": "3.0.15", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/verbb/super-table/zipball/ce8830aeb0cc5e8b5065d4e19925a60ddc3f3d4a", "reference": "ce8830aeb0cc5e8b5065d4e19925a60ddc3f3d4a", "shasum": ""}, "require": {"craftcms/cms": "^4.0.0", "php": "^8.0.2", "verbb/base": "^2.0.0"}, "time": "2024-09-13T02:39:50+00:00", "type": "craft-plugin", "extra": {"name": "Super Table", "handle": "super-table", "description": "Super-charge your Craft workflow with Super Table. Use it to group fields together or build complex Matrix-in-Matrix solutions.", "documentationUrl": "https://github.com/verbb/super-table", "changelogUrl": "https://raw.githubusercontent.com/verbb/super-table/craft-4/CHANGELOG.md", "class": "verbb\\supertable\\SuperTable"}, "installation-source": "dist", "autoload": {"psr-4": {"verbb\\supertable\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://verbb.io"}], "description": "Super-charge your content builders and create nested Matrix fields.", "keywords": ["cms", "craft", "craft-plugin", "craftcms", "super table"], "support": {"email": "<EMAIL>", "issues": "https://github.com/verbb/super-table/issues?state=open", "source": "https://github.com/verbb/super-table", "docs": "https://github.com/verbb/super-table", "rss": "https://github.com/verbb/super-table/commits/v2.atom"}, "install-path": "../verbb/super-table"}, {"name": "vlucas/phpdotenv", "version": "v5.6.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/a59a13791077fe3d44f90e7133eb68e7d22eaff2", "reference": "a59a13791077fe3d44f90e7133eb68e7d22eaff2", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "time": "2024-07-20T21:52:34+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "install-path": "../vlucas/phpdotenv"}, {"name": "voku/anti-xss", "version": "4.1.42", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/anti-xss/zipball/bca1f8607e55a3c5077483615cd93bd8f11bd675", "reference": "bca1f8607e55a3c5077483615cd93bd8f11bd675", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-utf8": "~6.0.2"}, "time": "2023-07-03T14:40:46+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "EllisLab Dev Team", "homepage": "http://ellislab.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/"}], "description": "anti xss-library", "homepage": "https://github.com/voku/anti-xss", "keywords": ["anti-xss", "clean", "security", "xss"], "install-path": "../voku/anti-xss"}, {"name": "voku/arrayy", "version": "7.9.6", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Arrayy/zipball/0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "reference": "0e20b8c6eef7fc46694a2906e0eae2f9fc11cade", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.0.0", "phpdocumentor/reflection-docblock": "~4.3 || ~5.0", "symfony/polyfill-mbstring": "~1.0"}, "time": "2022-12-27T12:58:32+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Create.php"], "psr-4": {"Arrayy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Maintainer"}], "description": "Array manipulation library for PHP, called Arrayy!", "keywords": ["A<PERSON>yy", "array", "helpers", "manipulation", "methods", "utility", "utils"], "install-path": "../voku/arrayy"}, {"name": "voku/email-check", "version": "3.1.0", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/email-check/zipball/6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "reference": "6ea842920bbef6758b8c1e619fd1710e7a1a2cac", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-intl-idn": "~1.10"}, "suggest": {"ext-intl": "Use Intl for best performance"}, "time": "2021-01-27T14:14:33+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "email-check (syntax, dns, trash, ...) library", "homepage": "https://github.com/voku/email-check", "keywords": ["check-email", "email", "mail", "mail-check", "validate-email", "validate-email-address", "validate-mail"], "install-path": "../voku/email-check"}, {"name": "voku/portable-ascii", "version": "2.0.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "time": "2024-11-21T01:49:47+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "install-path": "../voku/portable-ascii"}, {"name": "voku/portable-utf8", "version": "6.0.13", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-utf8/zipball/b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "reference": "b8ce36bf26593e5c2e81b1850ef0ffb299d2043f", "shasum": ""}, "require": {"php": ">=7.0.0", "symfony/polyfill-iconv": "~1.0", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.0", "voku/portable-ascii": "~2.0.0"}, "suggest": {"ext-ctype": "Use Ctype for e.g. hexadecimal digit detection", "ext-fileinfo": "Use Fileinfo for better binary file detection", "ext-iconv": "Use iconv for best performance", "ext-intl": "Use Intl for best performance", "ext-json": "Use JSON for string detection", "ext-mbstring": "Use Mbstring for best performance"}, "time": "2023-03-08T08:35:38+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"voku\\": "src/voku/"}}, "license": ["(Apache-2.0 or GPL-2.0)"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "http://pageconfig.com/"}, {"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable UTF-8 library - performance optimized (unicode) string functions for php.", "homepage": "https://github.com/voku/portable-utf8", "keywords": ["UTF", "clean", "php", "unicode", "utf-8", "utf8"], "install-path": "../voku/portable-utf8"}, {"name": "voku/stop-words", "version": "2.0.1", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/stop-words/zipball/8e63c0af20f800b1600783764e0ce19e53969f71", "reference": "8e63c0af20f800b1600783764e0ce19e53969f71", "shasum": ""}, "require": {"php": ">=7.0.0"}, "time": "2018-11-23T01:37:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Stop-Words via PHP", "keywords": ["stop words", "stop-words"], "install-path": "../voku/stop-words"}, {"name": "voku/stringy", "version": "6.5.3", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/Stringy/zipball/c453c88fbff298f042c836ef44306f8703b2d537", "reference": "c453c88fbff298f042c836ef44306f8703b2d537", "shasum": ""}, "require": {"defuse/php-encryption": "~2.0", "ext-json": "*", "php": ">=7.0.0", "voku/anti-xss": "~4.1", "voku/arrayy": "~7.8", "voku/email-check": "~3.1", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/urlify": "~5.0"}, "replace": {"danielstjules/stringy": "~3.0"}, "time": "2022-03-28T14:52:20+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/Create.php"], "psr-4": {"Stringy\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.moelleken.org/", "role": "Fork-Maintainer"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "install-path": "../voku/stringy"}, {"name": "voku/urlify", "version": "5.0.7", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/urlify/zipball/014b2074407b5db5968f836c27d8731934b330e4", "reference": "014b2074407b5db5968f836c27d8731934b330e4", "shasum": ""}, "require": {"php": ">=7.0.0", "voku/portable-ascii": "~2.0", "voku/portable-utf8": "~6.0", "voku/stop-words": "~2.0"}, "time": "2022-01-24T19:08:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"voku\\helper\\": "src/voku/helper/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.johnnybroadway.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://moelleken.org/"}], "description": "PHP port of URLify.js from the Django project. Transliterates non-ascii characters for use in URLs.", "homepage": "https://github.com/voku/urlify", "keywords": ["encode", "iconv", "link", "slug", "translit", "transliterate", "transliteration", "url", "urlify"], "install-path": "../voku/urlify"}, {"name": "webmozart/assert", "version": "1.11.0", "version_normalized": "********", "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "time": "2022-06-03T18:03:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "install-path": "../webmozart/assert"}, {"name": "webonyx/graphql-php", "version": "v14.11.10", "version_normalized": "**********", "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/d9c2fdebc6aa01d831bc2969da00e8588cffef19", "reference": "d9c2fdebc6aa01d831bc2969da00e8588cffef19", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.1 || ^8"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "time": "2023-07-05T14:23:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "install-path": "../webonyx/graphql-php"}, {"name": "yiisoft/yii2", "version": "2.0.52", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "540e7387d934c52e415614aa081fb38d04c72d9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/540e7387d934c52e415614aa081fb38d04c72d9a", "reference": "540e7387d934c52e415614aa081fb38d04c72d9a", "shasum": ""}, "require": {"bower-asset/inputmask": "^5.0.8 ", "bower-asset/jquery": "3.7.*@stable | 3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "^1.4", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "^4.17", "lib-pcre": "*", "php": ">=7.3.0", "yiisoft/yii2-composer": "~2.0.4"}, "time": "2025-02-13T20:02:28+00:00", "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "support": {"forum": "https://forum.yiiframework.com/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2/issues?state=open", "source": "https://github.com/yiisoft/yii2", "wiki": "https://www.yiiframework.com/wiki"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "install-path": "../yiisoft/yii2"}, {"name": "yiisoft/yii2-composer", "version": "2.0.11", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/b684b01ecb119c8287721def726a0e24fec2fef2", "reference": "b684b01ecb119c8287721def726a0e24fec2fef2", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "time": "2025-02-13T20:59:36+00:00", "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "install-path": "../yiisoft/yii2-composer"}, {"name": "yiisoft/yii2-debug", "version": "2.1.26", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "e4b28a1d295fc977d8399db544336dd5b2764397"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/e4b28a1d295fc977d8399db544336dd5b2764397", "reference": "e4b28a1d295fc977d8399db544336dd5b2764397", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.4", "yiisoft/yii2": "~2.0.13"}, "require-dev": {"cweagans/composer-patches": "^1.7", "phpunit/phpunit": "4.8.34", "yiisoft/yii2-coding-standards": "~2.0", "yiisoft/yii2-swiftmailer": "*"}, "time": "2025-02-13T21:27:29+00:00", "type": "yii2-extension", "extra": {"patches": {"phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch", "Fix PHP 8.1 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php81.patch"}, "phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}}, "branch-alias": {"dev-master": "2.0.x-dev"}, "composer-exit-on-patch-failure": true}, "installation-source": "dist", "autoload": {"psr-4": {"yii\\debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "dev", "yii2"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/yii2-debug/issues", "source": "https://github.com/yiisoft/yii2-debug", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-debug", "type": "tidelift"}], "install-path": "../yiisoft/yii2-debug"}, {"name": "yiisoft/yii2-queue", "version": "2.3.7", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "reference": "dbc9d4a7b2a6995cd19c3e334227482ef55e559b", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0||^6.0||^7.0", "yiisoft/yii2": "~2.0.14"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "time": "2024-04-29T09:40:52+00:00", "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "composer-exit-on-patch-failure": true, "patches": {"phpunit/phpunit-mock-objects": {"Fix PHP 7 and 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_mock_objects.patch"}, "phpunit/phpunit": {"Fix PHP 7 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php7.patch", "Fix PHP 8 compatibility": "https://yiisoft.github.io/phpunit-patches/phpunit_php8.patch"}}}, "installation-source": "dist", "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\stomp\\": "src/drivers/stomp", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "install-path": "../yiisoft/yii2-queue"}, {"name": "yiisoft/yii2-shell", "version": "2.0.5", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-shell/zipball/358d4651ce1f54db0f1add026c202ac2e47db06b", "reference": "358d4651ce1f54db0f1add026c202ac2e47db06b", "shasum": ""}, "require": {"psy/psysh": "~0.9.3|~0.10.3|^0.11.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0", "yiisoft/yii2": "~2.0.0"}, "time": "2022-09-04T10:37:52+00:00", "type": "yii2-extension", "extra": {"bootstrap": "yii\\shell\\Bootstrap", "branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"yii\\shell\\": ""}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "svku<PERSON><PERSON>@gmail.com"}], "description": "The interactive shell extension for Yii framework", "keywords": ["shell", "yii2"], "install-path": "../yiisoft/yii2-shell"}, {"name": "yiisoft/yii2-symfonymailer", "version": "2.0.4", "version_normalized": "*******", "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-symfonymailer/zipball/82f5902551a160633c4734b5096977ce76a809d9", "reference": "82f5902551a160633c4734b5096977ce76a809d9", "shasum": ""}, "require": {"php": ">=7.4.0", "symfony/mailer": ">=5.4.0", "yiisoft/yii2": ">=2.0.4"}, "time": "2022-09-04T10:48:21+00:00", "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"yii\\symfonymailer\\": "src"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The SymfonyMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "symfony", "symfonymailer", "yii2"], "install-path": "../yiisoft/yii2-symfonymailer"}], "dev": true, "dev-package-names": ["craftcms/generator", "nette/php-generator", "nette/utils", "nikic/php-parser", "psy/psysh", "yiisoft/yii2-shell"]}