<?php

$vendorDir = dirname(__DIR__);
$rootDir = dirname(dirname(__DIR__));

return array (
  'kldev/craft-ai-tools' => 
  array (
    'class' => 'kldev\\craftaitools\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-ai-tools/src',
    'handle' => '_craft-ai-tools',
    'aliases' => 
    array (
      '@kldev/craftaitools' => $vendorDir . '/kldev/craft-ai-tools/src',
    ),
    'name' => 'AI Content Tools',
    'version' => 'dev-master',
    'description' => 'a bundle of ai functionality for content creation and marketing',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-googlefonts-field-type' => 
  array (
    'class' => 'kldev\\googlefonts\\GoogleFonts',
    'basePath' => $vendorDir . '/kldev/craft-googlefonts-field-type/src',
    'handle' => '_craft-googlefonts-field-type',
    'aliases' => 
    array (
      '@kldev/googlefonts' => $vendorDir . '/kldev/craft-googlefonts-field-type/src',
    ),
    'name' => 'Google Fonts Field',
    'version' => 'dev-master',
    'description' => 'adds a googlefonts field to choose a google font with preview',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-iconify' => 
  array (
    'class' => 'kldev\\iconify\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-iconify/src',
    'handle' => '_craft-iconify',
    'aliases' => 
    array (
      '@kldev/iconify' => $vendorDir . '/kldev/craft-iconify/src',
    ),
    'name' => 'Iconify Extension',
    'version' => 'dev-master',
    'description' => 'adds a custom field to select icon set and changes the icons in templates accordingly',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-kldev-bundle' => 
  array (
    'class' => 'kldev\\bundle\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-kldev-bundle/src',
    'handle' => '_kldev-bundle',
    'aliases' => 
    array (
      '@kldev/bundle' => $vendorDir . '/kldev/craft-kldev-bundle/src',
    ),
    'name' => 'Kldev Bundle',
    'version' => 'dev-main',
    'description' => 'bundle for all shared logic accross customer craft cms',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-og-tag-parser' => 
  array (
    'class' => 'ogtagparser\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-og-tag-parser/src',
    'handle' => '_craft-og-tag-parser',
    'aliases' => 
    array (
      '@ogtagparser' => $vendorDir . '/kldev/craft-og-tag-parser/src',
    ),
    'name' => 'craft-og-tag-parser',
    'version' => 'dev-main',
    'description' => 'parses og tags from urls and makes them accessible to twig templates',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-section-select-field' => 
  array (
    'class' => 'kldev\\sectionselect\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-section-select-field/src',
    'handle' => '_craft-section-select-field',
    'aliases' => 
    array (
      '@kldev/sectionselect' => $vendorDir . '/kldev/craft-section-select-field/src',
    ),
    'name' => 'CraftSectionSelectField',
    'version' => 'dev-main',
    'description' => 'select fields for all sections added in the cms',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-translation-manager' => 
  array (
    'class' => 'kldev\\translationmanager\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-translation-manager/src',
    'handle' => '_craft-translation-manager',
    'aliases' => 
    array (
      '@kldev/translationmanager' => $vendorDir . '/kldev/craft-translation-manager/src',
    ),
    'name' => 'Craft Translation Manager',
    'version' => 'dev-master',
    'description' => 'A plugin for static message translation in cp',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'adigital/cookie-consent-banner' => 
  array (
    'class' => 'adigital\\cookieconsentbanner\\CookieConsentBanner',
    'basePath' => $vendorDir . '/adigital/cookie-consent-banner/src',
    'handle' => 'cookie-consent-banner',
    'aliases' => 
    array (
      '@adigital/cookieconsentbanner' => $vendorDir . '/adigital/cookie-consent-banner/src',
    ),
    'name' => 'Cookie Consent Banner',
    'version' => '2.0.1',
    'description' => 'Add a configurable cookie consent banner to the website.',
    'developer' => 'Mark @ A Digital',
    'developerUrl' => 'https://adigital.agency',
    'documentationUrl' => 'https://github.com/a-digital/cookie-consent-banner/blob/master/README.md',
    'changelogUrl' => 'https://github.com/a-digital/cookie-consent-banner/blob/master/CHANGELOG.md',
    'hasCpSettings' => true,
    'hasCpSection' => false,
  ),
  'codemonauts/craft-instagram-feed' => 
  array (
    'class' => 'codemonauts\\instagramfeed\\InstagramFeed',
    'basePath' => $vendorDir . '/codemonauts/craft-instagram-feed/src',
    'handle' => 'instagramfeed',
    'aliases' => 
    array (
      '@codemonauts/instagramfeed' => $vendorDir . '/codemonauts/craft-instagram-feed/src',
    ),
    'name' => 'Instagram Feed',
    'version' => '2.2.1',
    'description' => 'Craft CMS plugin to receive Instagram feed data as variable in templates.',
    'developer' => 'codemonauts',
    'developerUrl' => 'https://codemonauts.com',
    'documentationUrl' => 'https://plugins.codemonauts.com/plugins/instagramfeed/Introduction.html',
  ),
  'nystudio107/craft-twigpack' => 
  array (
    'class' => 'nystudio107\\twigpack\\Twigpack',
    'basePath' => $vendorDir . '/nystudio107/craft-twigpack/src',
    'handle' => 'twigpack',
    'aliases' => 
    array (
      '@nystudio107/twigpack' => $vendorDir . '/nystudio107/craft-twigpack/src',
    ),
    'name' => 'Twigpack',
    'version' => '4.0.0',
    'description' => 'Twigpack is a bridge between Twig and webpack, with manifest.json & webpack-dev-server HMR support',
    'developer' => 'nystudio107',
    'developerUrl' => 'https://nystudio107.com/',
    'documentationUrl' => 'https://nystudio107.com/docs/twigpack/',
  ),
  'jalendport/craft-preparse' => 
  array (
    'class' => 'jalendport\\preparse\\PreparseField',
    'basePath' => $vendorDir . '/jalendport/craft-preparse/src',
    'handle' => 'preparse-field',
    'aliases' => 
    array (
      '@jalendport/preparse' => $vendorDir . '/jalendport/craft-preparse/src',
      '@aelvan/preparsefield' => $vendorDir . '/jalendport/craft-preparse/src',
      '@besteadfast/preparsefield' => $vendorDir . '/jalendport/craft-preparse/src',
    ),
    'name' => 'Preparse',
    'version' => '2.1.2',
    'schemaVersion' => '1.1.0',
    'description' => 'A fieldtype that parses Twig when an element is saved and saves the result as plain text.',
    'developer' => 'Jalen Davenport',
    'developerUrl' => 'https://jalendport.com/',
    'documentationUrl' => 'https://github.com/jalendport/craft-preparse/blob/master/README.md',
    'changelogUrl' => 'https://github.com/jalendport/craft-preparse/blob/master/CHANGELOG.md',
    'hasCpSettings' => false,
    'hasCpSection' => false,
    'components' => 
    array (
      'preparseFieldService' => 'jalendport\\preparse\\services\\PreparseFieldService',
    ),
  ),
  'kldev/craft-marketing-analytics' => 
  array (
    'class' => 'kldev\\analytics\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-marketing-analytics/src',
    'handle' => '_marketing-analytics',
    'aliases' => 
    array (
      '@kldev/analytics' => $vendorDir . '/kldev/craft-marketing-analytics/src',
    ),
    'name' => 'Marketing Analytics',
    'version' => 'dev-main',
    'description' => 'plugin for marketing analytics services',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-deepl-translation' => 
  array (
    'class' => 'kldev\\deepl\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-deepl-translation/src',
    'handle' => '_craft-deepl-translation',
    'aliases' => 
    array (
      '@kldev/deepl' => $vendorDir . '/kldev/craft-deepl-translation/src',
    ),
    'name' => 'DeepL Translation',
    'version' => 'dev-main',
    'description' => 'Automated translation of pages, posts and blocks to target languages',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'verbb/bugsnag' => 
  array (
    'class' => 'verbb\\bugsnag\\Bugsnag',
    'basePath' => $vendorDir . '/verbb/bugsnag/src',
    'handle' => 'bugsnag',
    'aliases' => 
    array (
      '@verbb/bugsnag' => $vendorDir . '/verbb/bugsnag/src',
    ),
    'name' => 'Bugsnag',
    'version' => '4.0.1',
    'description' => 'Log errors/exceptions to Bugsnag.',
    'developer' => 'Verbb',
    'developerUrl' => 'https://verbb.io',
    'developerEmail' => '<EMAIL>',
    'documentationUrl' => 'https://github.com/verbb/bugsnag',
    'changelogUrl' => 'https://raw.githubusercontent.com/verbb/bugsnag/craft-4/CHANGELOG.md',
  ),
  'verbb/super-table' => 
  array (
    'class' => 'verbb\\supertable\\SuperTable',
    'basePath' => $vendorDir . '/verbb/super-table/src',
    'handle' => 'super-table',
    'aliases' => 
    array (
      '@verbb/supertable' => $vendorDir . '/verbb/super-table/src',
    ),
    'name' => 'Super Table',
    'version' => '3.0.15',
    'description' => 'Super-charge your Craft workflow with Super Table. Use it to group fields together or build complex Matrix-in-Matrix solutions.',
    'developer' => 'Verbb',
    'developerUrl' => 'https://verbb.io',
    'developerEmail' => '<EMAIL>',
    'documentationUrl' => 'https://github.com/verbb/super-table',
    'changelogUrl' => 'https://raw.githubusercontent.com/verbb/super-table/craft-4/CHANGELOG.md',
  ),
  'craftpulse/craft-colour-swatches' => 
  array (
    'class' => 'percipiolondon\\colourswatches\\ColourSwatches',
    'basePath' => $vendorDir . '/craftpulse/craft-colour-swatches/src',
    'handle' => 'colour-swatches',
    'aliases' => 
    array (
      '@percipiolondon/colourswatches' => $vendorDir . '/craftpulse/craft-colour-swatches/src',
    ),
    'name' => 'Colour Swatches',
    'version' => '4.5.3',
    'description' => 'Adding custom selectable colour palettes, gradients, classes and more to your field types.',
    'developer' => 'craftpulse',
    'developerUrl' => 'https://github.com/craftpulse',
    'developerEmail' => '<EMAIL>',
    'documentationUrl' => 'https://github.com/craftpulse/craft-colour-swatches/blob/v4/README.md',
    'changelogUrl' => 'https://github.com/craftpulse/craft-colour-swatches/blob/v4/CHANGELOG.md',
  ),
  'skynettechnologies/craft-allinoneaccessibility' => 
  array (
    'class' => 'skynettechnologies\\craftallinoneaccessibility\\CraftAllinoneaccessibility',
    'basePath' => $vendorDir . '/skynettechnologies/craft-allinoneaccessibility/src',
    'handle' => 'allinone-accessibility',
    'aliases' => 
    array (
      '@skynettechnologies/craftallinoneaccessibility' => $vendorDir . '/skynettechnologies/craft-allinoneaccessibility/src',
    ),
    'name' => 'All in One Accessibility™',
    'version' => '2.2.4',
    'description' => 'All in One Accessibility widget improves website ADA compliance and browser experience for ADA, WCAG 2.0, 2.1 & 2.2, Section 508, Australian DDA, European EAA EN 301 549, UK Equality Act (EA), Israeli Standard 5568, California Unruh, Ontario AODA, Canada ACA, German BITV, France RGAA, Brazilian Inclusion Law (LBI 13.146/2015), Spain UNE 139803:2012, JIS X 8341 (Japan), Italian Stanca Act and Switzerland DDA Standards. All in One Accessibility Widget complies with GDPR, COPPA regulations.',
    'developer' => 'Skynet Technologies USA LLC',
  ),
  'verbb/image-resizer' => 
  array (
    'class' => 'verbb\\imageresizer\\ImageResizer',
    'basePath' => $vendorDir . '/verbb/image-resizer/src',
    'handle' => 'image-resizer',
    'aliases' => 
    array (
      '@verbb/imageresizer' => $vendorDir . '/verbb/image-resizer/src',
    ),
    'name' => 'Image Resizer',
    'version' => '3.0.13',
    'description' => 'Image Resizer resizes your assets when they are uploaded.',
    'developer' => 'Verbb',
    'developerUrl' => 'https://verbb.io',
    'developerEmail' => '<EMAIL>',
    'documentationUrl' => 'https://github.com/verbb/image-resizer',
    'changelogUrl' => 'https://raw.githubusercontent.com/verbb/image-resizer/craft-4/CHANGELOG.md',
  ),
  'craftcms/ckeditor' => 
  array (
    'class' => 'craft\\ckeditor\\Plugin',
    'basePath' => $vendorDir . '/craftcms/ckeditor/src',
    'handle' => 'ckeditor',
    'aliases' => 
    array (
      '@craft/ckeditor' => $vendorDir . '/craftcms/ckeditor/src',
    ),
    'name' => 'CKEditor',
    'version' => '3.11.1',
    'description' => 'Edit rich text content in Craft CMS using CKEditor.',
    'developer' => 'Pixel & Tonic',
    'developerUrl' => 'https://pixelandtonic.com/',
    'developerEmail' => '<EMAIL>',
    'documentationUrl' => 'https://github.com/craftcms/ckeditor/blob/master/README.md',
  ),
  'kldev/craft-anonymous-portal' => 
  array (
    'class' => 'kldev\\anonymousportal\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-anonymous-portal/src',
    'handle' => '_craft-anonymous-portal',
    'aliases' => 
    array (
      '@kldev/anonymousportal' => $vendorDir . '/kldev/craft-anonymous-portal/src',
    ),
    'name' => 'Anonymous Portal',
    'version' => 'dev-main',
    'description' => 'Anonymous consultation portal with Matrix integration for secure visitor communication',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
  'kldev/craft-email-marketing' => 
  array (
    'class' => 'kldev\\emailmarketing\\Plugin',
    'basePath' => $vendorDir . '/kldev/craft-email-marketing/src',
    'handle' => '_craft-email-marketing',
    'aliases' => 
    array (
      '@kldev/emailmarketing' => $vendorDir . '/kldev/craft-email-marketing/src',
    ),
    'name' => 'Craft E-Mail Marketing',
    'version' => 'dev-main',
    'description' => 'plugin containing form section and integration of various email marketing services',
    'developer' => 'kldev',
    'documentationUrl' => '',
  ),
);
