{"version": 3, "file": "CraftSupportWidget.js", "mappings": "mDAIe,SAASA,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,CAAC,EACRC,EAAI,EAAGA,EAAIH,EAAKI,OAAQD,IAAK,CACpC,IAAIE,EAAOL,EAAKG,GACZG,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIP,EAAW,IAAMI,EACrBK,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBH,EAAUI,GAGbJ,EAAUI,GAAIK,MAAMC,KAAKL,GAFzBN,EAAOW,KAAKV,EAAUI,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,IAIlD,CACA,OAAON,CACT,C,gCClBA,IAAIY,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,CAMhB,EAEEC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,WAAa,EACpBC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiBhC,EAAUC,EAAMgC,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,CAAC,EAEvB,IAAIhC,EAASH,EAAaC,EAAUC,GAGpC,OAFAkC,EAAejC,GAER,SAAiBkC,GAEtB,IADA,IAAIC,EAAY,GACPjC,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,IACdkC,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,EACjB,CAOA,IANIF,EAEFD,EADAjC,EAASH,EAAaC,EAAUoC,IAGhClC,EAAS,GAEFE,EAAI,EAAGA,EAAIiC,EAAUhC,OAAQD,IAAK,CACzC,IAAIkC,EACJ,GAAsB,KADlBA,EAAWD,EAAUjC,IACZmC,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,GAC9B,CACF,CACF,CACF,CAEA,SAAS4B,EAAgBjC,GACvB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,GACdkC,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,OAEvC,KAAO,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,EACxD,CACF,CACF,CAEA,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,CACT,CAEA,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIxC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,EAExC,CAEA,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,EACpE,MAEEV,EAAeD,IACfM,EAASQ,EAAWD,KAAK,KAAMZ,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,EACtC,EAKF,OAFAK,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAOhD,MAAQsC,EAAItC,KACnBgD,EAAO/C,QAAUqC,EAAIrC,OACrB+C,EAAO9C,YAAcoC,EAAIpC,UAC3B,OAEFqC,EAAOD,EAAMU,EACf,MACER,GAEJ,CACF,CAEA,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,KACxC,GAGF,SAASV,EAAqBX,EAAciB,EAAOX,EAAQF,GACzD,IAAItC,EAAMwC,EAAS,GAAKF,EAAItC,IAE5B,GAAIkC,EAAasB,WACftB,EAAasB,WAAWC,QAAUP,EAAYC,EAAOnD,OAChD,CACL,IAAI0D,EAAUpD,SAASqD,eAAe3D,GAClC4D,EAAa1B,EAAa0B,WAC1BA,EAAWT,IAAQjB,EAAaS,YAAYiB,EAAWT,IACvDS,EAAWhE,OACbsC,EAAa2B,aAAaH,EAASE,EAAWT,IAE9CjB,EAAaG,YAAYqB,EAE7B,CACF,CAEA,SAASX,EAAYb,EAAcI,GACjC,IAAItC,EAAMsC,EAAItC,IACVC,EAAQqC,EAAIrC,MACZC,EAAYoC,EAAIpC,UAiBpB,GAfID,GACFiC,EAAa4B,aAAa,QAAS7D,GAEjCe,EAAQ+C,OACV7B,EAAa4B,aAAa7C,EAAUqB,EAAIxC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU8D,QAAQ,GAAK,MAEnDhE,GAAO,uDAAyDiE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUnE,MAAgB,OAG9HgC,EAAasB,WACftB,EAAasB,WAAWC,QAAUzD,MAC7B,CACL,KAAOkC,EAAaoC,YAClBpC,EAAaS,YAAYT,EAAaoC,YAExCpC,EAAaG,YAAY/B,SAASqD,eAAe3D,GACnD,CACF,C,uCC1NA,IAAIuE,EAAU,EAAQ,KACnBA,EAAQC,aAAYD,EAAUA,EAAQE,SACnB,iBAAZF,IAAsBA,EAAU,CAAC,CAACG,EAAO5E,GAAIyE,EAAS,MAC7DA,EAAQI,SAAQD,EAAOE,QAAUL,EAAQI,SAG/BE,EADH,SACO,WAAYN,GAAS,EAAM,CAAC,E,GCRzCO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaL,QAGrB,IAAIF,EAASI,EAAyBE,GAAY,CACjDlF,GAAIkF,EAEJJ,QAAS,CAAC,GAOX,OAHAO,EAAoBH,GAAUN,EAAQA,EAAOE,QAASG,GAG/CL,EAAOE,OACf,CCrBAG,EAAoBK,EAAI,SAASV,GAChC,IAAIW,EAASX,GAAUA,EAAOF,WAC7B,WAAa,OAAOE,EAAgB,OAAG,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAK,EAAoBO,EAAED,EAAQ,CAAEE,EAAGF,IAC5BA,CACR,ECNAN,EAAoBO,EAAI,SAASV,EAASY,GACzC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,EAAEF,EAAYC,KAASV,EAAoBW,EAAEd,EAASa,IAC5EE,OAAOC,eAAehB,EAASa,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAV,EAAoBW,EAAI,SAASpD,EAAKyD,GAAQ,OAAOJ,OAAOK,UAAUC,eAAeC,KAAK5D,EAAKyD,EAAO,E,+BCEtG,SAAWI,GAGTC,MAAMC,mBAAqBC,QAAQC,KAAKC,OACtC,CACEC,SAAU,EACVC,SAAS,EAETC,QAAS,KACTC,MAAO,KACPC,SAAU,KACVC,eAAgB,KAChBC,YAAa,KACbC,gBAAiB,KAEjBC,QAAS,KACTC,cAAe,KACfC,UAAW,KACXC,cAAe,KAEfC,KAAM,SAAUZ,EAAUa,GACxBC,KAAKd,SAAWA,EAChBc,KAAKC,YAAYF,GAEjBlB,MAAMC,mBAAmBoB,QAAQF,KAAKd,UAAYc,KAElDA,KAAKZ,QAAUR,EAAE,UAAYM,GAC7Bc,KAAKX,MAAQW,KAAKZ,QAAQe,KAAK,oBAC/BH,KAAKV,SAAWU,KAAKX,MAAMc,KAAK,wBAEhCH,KAAKN,QAAU,CAAC,EAChBM,KAAKT,eAAiBS,KAAKI,WACzBvB,MAAMC,mBAAmBuB,aACzBC,OACJ,EAEAC,UAAW,SAAUC,GACnB,OAAOR,KAAKV,SAASxD,OAAO,cAAgB0E,EAAS,SACvD,EAEAJ,WAAY,SAAUI,GAQpB,YANoC,IAAzBR,KAAKN,QAAQc,GACtBR,KAAKN,QAAQc,GAAUR,KAAKS,WAAWD,GAEvCR,KAAKN,QAAQc,GAAQE,SAGhBV,KAAKN,QAAQc,EACtB,EAEAC,WAAY,SAAUD,GACpB,IAAIF,EAAUN,KAAKO,UAAUC,GAE7B,OAAQA,GACN,KAAK3B,MAAMC,mBAAmBuB,YAC5B,OAAO,IAAIM,EAAWX,KAAMQ,EAAQF,GACtC,KAAKzB,MAAMC,mBAAmB8B,YAC5B,OAAO,IAAIC,EAAWb,KAAMQ,EAAQF,GACtC,KAAKzB,MAAMC,mBAAmBgC,gBAC5B,OAAO,IAAIC,EAAef,KAAMQ,EAAQF,GAC1C,QACE,KAAM,mBAAqBE,EAEjC,EAEAQ,aAAc,WACZhB,KAAKP,gBAAgBwB,OACvB,EAEAC,kBAAmB,WACjBlB,KAAKmB,WAAWtC,MAAMC,mBAAmBuB,aACzCL,KAAKgB,eACLhB,KAAKP,gBAAgB2B,KAAK,gBAAiB,QAC7C,EAEAD,WAAY,SAAUX,GAEhBR,KAAKR,cAEPQ,KAAKT,eACF8B,SAAS,QACT5I,IAAI,CAAC6I,QAAS,EAAGC,QAAS,SAC7BvB,KAAKR,YAAY6B,SAAS,QAAQ5I,IAAI,CAAC6I,QAAS,IAChDtB,KAAKX,MAAMgC,SAAS,QACpBrB,KAAKwB,iCAIPxB,KAAKR,YAAcQ,KAAKO,UAAUC,GAAQ/H,IAAI,CAC5C8I,QAAS,QACTE,SAAU,WACVC,KAAM,MACNC,IAAK,MACLC,MAAO5B,KAAKX,MAAMuC,QAAU,OAI9B5B,KAAKX,MAAMwC,OAAO7B,KAAKX,MAAMwC,UAC7B7B,KAAKT,eAAe8B,SAAS,CAACC,QAAS,GAAI,CAACC,QAAS,SACrDvB,KAAKR,YAAY6B,SAAS,CAACC,QAAS,IACpCtB,KAAKX,MAAMgC,SACT,CAACQ,OAAQ7B,KAAKR,YAAYsC,eAC1B,CACEC,SAAU/B,KAAKwB,8BAA8BjG,KAAKyE,QAItDA,KAAKL,cAAgBK,KAAKI,WAAWI,EACvC,EAEAgB,8BAA+B,WAC7BxB,KAAKX,MAAMwC,OAAO,QAClB7B,KAAKR,YAAY/G,IAAI,CACnBgJ,SAAU,WACVG,MAAO,SAET5B,KAAKT,eAAiBS,KAAKR,YAC3BQ,KAAKR,YAAc,IACrB,GAEF,CACEU,QAAS,CAAC,EACVG,YAAa,OACbO,YAAa,OACbE,gBAAiB,aAIrB,IAAIkB,EAAajD,QAAQC,KAAKC,OAAO,CACnCgD,OAAQ,KACRzB,OAAQ,KACRF,QAAS,KAETR,KAAM,SAAUmC,EAAQzB,EAAQF,GAC9BN,KAAKiC,OAASA,EACdjC,KAAKQ,OAASA,EACdR,KAAKM,QAAUA,EAEfN,KAAKkC,WACP,EAEAA,UAAWtD,EAAEpF,KACbkH,OAAQ9B,EAAEpF,OAGRmH,EAAaqB,EAAW/C,OAAO,CACjCiD,UAAW,WACT,IAAIC,EAAWnC,KAAKM,QAAQ8B,SAAS,WACrCpC,KAAKqC,YAAYF,EAAU,QAAS,oBACtC,EAEAG,kBAAmB,SAAUC,GAC3B,IAAI/B,EAAS5B,EAAEwC,KAAKmB,EAAGC,cAAe,eACtCxC,KAAKiC,OAAOxC,gBAAkBb,EAAE2D,EAAGC,eAAepB,KAChD,iBACA,GAEFpB,KAAKiC,OAAOd,WAAWX,EACzB,IAGEiC,EAAmBT,EAAW/C,OAChC,CACEyD,MAAO,KACPC,eAAgB,KAChBC,WAAY,KACZC,KAAM,KACNC,gBAAiB,KACjBC,cAAe,KAEfC,wBAAyB,KACzBC,eAAgB,KAChBC,YAAa,KACbC,cAAe,KACfC,cAAe,KACfC,cAAe,KACfC,gBAAgB,EAEhBC,aAAc,KACdC,gBAAiB,KACjBC,mBAAoB,KACpBC,eAAgB,KAChBC,kBAAmB,KACnBC,eAAgB,KAChBC,sBAAsB,EAEtB3B,UAAW,WACTlC,KAAK0C,MAAQ1C,KAAKM,QAAQH,KAAK,uBAAuBc,QACtDjB,KAAK2C,eAAiB3C,KAAKM,QAAQ8B,SAAS,aAC5CpC,KAAK4C,WAAa5C,KAAKM,QAAQH,KAAK,WAGpCH,KAAKgD,wBAA0BhD,KAAKM,QAAQ8B,SAC1C,sCAEFpC,KAAKiD,eAAiBjD,KAAKgD,wBAAwB7C,KACjD,4BAEFH,KAAKkD,YAAclD,KAAK2C,eAAeP,SACrC,yBAEFpC,KAAKmD,cAAgBnD,KAAKkD,YAAYd,SACpC,2BAEFpC,KAAKoD,cAAgBpD,KAAKkD,YAAY/C,KAAK,iBAC3CH,KAAKqC,YAAYrC,KAAKkD,YAAa,SAAU,0BAC7ClD,KAAKqC,YACHrC,KAAKkD,YAAY/C,KAAK,8BACtB,QACA,0BAIFH,KAAKuD,aAAevD,KAAK2C,eAAeP,SACtC,0BAEFpC,KAAKwD,gBAAkBxD,KAAKuD,aAAanB,SACvC,4BAEF,IAAI0B,EAAQ9D,KAAKuD,aAAanB,SAAS,oBACvCpC,KAAKyD,mBAAqBK,EAAM3D,KAAK,gCACrCH,KAAK0D,eAAiB1D,KAAKuD,aAAanB,SAAS,iBACjDpC,KAAK4D,eAAiB5D,KAAKM,QAAQ8B,SAAS,UAC5CpC,KAAKqC,YACHrC,KAAKuD,aACL,SACA,2BAGFvD,KAAK8C,gBAAkB9C,KAAK0C,MAAMb,SAClC7B,KAAKqC,YAAYrC,KAAK0C,MAAO,QAAS,wBACtC1C,KAAKqC,YAAYrC,KAAK0C,MAAO,UAAW,qBACxC1C,KAAKqC,YAAYrC,KAAK4C,WAAY,QAAS,qBAC3C5C,KAAK+D,eAAc,EACrB,EAEAC,uBAAwB,SAAUzB,GAC3BvC,KAAK0C,MAAMuB,OACd1B,EAAG2B,gBAEP,EAEAC,qBAAsB,WACpB,IAAIC,EAAOpE,KAAK0C,MAAMuB,MAEtB,GAAIjE,KAAK6C,OAASJ,EAAiB4B,YAIjC,GAHArE,KAAKsE,qBACLtE,KAAKqD,cAAgBkB,WAAWvE,KAAKwE,OAAOjJ,KAAKyE,MAAO,KAEpDoE,EAAM,CACRpE,KAAKmD,cAAcsB,KAAK,IACxB,IAAIC,EAAS1E,KAAK2E,cAAcP,GAChC,IAAK,IAAIQ,KAAQF,EACXA,EAAOhG,eAAekG,IACxBhG,EAAE,WAAY,CACZ/D,KAAM,SACN+J,KAAMA,EACNC,MAAOH,EAAOE,KACbE,SAAS9E,KAAKmD,eAGrBnD,KAAKoD,cAAc2B,YAAY,YAC/B/E,KAAKoD,cAAc4B,WAAW,gBAChC,MACEhF,KAAKoD,cAAc6B,SAAS,YAC5BjF,KAAKoD,cAAchC,KAAK,gBAAiB,aAGvCgD,GACFpE,KAAKwD,gBAAgBS,IAAIG,GACzBpE,KAAK0D,eAAeqB,YAAY,aAEhC/E,KAAK0D,eAAeuB,SAAS,WAGnC,EAEAC,oBAAqB,WACnB,IAAMC,EAAanF,KAAK2C,eAAeyC,SAASjF,KAAK,eACjDgF,EAAW9M,OAAS,IAClB2H,KAAK6C,MAAQJ,EAAiB4C,cAChCrF,KAAK+C,cAAgBoC,EAAWf,OAChCe,EAAWf,KAAKvF,MAAMyG,EAAE,MAAO,+BAEJ,OAAvBtF,KAAK+C,gBACPoC,EAAWf,KAAKpE,KAAK+C,eACrB/C,KAAK+C,cAAgB,MAI7B,EAEAwC,kBAAmB,WACjBvF,KAAKiC,OAAOf,mBACd,EAEAsE,kBAAmB,SAAUjD,GAC3B,OAAQA,EAAGkD,SACT,KAAK1G,QAAQ2G,QACP1F,KAAK6C,OAASJ,EAAiB4B,YACjCrE,KAAKiC,OAAOf,oBACFlB,KAAK6D,sBACf7D,KAAK+D,eAAc,GAErB,MACF,KAAKhF,QAAQ4G,WACP5G,QAAQ6G,iBAAiBrD,KACvBvC,KAAK6C,OAASJ,EAAiB4B,YACjCrE,KAAKkD,YAAY2C,QAAQ,UAEzB7F,KAAKuD,aAAasC,QAAQ,WAKpC,EAEAC,uBAAwB,WACtB9F,KAAK+F,gBAAe,EACtB,EAEAzB,mBAAoB,WACdtE,KAAKqD,gBACP2C,aAAahG,KAAKqD,eAClBrD,KAAKqD,cAAgB,KAEzB,EAEAmB,OAAQ,WAKN,GAJAxE,KAAKsE,qBAEMtE,KAAK0C,MAAMuB,MAEZ,CACR,IAAIgC,EAAMjG,KAAKkG,aAAalG,KAAK0C,MAAMuB,OACvCrF,EAAEuH,KAAK,CACLF,IAAKA,EACLG,SAAU,OACVC,QAASrG,KAAKsG,oBAAoB/K,KAAKyE,MACvCuG,MAAOvG,KAAKwG,kBAAkBjL,KAAKyE,OAEvC,MACEA,KAAKwG,mBAET,EAEAF,oBAAqB,SAAUG,GAAU,IAAAC,EAAA,KACvC,GAAI1G,KAAK6C,OAASJ,EAAiB4B,YAAnC,CAIA,IAAIsC,EAAU3G,KAAK4G,iBAAiBH,GAEpC,GAAIE,EAAQtO,OAAQ,CAClB,IAAIwO,EAEC7G,KAAKsD,eAMRuD,EAAqB7G,KAAKgD,wBAAwBnB,UALlD7B,KAAKgD,wBAAwB+B,YAAY,UACzC8B,EAAqB,EACrB7G,KAAKsD,gBAAiB,EACtBtD,KAAKM,QAAQ2E,SAAS,iBAKxBjF,KAAKiD,eAAewB,KAAK,IAGzB,IADA,IAAIqC,EAAMC,KAAKC,IAAIL,EAAQtO,OAAQ,IAC1BD,EAAI,EAAGA,EAAI0O,EAAK1O,IACvB4H,KAAKiD,eAAegE,OAClBrI,EAAE,QAAQqI,OACRrI,EAAE,MAAO,CACPsI,KAAMlH,KAAKmH,mBAAmBR,EAAQvO,IACtCgP,OAAQ,SACR3C,KACE,uBACAzE,KAAKqH,sBAAsBV,EAAQvO,IACnC,YACA4H,KAAKsH,oBAAoBX,EAAQvO,QAM3C,IAAImP,EAAmBvH,KAAKgD,wBACzBnB,OAAO,QACPA,SACH7B,KAAKgD,wBACF3B,SAAS,QACTQ,OAAOgF,GACPxF,SACC,CAACQ,OAAQ0F,GACT,CACExF,SAAU,WACR2E,EAAK1D,wBAAwBnB,OAAO,OACtC,GAGR,MACE7B,KAAKwG,mBAlDP,CAoDF,EAEAA,kBAAmB,WAAY,IAAAgB,EAAA,KAE3BxH,KAAK6C,OAASJ,EAAiB4B,aAC9BrE,KAAKsD,iBAKRtD,KAAKgD,wBACF3B,SAAS,QACTQ,OAAO7B,KAAKgD,wBAAwBnB,UACpCR,SACC,CAACQ,OAAQ,GACT,CACEE,SAAU,WACRyF,EAAKxE,wBAAwBiC,SAAS,SACxC,IAINjF,KAAKsD,gBAAiB,EACtBtD,KAAKM,QAAQyE,YAAY,gBAC3B,EAEA0C,wBAAyB,SAAUlF,GAC5BvC,KAAK0C,MAAMuB,QAASjE,KAAK6D,sBAK9B7D,KAAK6D,sBAAuB,EAC5B7D,KAAK0D,eAAeuB,SAAS,YAL3B1C,EAAG2B,gBAMP,EAEAxD,OAAQ,WACNV,KAAK0C,MAAMzB,OACb,EAEA8C,cAAe,SAAU2D,GACvB1H,KAAK6C,KAAOJ,EAAiB4B,YAE7BrE,KAAK0C,MAAMrB,SAAS,QAAQJ,QAExBjB,KAAK2D,oBACP3D,KAAK2D,kBAAkB1I,SACvB+E,KAAK2D,kBAAoB,MAGvB+D,EACF1H,KAAK0C,MAAMrB,SAAS,CAACQ,OAAQ7B,KAAK8C,kBAElC9C,KAAK0C,MAAMb,OAAO7B,KAAK8C,iBAGzB9C,KAAK2H,UAAU3H,KAAKuD,aAAcvD,KAAKkD,YAAawE,GAGpD1H,KAAKmE,uBACLnE,KAAKkF,sBACLlF,KAAKwE,QACP,EAEAuB,eAAgB,SAAU2B,GACxB1H,KAAKsE,qBACLtE,KAAKwG,oBAELxG,KAAK6C,KAAOJ,EAAiB4C,aAE7BrF,KAAK0C,MAAMrB,SAAS,QAAQJ,QAExByG,EACF1H,KAAK0C,MAAMrB,SAAS,CAACQ,OAA+B,EAAvB7B,KAAK8C,kBAElC9C,KAAK0C,MAAMb,OAA8B,EAAvB7B,KAAK8C,iBAGzB9C,KAAK2H,UAAU3H,KAAKkD,YAAalD,KAAKuD,aAAcmE,GAGpD1H,KAAKmE,uBACLnE,KAAKkF,qBACP,EAEAyC,UAAW,SAAUC,EAAMC,EAAKH,GAAS,IAAAI,EAAA,KACvC,GAAIJ,EAAS,CACX1H,KAAK2C,eAAed,OAAO7B,KAAK2C,eAAed,UAC/C,IAAID,EAAQ5B,KAAK2C,eAAef,QAEhCgG,EACGvG,SAAS,QACT5I,IAAI,CAACgJ,SAAU,WAAYE,IAAK,EAAGD,KAAM,EAAGE,MAAOA,IACnDP,SACC,CAACC,QAAS,GACV,CACES,SAAU,WACR6F,EACG3C,SAAS,UACTxM,IAAI,CAACgJ,SAAU,WAAYG,MAAO,QACvC,IAINiG,EACGxG,SAAS,QACT0D,YAAY,UACZtM,IAAI,CAACgJ,SAAU,WAAYE,IAAK,EAAGD,KAAM,EAAGE,MAAOA,IACnDP,SACC,CAACC,QAAS,GACV,CACES,SAAU,WACR8F,EAAIpP,IAAI,CAACgJ,SAAU,WAAYG,MAAO,QACxC,IAIN5B,KAAK2C,eAAetB,SAAS,QAAQA,SACnC,CAACQ,OAAQgG,EAAIhG,UACb,CACEE,SAAU,WACR+F,EAAKnF,eAAelK,IAAI,CAACoJ,OAAQ,QACnC,GAGN,MACE+F,EAAK3C,SAAS,UACd4C,EAAI9C,YAAY,SAEpB,EAEAgD,qBAAsB,SAAUtB,GAQ9B,GAPAzG,KAAK6D,sBAAuB,EAC5B7D,KAAK0D,eAAeqB,YAAY,WAE5B/E,KAAK2D,mBACP3D,KAAK2D,kBAAkBvB,WAAWnH,SAGhCwL,EAASuB,OAOX,IAAK,IAAIC,KANJjI,KAAK2D,oBACR3D,KAAK2D,kBAAoB/E,EAAE,wBAAwBsJ,YACjDlI,KAAKuD,eAIakD,EAASuB,OAC7B,GAAIvB,EAASuB,OAAOtJ,eAAeuJ,GACjC,IAAK,IAAI7P,EAAI,EAAGA,EAAIqO,EAASuB,OAAOC,GAAW5P,OAAQD,IAAK,CAC1D,IAAImO,EAAQE,EAASuB,OAAOC,GAAW7P,GACvCwG,EAAE,OAAS2H,EAAQ,SAASzB,SAAS9E,KAAK2D,kBAC5C,CAKF8C,EAASJ,UACXxH,MAAMsJ,GAAGC,eAAevJ,MAAMyG,EAAE,MAAO,+BACvCtF,KAAK0C,MAAMuB,IAAI,IACfjE,KAAKwD,gBAAgBS,IAAI,IACzBjE,KAAKyD,mBAAmBQ,IAAI,KAG9BjE,KAAK4D,eAAea,KAAK,GAC3B,EAEAE,cAAe,WACb,KAAM,qCACR,EACAuB,aAAc,WACZ,KAAM,oCACR,EACAU,iBAAkB,WAChB,KAAM,wCACR,EACAO,mBAAoB,WAClB,KAAM,0CACR,EACAE,sBAAuB,WACrB,KAAM,6CACR,EACAC,oBAAqB,WACnB,KAAM,0CACR,GAEF,CACEjD,YAAa,SACbgB,aAAc,YAIdxE,EAAa4B,EAAiBxD,OAAO,CACvC0F,cAAe,SAAU0D,GACvB,MAAO,CAACC,MAAOD,EACjB,EAEAnC,aAAc,SAAUmC,GACtB,MACE,2FACAzL,mBAAmByL,EAEvB,EAEAzB,iBAAkB,SAAUH,GAC1B,OAAOA,EAAS8B,OAAS,EAC3B,EAEApB,mBAAoB,SAAUqB,GAC5B,OAAOA,EAAOC,IAChB,EAEApB,sBAAuB,SAAUmB,GAC/B,OAAOA,EAAOE,YAAc,QAAU,EACxC,EAEApB,oBAAqB,SAAUkB,GAC7B,OAAOA,EAAOF,KAChB,IAGIvH,EAAiB0B,EAAiBxD,OAAO,CAC7C0F,cAAe,SAAU0D,GACvB,OAAOjK,OAAOuK,OACZ,CACEL,MAAOtI,KAAKiC,OAAOlC,SAAS6I,iBAAmBP,GAEjDrI,KAAKiC,OAAOlC,SAAS8I,YAEzB,EAEA3C,aAAc,SAAUmC,GACtB,MACE,uEACAzL,mBAAmByL,EAEvB,EAEAzB,iBAAkB,SAAUH,GAC1B,OAAOA,EAAS8B,OAAS,EAC3B,EAEApB,mBAAoB,SAAUqB,GAC5B,OAAOA,EAAOM,QAChB,EAEAzB,sBAAuB,SAAUmB,GAC/B,MAAwB,SAAjBA,EAAOO,MAAmB,QAAU,KAC7C,EAEAzB,oBAAqB,SAAUkB,GAC7B,OAAOA,EAAOF,KAChB,GAEH,CA/oBD,CA+oBGU,O", "sources": ["webpack:///../../../../../node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///../../../../../node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:///./CraftSupportWidget.scss?784e", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///./CraftSupportWidget.js"], "sourcesContent": ["/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ruleSet[1].rules[3].use[1]!../../../../../node_modules/css-loader/dist/cjs.js!../../../../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].use[3]!../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[4]!./CraftSupportWidget.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"576ec7e4\", content, true, {});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "import './CraftSupportWidget.scss';\n\n(function ($) {\n  /** global: Craft */\n  /** global: Garnish */\n  Craft.CraftSupportWidget = Garnish.Base.extend(\n    {\n      widgetId: 0,\n      loading: false,\n\n      $widget: null,\n      $pane: null,\n      $screens: null,\n      $currentScreen: null,\n      $nextScreen: null,\n      $triggerElement: null,\n\n      screens: null,\n      currentScreen: null,\n      $helpBody: null,\n      $feedbackBody: null,\n\n      init: function (widgetId, settings) {\n        this.widgetId = widgetId;\n        this.setSettings(settings);\n\n        Craft.CraftSupportWidget.widgets[this.widgetId] = this;\n\n        this.$widget = $('#widget' + widgetId);\n        this.$pane = this.$widget.find('> .front > .pane');\n        this.$screens = this.$pane.find('> .body > .cs-screen');\n\n        this.screens = {};\n        this.$currentScreen = this.initScreen(\n          Craft.CraftSupportWidget.SCREEN_HOME\n        ).$screen;\n      },\n\n      getScreen: function (screen) {\n        return this.$screens.filter('.cs-screen-' + screen + ':first');\n      },\n\n      initScreen: function (screen) {\n        // First time?\n        if (typeof this.screens[screen] === 'undefined') {\n          this.screens[screen] = this.loadScreen(screen);\n        } else {\n          this.screens[screen].reinit();\n        }\n\n        return this.screens[screen];\n      },\n\n      loadScreen: function (screen) {\n        var $screen = this.getScreen(screen);\n\n        switch (screen) {\n          case Craft.CraftSupportWidget.SCREEN_HOME:\n            return new HomeScreen(this, screen, $screen);\n          case Craft.CraftSupportWidget.SCREEN_HELP:\n            return new HelpScreen(this, screen, $screen);\n          case Craft.CraftSupportWidget.SCREEN_FEEDBACK:\n            return new FeedbackScreen(this, screen, $screen);\n          default:\n            throw 'Invalid screen: ' + screen;\n        }\n      },\n\n      focusTrigger: function () {\n        this.$triggerElement.focus();\n      },\n\n      closeSearchScreen: function () {\n        this.gotoScreen(Craft.CraftSupportWidget.SCREEN_HOME);\n        this.focusTrigger();\n        this.$triggerElement.attr('aria-expanded', 'false');\n      },\n\n      gotoScreen: function (screen) {\n        // Are we right in the middle of a transition?\n        if (this.$nextScreen) {\n          // Unfortunately velocity('finish') doesn't work fast enough\n          this.$currentScreen\n            .velocity('stop')\n            .css({opacity: 0, display: 'none'});\n          this.$nextScreen.velocity('stop').css({opacity: 1});\n          this.$pane.velocity('stop');\n          this.handleScreenAnimationComplete();\n        }\n\n        // Init/prep the next screen\n        this.$nextScreen = this.getScreen(screen).css({\n          display: 'block',\n          position: 'absolute',\n          left: '0px',\n          top: '0px',\n          width: this.$pane.width() + 'px',\n        });\n\n        // Animate the new screen into view\n        this.$pane.height(this.$pane.height());\n        this.$currentScreen.velocity({opacity: 0}, {display: 'none'});\n        this.$nextScreen.velocity({opacity: 1});\n        this.$pane.velocity(\n          {height: this.$nextScreen.outerHeight()},\n          {\n            complete: this.handleScreenAnimationComplete.bind(this),\n          }\n        );\n\n        this.currentScreen = this.initScreen(screen);\n      },\n\n      handleScreenAnimationComplete: function () {\n        this.$pane.height('auto');\n        this.$nextScreen.css({\n          position: 'relative',\n          width: 'auto',\n        });\n        this.$currentScreen = this.$nextScreen;\n        this.$nextScreen = null;\n      },\n    },\n    {\n      widgets: {},\n      SCREEN_HOME: 'home',\n      SCREEN_HELP: 'help',\n      SCREEN_FEEDBACK: 'feedback',\n    }\n  );\n\n  var BaseScreen = Garnish.Base.extend({\n    widget: null,\n    screen: null,\n    $screen: null,\n\n    init: function (widget, screen, $screen) {\n      this.widget = widget;\n      this.screen = screen;\n      this.$screen = $screen;\n\n      this.afterInit();\n    },\n\n    afterInit: $.noop,\n    reinit: $.noop,\n  });\n\n  var HomeScreen = BaseScreen.extend({\n    afterInit: function () {\n      var $options = this.$screen.children('.cs-opt');\n      this.addListener($options, 'click', 'handleOptionClick');\n    },\n\n    handleOptionClick: function (ev) {\n      var screen = $.attr(ev.currentTarget, 'data-screen');\n      this.widget.$triggerElement = $(ev.currentTarget).attr(\n        'aria-expanded',\n        true\n      );\n      this.widget.gotoScreen(screen);\n    },\n  });\n\n  var BaseSearchScreen = BaseScreen.extend(\n    {\n      $body: null,\n      $formContainer: null,\n      $cancelBtn: null,\n      mode: null,\n      bodyStartHeight: null,\n      csHeadingText: null,\n\n      $searchResultsContainer: null,\n      $searchResults: null,\n      $searchForm: null,\n      $searchParams: null,\n      $searchSubmit: null,\n      searchTimeout: null,\n      showingResults: false,\n\n      $supportForm: null,\n      $supportMessage: null,\n      $supportAttachment: null,\n      $supportSubmit: null,\n      $supportErrorList: null,\n      $supportIframe: null,\n      sendingSupportTicket: false,\n\n      afterInit: function () {\n        this.$body = this.$screen.find('.cs-body-text:first').focus();\n        this.$formContainer = this.$screen.children('.cs-forms');\n        this.$cancelBtn = this.$screen.find('.cancel');\n\n        // Search mode stuff\n        this.$searchResultsContainer = this.$screen.children(\n          '.cs-search-results-container:first'\n        );\n        this.$searchResults = this.$searchResultsContainer.find(\n          '.cs-search-results:first'\n        );\n        this.$searchForm = this.$formContainer.children(\n          '.cs-search-form:first'\n        );\n        this.$searchParams = this.$searchForm.children(\n          '.cs-search-params:first'\n        );\n        this.$searchSubmit = this.$searchForm.find('.submit:first');\n        this.addListener(this.$searchForm, 'submit', 'handleSearchFormSubmit');\n        this.addListener(\n          this.$searchForm.find('.cs-button-wrapper > p > a'),\n          'click',\n          'handleSupportLinkClick'\n        );\n\n        // Support mode stuff\n        this.$supportForm = this.$formContainer.children(\n          '.cs-support-form:first'\n        );\n        this.$supportMessage = this.$supportForm.children(\n          'input.cs-support-message'\n        );\n        var $more = this.$supportForm.children('.cs-support-more');\n        this.$supportAttachment = $more.find('.cs-support-attachment:first');\n        this.$supportSubmit = this.$supportForm.children('.submit:first');\n        this.$supportIframe = this.$screen.children('iframe');\n        this.addListener(\n          this.$supportForm,\n          'submit',\n          'handleSupportFormSubmit'\n        );\n\n        this.bodyStartHeight = this.$body.height();\n        this.addListener(this.$body, 'input', 'handleBodyTextChange');\n        this.addListener(this.$body, 'keydown', 'handleBodyKeydown');\n        this.addListener(this.$cancelBtn, 'click', 'handleCancelClick');\n        this.prepForSearch(false);\n      },\n\n      handleSearchFormSubmit: function (ev) {\n        if (!this.$body.val()) {\n          ev.preventDefault();\n        }\n      },\n\n      handleBodyTextChange: function () {\n        var text = this.$body.val();\n\n        if (this.mode === BaseSearchScreen.MODE_SEARCH) {\n          this.clearSearchTimeout();\n          this.searchTimeout = setTimeout(this.search.bind(this), 500);\n\n          if (text) {\n            this.$searchParams.html('');\n            var params = this.getFormParams(text);\n            for (var name in params) {\n              if (params.hasOwnProperty(name)) {\n                $('<input/>', {\n                  type: 'hidden',\n                  name: name,\n                  value: params[name],\n                }).appendTo(this.$searchParams);\n              }\n            }\n            this.$searchSubmit.removeClass('disabled');\n            this.$searchSubmit.removeAttr('aria-disabled');\n          } else {\n            this.$searchSubmit.addClass('disabled');\n            this.$searchSubmit.attr('aria-disabled', 'true');\n          }\n        } else {\n          if (text) {\n            this.$supportMessage.val(text);\n            this.$supportSubmit.removeClass('disabled');\n          } else {\n            this.$supportSubmit.addClass('disabled');\n          }\n        }\n      },\n\n      handleHeadingChange: function () {\n        const $csHeading = this.$formContainer.parent().find('.cs-heading');\n        if ($csHeading.length > 0) {\n          if (this.mode == BaseSearchScreen.MODE_SUPPORT) {\n            this.csHeadingText = $csHeading.text();\n            $csHeading.text(Craft.t('app', 'Contact Developer Support'));\n          } else {\n            if (this.csHeadingText !== null) {\n              $csHeading.text(this.csHeadingText);\n              this.csHeadingText = null;\n            }\n          }\n        }\n      },\n\n      handleCancelClick: function () {\n        this.widget.closeSearchScreen();\n      },\n\n      handleBodyKeydown: function (ev) {\n        switch (ev.keyCode) {\n          case Garnish.ESC_KEY:\n            if (this.mode === BaseSearchScreen.MODE_SEARCH) {\n              this.widget.closeSearchScreen();\n            } else if (!this.sendingSupportTicket) {\n              this.prepForSearch(true);\n            }\n            break;\n          case Garnish.RETURN_KEY:\n            if (Garnish.isCtrlKeyPressed(ev)) {\n              if (this.mode === BaseSearchScreen.MODE_SEARCH) {\n                this.$searchForm.trigger('submit');\n              } else {\n                this.$supportForm.trigger('submit');\n              }\n            }\n            break;\n        }\n      },\n\n      handleSupportLinkClick: function () {\n        this.prepForSupport(true);\n      },\n\n      clearSearchTimeout: function () {\n        if (this.searchTimeout) {\n          clearTimeout(this.searchTimeout);\n          this.searchTimeout = null;\n        }\n      },\n\n      search: function () {\n        this.clearSearchTimeout();\n\n        var text = this.$body.val();\n\n        if (text) {\n          var url = this.getSearchUrl(this.$body.val());\n          $.ajax({\n            url: url,\n            dataType: 'json',\n            success: this.handleSearchSuccess.bind(this),\n            error: this.hideSearchResults.bind(this),\n          });\n        } else {\n          this.hideSearchResults();\n        }\n      },\n\n      handleSearchSuccess: function (response) {\n        if (this.mode !== BaseSearchScreen.MODE_SEARCH) {\n          return;\n        }\n\n        var results = this.getSearchResults(response);\n\n        if (results.length) {\n          var startResultsHeight;\n\n          if (!this.showingResults) {\n            this.$searchResultsContainer.removeClass('hidden');\n            startResultsHeight = 0;\n            this.showingResults = true;\n            this.$screen.addClass('with-results');\n          } else {\n            startResultsHeight = this.$searchResultsContainer.height();\n          }\n\n          this.$searchResults.html('');\n\n          var max = Math.min(results.length, 20);\n          for (var i = 0; i < max; i++) {\n            this.$searchResults.append(\n              $('<li>').append(\n                $('<a>', {\n                  href: this.getSearchResultUrl(results[i]),\n                  target: '_blank',\n                  html:\n                    '<span class=\"status ' +\n                    this.getSearchResultStatus(results[i]) +\n                    '\"></span>' +\n                    this.getSearchResultText(results[i]),\n                })\n              )\n            );\n          }\n\n          var endResultsHeight = this.$searchResultsContainer\n            .height('auto')\n            .height();\n          this.$searchResultsContainer\n            .velocity('stop')\n            .height(startResultsHeight)\n            .velocity(\n              {height: endResultsHeight},\n              {\n                complete: () => {\n                  this.$searchResultsContainer.height('auto');\n                },\n              }\n            );\n        } else {\n          this.hideSearchResults();\n        }\n      },\n\n      hideSearchResults: function () {\n        if (\n          this.mode !== BaseSearchScreen.MODE_SEARCH ||\n          !this.showingResults\n        ) {\n          return;\n        }\n\n        this.$searchResultsContainer\n          .velocity('stop')\n          .height(this.$searchResultsContainer.height())\n          .velocity(\n            {height: 0},\n            {\n              complete: () => {\n                this.$searchResultsContainer.addClass('hidden');\n              },\n            }\n          );\n\n        this.showingResults = false;\n        this.$screen.removeClass('with-results');\n      },\n\n      handleSupportFormSubmit: function (ev) {\n        if (!this.$body.val() || this.sendingSupportTicket) {\n          ev.preventDefault();\n          return;\n        }\n\n        this.sendingSupportTicket = true;\n        this.$supportSubmit.addClass('loading');\n      },\n\n      reinit: function () {\n        this.$body.focus();\n      },\n\n      prepForSearch: function (animate) {\n        this.mode = BaseSearchScreen.MODE_SEARCH;\n\n        this.$body.velocity('stop').focus();\n\n        if (this.$supportErrorList) {\n          this.$supportErrorList.remove();\n          this.$supportErrorList = null;\n        }\n\n        if (animate) {\n          this.$body.velocity({height: this.bodyStartHeight});\n        } else {\n          this.$body.height(this.bodyStartHeight);\n        }\n\n        this.swapForms(this.$supportForm, this.$searchForm, animate);\n\n        // In case there's already a search value\n        this.handleBodyTextChange();\n        this.handleHeadingChange();\n        this.search();\n      },\n\n      prepForSupport: function (animate) {\n        this.clearSearchTimeout();\n        this.hideSearchResults();\n\n        this.mode = BaseSearchScreen.MODE_SUPPORT;\n\n        this.$body.velocity('stop').focus();\n\n        if (animate) {\n          this.$body.velocity({height: this.bodyStartHeight * 2});\n        } else {\n          this.$body.height(this.bodyStartHeight * 2);\n        }\n\n        this.swapForms(this.$searchForm, this.$supportForm, animate);\n\n        // In case there's already a search value\n        this.handleBodyTextChange();\n        this.handleHeadingChange();\n      },\n\n      swapForms: function ($out, $in, animate) {\n        if (animate) {\n          this.$formContainer.height(this.$formContainer.height());\n          var width = this.$formContainer.width();\n\n          $out\n            .velocity('stop')\n            .css({position: 'absolute', top: 0, left: 0, width: width})\n            .velocity(\n              {opacity: 0},\n              {\n                complete: function () {\n                  $out\n                    .addClass('hidden')\n                    .css({position: 'relative', width: 'auto'});\n                },\n              }\n            );\n\n          $in\n            .velocity('stop')\n            .removeClass('hidden')\n            .css({position: 'absolute', top: 0, left: 0, width: width})\n            .velocity(\n              {opacity: 1},\n              {\n                complete: function () {\n                  $in.css({position: 'relative', width: 'auto'});\n                },\n              }\n            );\n\n          this.$formContainer.velocity('stop').velocity(\n            {height: $in.height()},\n            {\n              complete: () => {\n                this.$formContainer.css({height: 'auto'});\n              },\n            }\n          );\n        } else {\n          $out.addClass('hidden');\n          $in.removeClass('hidden');\n        }\n      },\n\n      parseSupportResponse: function (response) {\n        this.sendingSupportTicket = false;\n        this.$supportSubmit.removeClass('loading');\n\n        if (this.$supportErrorList) {\n          this.$supportErrorList.children().remove();\n        }\n\n        if (response.errors) {\n          if (!this.$supportErrorList) {\n            this.$supportErrorList = $('<ul class=\"errors\"/>').insertAfter(\n              this.$supportForm\n            );\n          }\n\n          for (var attribute in response.errors) {\n            if (response.errors.hasOwnProperty(attribute)) {\n              for (var i = 0; i < response.errors[attribute].length; i++) {\n                var error = response.errors[attribute][i];\n                $('<li>' + error + '</li>').appendTo(this.$supportErrorList);\n              }\n            }\n          }\n        }\n\n        if (response.success) {\n          Craft.cp.displaySuccess(Craft.t('app', 'Message sent successfully.'));\n          this.$body.val('');\n          this.$supportMessage.val('');\n          this.$supportAttachment.val('');\n        }\n\n        this.$supportIframe.html('');\n      },\n\n      getFormParams: function () {\n        throw 'getFormParams() must be implemented';\n      },\n      getSearchUrl: function () {\n        throw 'getSearchUrl() must be implemented';\n      },\n      getSearchResults: function () {\n        throw 'getSearchResults() must be implemented';\n      },\n      getSearchResultUrl: function () {\n        throw 'getSearchResultUrl() must be implemented';\n      },\n      getSearchResultStatus: function () {\n        throw 'getSearchResultStatus() must be implemented';\n      },\n      getSearchResultText: function () {\n        throw 'getSearchResultUrl() must be implemented';\n      },\n    },\n    {\n      MODE_SEARCH: 'search',\n      MODE_SUPPORT: 'support',\n    }\n  );\n\n  var HelpScreen = BaseSearchScreen.extend({\n    getFormParams: function (query) {\n      return {title: query};\n    },\n\n    getSearchUrl: function (query) {\n      return (\n        'https://api.stackexchange.com/2.2/similar?site=craftcms&sort=relevance&order=desc&title=' +\n        encodeURIComponent(query)\n      );\n    },\n\n    getSearchResults: function (response) {\n      return response.items || [];\n    },\n\n    getSearchResultUrl: function (result) {\n      return result.link;\n    },\n\n    getSearchResultStatus: function (result) {\n      return result.is_answered ? 'green' : '';\n    },\n\n    getSearchResultText: function (result) {\n      return result.title;\n    },\n  });\n\n  const FeedbackScreen = BaseSearchScreen.extend({\n    getFormParams: function (query) {\n      return Object.assign(\n        {\n          title: this.widget.settings.issueTitlePrefix + query,\n        },\n        this.widget.settings.issueParams\n      );\n    },\n\n    getSearchUrl: function (query) {\n      return (\n        'https://api.github.com/search/issues?q=type:issue+repo:craftcms/cms+' +\n        encodeURIComponent(query)\n      );\n    },\n\n    getSearchResults: function (response) {\n      return response.items || [];\n    },\n\n    getSearchResultUrl: function (result) {\n      return result.html_url;\n    },\n\n    getSearchResultStatus: function (result) {\n      return result.state === 'open' ? 'green' : 'red';\n    },\n\n    getSearchResultText: function (result) {\n      return result.title;\n    },\n  });\n})(jQuery);\n"], "names": ["listToStyles", "parentId", "list", "styles", "newStyles", "i", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "__esModule", "default", "module", "locals", "exports", "add", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "prop", "prototype", "hasOwnProperty", "call", "$", "Craft", "CraftSupportWidget", "Garnish", "Base", "extend", "widgetId", "loading", "$widget", "$pane", "$screens", "$currentScreen", "$nextScreen", "$triggerElement", "screens", "currentScreen", "$helpBody", "$feedbackBody", "init", "settings", "this", "setSettings", "widgets", "find", "initScreen", "SCREEN_HOME", "$screen", "getScreen", "screen", "loadScreen", "reinit", "HomeScreen", "SCREEN_HELP", "HelpScreen", "SCREEN_FEEDBACK", "FeedbackScreen", "focusTrigger", "focus", "closeSearchScreen", "gotoScreen", "attr", "velocity", "opacity", "display", "handleScreenAnimationComplete", "position", "left", "top", "width", "height", "outerHeight", "complete", "BaseScreen", "widget", "afterInit", "$options", "children", "addListener", "handleOptionClick", "ev", "currentTarget", "BaseSearchScreen", "$body", "$formContainer", "$cancelBtn", "mode", "bodyStartHeight", "csHeadingText", "$searchResultsContainer", "$searchResults", "$searchForm", "$searchParams", "$searchSubmit", "searchTimeout", "showingResults", "$supportForm", "$supportMessage", "$supportAttachment", "$supportSubmit", "$supportErrorList", "$supportIframe", "sendingSupportTicket", "$more", "prepForSearch", "handleSearchFormSubmit", "val", "preventDefault", "handleBodyTextChange", "text", "MODE_SEARCH", "clearSearchTimeout", "setTimeout", "search", "html", "params", "getFormParams", "name", "value", "appendTo", "removeClass", "removeAttr", "addClass", "handleHeadingChange", "$csHeading", "parent", "MODE_SUPPORT", "t", "handleCancelClick", "handleBodyKeydown", "keyCode", "ESC_KEY", "RETURN_KEY", "isCtrlKeyPressed", "trigger", "handleSupportLinkClick", "prepForSupport", "clearTimeout", "url", "getSearchUrl", "ajax", "dataType", "success", "handleSearchSuccess", "error", "hideSearchResults", "response", "_this", "results", "getSearchResults", "startResultsHeight", "max", "Math", "min", "append", "href", "getSearchResultUrl", "target", "getSearchResultStatus", "getSearchResultText", "endResultsHeight", "_this2", "handleSupportFormSubmit", "animate", "swapForms", "$out", "$in", "_this3", "parseSupportResponse", "errors", "attribute", "insertAfter", "cp", "displaySuccess", "query", "title", "items", "result", "link", "is_answered", "assign", "issueTitlePrefix", "issueParams", "html_url", "state", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}