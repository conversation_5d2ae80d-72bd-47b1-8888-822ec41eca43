<?php

namespace kldev\emailmarketing\resources;

use craft\web\AssetBundle;
use craft\web\assets\cp\CpAsset;

class CraftEmailMarketingCpAssetBundle extends AssetBundle
{
    public function init()
    {
        $this->sourcePath = '@kldev/emailmarketing/resources';

        // Define the dependencies ( if any )
        $this->depends = [
            CpAsset::class,
        ];


        // Define the relative path to CSS/JS files that should be registered with the page
        $this->js = [
            'js/kldev-email-marketing.js',
        ];

        $this->css = [
            'css/kldev-email-marketing.css',
        ];

        parent::init();
    }
}
