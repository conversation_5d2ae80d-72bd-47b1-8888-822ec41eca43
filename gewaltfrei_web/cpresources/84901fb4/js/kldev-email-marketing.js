if (window.location.pathname.includes('_craft-email-marketing')) {

    document.addEventListener('DOMContentLoaded', e => {
        handleTabNavigation();
    });

    function handleTabNavigation() {
        const tabButtons = document.querySelectorAll('[aria-label="kldev-email-marketing-settings-sidebar"] a');
        const activeItemTargetId = Array.from(tabButtons).filter(button => button.classList.contains('sel'))[0].getAttribute('href').replace('#', '');

        document.getElementById(activeItemTargetId).classList.remove('hidden');

        tabButtons.forEach(button => {
            const element = document.getElementById(button.getAttribute('href').replace('#', ''));

            if (element.getAttribute('data-status') == "1") {
                const span = document.createElement('span');
                span.classList.add('status', 'green');
                button.setAttribute('style', 'display: flex; justify-content: space-between;')
                button.appendChild(span)
            }

            button.addEventListener('click', e => {

                Array.from(tabButtons).filter(tabButton => tabButton.classList.contains('sel'))[0].classList.remove('sel');

                Array.from(document.querySelectorAll('.pane-content')).filter(pane => !pane.classList.contains('hidden'))[0].classList.add('hidden');

                e.target.classList.add('sel');

                element.classList.remove('hidden');
            });
        })
    }
}