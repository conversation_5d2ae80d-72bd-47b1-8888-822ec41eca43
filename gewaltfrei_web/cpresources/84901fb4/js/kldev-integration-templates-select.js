document.addEventListener('DOMContentLoaded', function () {
    const id = document.getElementById('fields-inputId').value;
    const selectElement = document.querySelector(`[id*=${id}] select`);
    const previewElement = document.querySelector(`[id$=${id}-preview]`);
    const previewData = JSON.parse(document.querySelector(`[id$=${id}-previewUrls]`).value);

    // Initialize Selectize
    const selectizeInstance = $(selectElement).selectize()[0].selectize;

    selectizeInstance.on('change', function () {
        const selectedValue = selectizeInstance.getValue();
        const previewUrl = selectedValue ? previewData[selectedValue] : '';

        if (previewUrl) {
            previewElement.innerHTML = '<img src="' + previewUrl + '" alt="Template Preview" style="max-width: 100%; height: auto;" />';
        } else {
            previewElement.innerHTML = '';
        }
    });

    // Trigger initial change event to show the preview if there is a pre-selected value
    selectizeInstance.trigger('change');
});
