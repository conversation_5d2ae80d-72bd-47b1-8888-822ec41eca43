document.addEventListener('DOMContentLoaded', function () {
    const btns = document.querySelectorAll('[id$="--refresh-lists"]');

    btns.forEach(btn => {
        btn.addEventListener('click', e => {
            e.preventDefault();

            const integrationService = btn.dataset.integration.replace('Integration', '');
            const entity = btn.dataset.entity;
            const errorMsg = document.querySelector(`.error-message[data-entity=${entity}]`);

            btn.classList.add('disabled');
            
            fetch(Craft.getActionUrl('_craft-email-marketing/integration/reset-cache'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    integration: integrationService,
                    entity: entity,
                })
            })
            .then(response => response.json())
            .then(response => {
                if(response['status'] === 'success'){
                    window.location.reload();
                } else {
                    btn.classList.remove('disabled');
                    errorMsg.classList.remove('hidden');
                }
            })
            .catch(error => {
                btn.classList.remove('disabled');
                errorMsg.classList.remove('hidden');
                console.log(error);
            });
        });
    });
});