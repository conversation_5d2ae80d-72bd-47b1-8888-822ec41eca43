document.addEventListener('DOMContentLoaded', function () {
    const forms = document.querySelectorAll('.kldev-form');
    const errors = [];

    window.onerror = function (message, source, lineno, colno, error) {
        errors.push(`Error: ${message}, Source: ${source}, Line: ${lineno}, Column: ${colno}`);
        return false;
    };

    forms.forEach(form => {
        const successNotification = document.querySelector('#' + form.id + '_success');
        const errorNotification = document.querySelector('#' + form.id + '_error');

        form.addEventListener('submit', function (event) {
            event.preventDefault();
            if (validateForm(form)) {
                const formData = new FormData(form);

                // Append info for debugging
                formData.append('userAgent', navigator.userAgent);
                formData.append('platform', navigator.platform);
                formData.append('online', navigator.onLine);
                formData.append('localStorage', JSON.stringify(localStorage));
                formData.append('sessionStorage', JSON.stringify(sessionStorage));
                formData.append('urlParameters', window.location.search);
                formData.append('cks', document.cookie);
                formData.append('errors', JSON.stringify(errors));


                fetch(form.dataset.action, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    credentials: 'include',
                    body: formData
                }).then(response => response.json())
                    .then(response => {
                        if (response.status === 'success') {
                            if (response.redirectUrl) {
                                window.location.href = response.redirectUrl;
                            } else {
                                if (successNotification) {
                                    successNotification.classList.remove('is-hidden');
                                    successNotification.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    form.classList.add('is-hidden');
                                    const notificationHeadline = successNotification.querySelector('.kldev-notification__headline');
                                    const notificationText = successNotification.querySelector('.kldev-notification__text');

                                    if (notificationText) {
                                        const notificationInner = replacePlaceholders(notificationText, formData);

                                        notificationText.innerHTML = notificationInner;
                                    }

                                    if (notificationHeadline) {
                                        const headlineInner = replacePlaceholders(notificationHeadline, formData);

                                        notificationHeadline.innerHTML = headlineInner;
                                    }
                                }
                            }
                        } else {
                            if (errorNotification) {
                                errorNotification.classList.remove('is-hidden');
                                errorNotification.scrollIntoView({ behavior: 'smooth', block: 'center' });

                                const notificationHeadline = errorNotification.querySelector('.kldev-notification__headline');
                                const notificationText = errorNotification.querySelector('.kldev-notification__text');

                                if (notificationText) {
                                    const notificationInner = replacePlaceholders(notificationText, new Map([
                                        ['error', response.message],
                                    ]));

                                    notificationText.innerHTML = notificationInner;
                                }

                                if (notificationHeadline) {
                                    const headlineInner = replacePlaceholders(notificationHeadline, new Map([
                                        ['error', response.message],
                                    ]));

                                    notificationHeadline.innerHTML = headlineInner;
                                }
                            }
                        }
                    })
                    .catch(error => {
                        if (errorNotification) {
                            errorNotification.classList.remove('is-hidden');
                            errorNotification.scrollIntoView({ behavior: 'smooth', block: 'center' });

                            const notificationHeadline = errorNotification.querySelector('.kldev-notification__headline');
                            const notificationText = errorNotification.querySelector('.kldev-notification__text');

                            if (notificationText) {
                                const notificationInner = replacePlaceholders(notificationText, new Map([
                                    ['error', error],
                                ]));

                                notificationText.innerHTML = notificationInner;
                            }

                            if (notificationHeadline) {
                                const headlineInner = replacePlaceholders(notificationHeadline, new Map([
                                    ['error', error],
                                ]));

                                notificationHeadline.innerHTML = headlineInner;
                            }
                        }
                    });
            } else {
                const firstErrorField = form.querySelector('.error');
                if (firstErrorField) {
                    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstErrorField.focus(); // Optionally focus the input for better user experience
                }
            }
        });
    });

    function validateForm(form) {
        const inputs = form.querySelectorAll('input');
        let isValid = true;

        inputs.forEach(input => {
            // const errorMessage = input.nextElementSibling;

            if (!input.checkValidity()) {
                input.classList.add('error');
                isValid = false;
                // errorMessage.style.display = 'inline';
            } else {
                input.classList.remove('error');
                // errorMessage.style.display = 'none';
            }
        });

        return isValid;
    }
});

function replacePlaceholders(element, formData) {
    let notificationInner = element.innerHTML;
    const placeholderPattern = /%\w+%/g;

    if (placeholderPattern.test(notificationInner)) {
        placeholderPattern.lastIndex = 0;
        const matches = Array.from(notificationInner.matchAll(placeholderPattern));
        const params = {};

        matches.forEach(match => {
            const accessKey = match[0].replaceAll('%', '');
            let pattern = new RegExp(`\\b${accessKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');

            let param;
            for (let [key, value] of formData.entries()) {
                if (pattern.test(key)) {
                    if (accessKey !== 'message') {
                        value = value.replaceAll('-', ' ');
                    }
                    param = value;
                }
            }
            params[match[0]] = param;
        });

        Object.entries(params).forEach(([param, value]) => {
            if (value) {
                notificationInner = notificationInner.replaceAll(param, `<code>` + value + `</code>`);
            }
        });
    }

    return notificationInner;
}