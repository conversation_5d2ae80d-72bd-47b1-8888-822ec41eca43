{"version": 3, "file": "PluginManager.js", "mappings": "mDAIe,SAASA,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,CAAC,EACRC,EAAI,EAAGA,EAAIH,EAAKI,OAAQD,IAAK,CACpC,IAAIE,EAAOL,EAAKG,GACZG,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIP,EAAW,IAAMI,EACrBK,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBH,EAAUI,GAGbJ,EAAUI,GAAIK,MAAMC,KAAKL,GAFzBN,EAAOW,KAAKV,EAAUI,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,IAIlD,CACA,OAAON,CACT,C,gCClBA,IAAIY,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,CAMhB,EAEEC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,WAAa,EACpBC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiBhC,EAAUC,EAAMgC,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,CAAC,EAEvB,IAAIhC,EAASH,EAAaC,EAAUC,GAGpC,OAFAkC,EAAejC,GAER,SAAiBkC,GAEtB,IADA,IAAIC,EAAY,GACPjC,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,IACdkC,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,EACjB,CAOA,IANIF,EAEFD,EADAjC,EAASH,EAAaC,EAAUoC,IAGhClC,EAAS,GAEFE,EAAI,EAAGA,EAAIiC,EAAUhC,OAAQD,IAAK,CACzC,IAAIkC,EACJ,GAAsB,KADlBA,EAAWD,EAAUjC,IACZmC,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,GAC9B,CACF,CACF,CACF,CAEA,SAAS4B,EAAgBjC,GACvB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,GACdkC,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,OAEvC,KAAO,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,EACxD,CACF,CACF,CAEA,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,CACT,CAEA,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIxC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,EAExC,CAEA,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,EACpE,MAEEV,EAAeD,IACfM,EAASQ,EAAWD,KAAK,KAAMZ,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,EACtC,EAKF,OAFAK,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAOhD,MAAQsC,EAAItC,KACnBgD,EAAO/C,QAAUqC,EAAIrC,OACrB+C,EAAO9C,YAAcoC,EAAIpC,UAC3B,OAEFqC,EAAOD,EAAMU,EACf,MACER,GAEJ,CACF,CAEA,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,KACxC,GAGF,SAASV,EAAqBX,EAAciB,EAAOX,EAAQF,GACzD,IAAItC,EAAMwC,EAAS,GAAKF,EAAItC,IAE5B,GAAIkC,EAAasB,WACftB,EAAasB,WAAWC,QAAUP,EAAYC,EAAOnD,OAChD,CACL,IAAI0D,EAAUpD,SAASqD,eAAe3D,GAClC4D,EAAa1B,EAAa0B,WAC1BA,EAAWT,IAAQjB,EAAaS,YAAYiB,EAAWT,IACvDS,EAAWhE,OACbsC,EAAa2B,aAAaH,EAASE,EAAWT,IAE9CjB,EAAaG,YAAYqB,EAE7B,CACF,CAEA,SAASX,EAAYb,EAAcI,GACjC,IAAItC,EAAMsC,EAAItC,IACVC,EAAQqC,EAAIrC,MACZC,EAAYoC,EAAIpC,UAiBpB,GAfID,GACFiC,EAAa4B,aAAa,QAAS7D,GAEjCe,EAAQ+C,OACV7B,EAAa4B,aAAa7C,EAAUqB,EAAIxC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU8D,QAAQ,GAAK,MAEnDhE,GAAO,uDAAyDiE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUnE,MAAgB,OAG9HgC,EAAasB,WACftB,EAAasB,WAAWC,QAAUzD,MAC7B,CACL,KAAOkC,EAAaoC,YAClBpC,EAAaS,YAAYT,EAAaoC,YAExCpC,EAAaG,YAAY/B,SAASqD,eAAe3D,GACnD,CACF,C,uCC1NA,IAAIuE,EAAU,EAAQ,KACnBA,EAAQC,aAAYD,EAAUA,EAAQE,SACnB,iBAAZF,IAAsBA,EAAU,CAAC,CAACG,EAAO5E,GAAIyE,EAAS,MAC7DA,EAAQI,SAAQD,EAAOE,QAAUL,EAAQI,SAG/BE,EADH,SACO,WAAYN,GAAS,EAAM,CAAC,E,GCRzCO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaL,QAGrB,IAAIF,EAASI,EAAyBE,GAAY,CACjDlF,GAAIkF,EAEJJ,QAAS,CAAC,GAOX,OAHAO,EAAoBH,GAAUN,EAAQA,EAAOE,QAASG,GAG/CL,EAAOE,OACf,CCrBAG,EAAoBK,EAAI,SAASV,GAChC,IAAIW,EAASX,GAAUA,EAAOF,WAC7B,WAAa,OAAOE,EAAgB,OAAG,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAK,EAAoBO,EAAED,EAAQ,CAAEE,EAAGF,IAC5BA,CACR,ECNAN,EAAoBO,EAAI,SAASV,EAASY,GACzC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,EAAEF,EAAYC,KAASV,EAAoBW,EAAEd,EAASa,IAC5EE,OAAOC,eAAehB,EAASa,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAV,EAAoBW,EAAI,SAASpD,EAAKyD,GAAQ,OAAOJ,OAAOK,UAAUC,eAAeC,KAAK5D,EAAKyD,EAAO,E,+BCEtG,SAAWI,GAGTC,MAAMC,cAAgBC,QAAQC,KAAKC,OACjC,CACEC,KAAM,WAAY,IAAAC,EAAA,KAChBC,KAAKC,uBAAuBC,MAAK,SAACC,GAChC,IAAK,IAAIC,KAAUD,EACbA,EAASb,eAAec,KACrBD,EAASC,GAAQC,oBAGpB,IAAIC,EAAOP,EAAMP,EAAE,WAAaY,IAASxE,OACvCuE,EAASC,IAHXL,EAAKQ,wBAAwBH,EAAQD,EAASC,IAQtD,GACF,EAEAH,qBAAsB,WACpB,OAAO,IAAIO,SAAQ,SAAUC,EAASC,GACpCjB,MAAMkB,eAAe,MAAO,eAAgB,CAC1CC,OAAQ,CACNC,QAAS,aAGVX,MAAK,SAACC,GACL,IAAIW,EAAO,CACTC,eAAgBZ,EAASa,QAAQD,gBAAkB,IAErDtB,MAAMwB,kBAAkB,OAAQ,8BAA+B,CAC7DH,KAAAA,IAECZ,MAAK,SAACC,GACLM,EAAQN,EAASW,KACnB,IAAE,OACK,WACLJ,GACF,GACJ,IAAE,MACKA,EACX,GACF,EAEAH,wBAAyB,SAAUH,EAAQc,GACzC,IAAIC,EAAS3B,EAAE,YACV2B,EAAOlI,SACVkI,EAAS3B,EAAE,WAAY,CACrBrG,GAAI,UACJiI,MAAO,6BACPC,KAAM,oBAER7B,EAAE,eAAe8B,YAAYH,IAG/B,IAAMI,EAAO/B,EAAE,QAAS,CACtBsB,KAAM,CACJV,OAAQA,KAGToB,SAASL,EAAOM,SAAS,UACzBC,OACClC,EAAE,SAASkC,OACTlC,EAAE,SAAU,CAAC4B,MAAO,iBACjBM,OACClC,EAAE,SAAU,CAAC4B,MAAO,SAASM,OAC3BlC,EAAE,SAAU,CAACmC,IAAKT,EAAKU,YAG1BF,OACClC,EAAE,SAAU,CAAC4B,MAAO,mBACjBM,OAAOlC,EAAE,QAAS,CAACqC,KAAMX,EAAKY,QAC9BJ,OACCR,EAAKa,YACDvC,EAAE,OAAQ,CAACqC,KAAMX,EAAKa,cACtBvC,KAELkC,OACCR,EAAKc,iBACDxC,EAAE,OAAQ,CAAC4B,MAAO,UAAUM,OAC1BlC,EAAE,OAAQ,CACRyC,KAAMf,EAAKc,iBACXE,OAAQ,SACRL,KAAMpC,MAAM0C,EAAE,MAAO,oBAGzB3C,KAELkC,OACClC,EAAE,SAAU,CAAC4B,MAAO,qBAAqBM,OACvClC,EAAE,UAAW,CAAC4B,MAAO,SAASM,OAC5BlC,EAAE,WAAY,CACZ4B,MAAO,YACPgB,KAAM,GACNC,UAAW,GACXC,MAAO7C,MAAMC,cAAc6C,iBACzBrB,EAAKsB,YAEPC,UAAU,EACVC,UAAU,UAQ3BhB,OACClC,EAAE,QAAS,CACT4B,MAAO,SACP,aAAc3B,MAAM0C,EAAE,MAAO,YAE5BT,OAAOlC,EAAE,UAAW,CAAC4B,MAAO,YAC5BM,OACClC,EAAE,UAAW,CAAC4B,MAAO,QAASS,KAAMpC,MAAM0C,EAAE,MAAO,eAGxDT,OACCR,EAAKyB,cACDnD,EAAE,QAAS,CACT4B,MAAO,cACP,aAAc3B,MAAM0C,EAAE,MAAO,YAC5BT,OACDlC,EAAE,UAAW,CACXoD,OAAQ,OACR,iBAAkB,UAEjBlB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,SACNQ,MAAO,yBAGVZ,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,cACNQ,MAAOpB,EAAK2B,eAGfnB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,SACNQ,MAAOlC,KAGVsB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,UACNQ,MAAOpB,EAAK4B,mBAGfpB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,UACNQ,MAAOpB,EAAKyB,iBAGfjB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,aACNQ,MAAOpB,EAAKsB,cAGfd,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,SACNQ,MAAO,sBAGVZ,OAAOjC,MAAMsD,gBACbrB,OACClC,EAAE,SAAU,CAAC4B,MAAO,aACjBM,OACClC,EAAE,YAAa,CACb/D,KAAM,SACN2F,MAAO,cACP,YAAa,cAGhBM,OACClC,EAAE,SAAU,CACV4B,MAAO,OACP,aAAc,UACbM,OACDlC,EAAE,SAASkC,OACTlC,EAAE,SAASkC,OACTlC,EAAE,OAAQ,CACR4B,MAAO,aACPS,KAAMpC,MAAM0C,EAAE,MAAO,mBAQvC3C,KAERC,MAAMuD,eAAezB,EACvB,GAEF,CACEgB,iBAAkB,SAAUzD,GAC1B,MAAmB,iBAARA,GAA4B,KAARA,EACtB,GAEM,MAAXA,EAAI,GACCA,EAEFA,EAAImE,QAAQ,QAAS,OAAOC,UAAU,EAAG,IAAIC,aACtD,IAIJ,IAAM7C,EAASX,QAAQC,KAAKC,OAAO,CACjCuD,QAAS,KACT7B,KAAM,KACN8B,SAAU,KACVC,cAAe,KACfC,UAAW,KACXC,SAAU,KACVC,QAAS,KACTrD,OAAQ,KACRsD,cAAe,KAEf5D,KAAM,SAAUsD,EAAS7B,GACvBvB,KAAKoD,QAAUA,EACfpD,KAAKuB,KAAOA,EACZvB,KAAKqD,SAAWrD,KAAKuB,KAAKoC,KAAK,mBAC/B3D,KAAKsD,cAAgB/B,EAAKoC,KAAK,gBAC/B3D,KAAKuD,UAAYvD,KAAKsD,cACnBK,KAAK,cACLC,WAAW,YACd5D,KAAKyD,QAAUzD,KAAKsD,cAAcK,KAAK,QACvC3D,KAAKwD,SAAWjC,EAAKoC,KAAK,YAC1B3D,KAAKI,OAASJ,KAAKuB,KAAKT,KAAK,UAC7Bd,KAAK6D,YAAY7D,KAAKuD,UAAW,QAAS,cAC1CvD,KAAK6D,YAAY7D,KAAKuD,UAAW,QAAS,cAC5C,EAEAO,OAAQ,WACN,OAAO9D,KAAKuD,UAAUQ,MAAMd,QAAQ,MAAO,IAAIE,aACjD,EAEAa,WAAY,WACVhE,KAAKuD,UAAUU,QACjB,EAEAC,YAAa,WACPlE,KAAK0D,eACPS,aAAanE,KAAK0D,eAEpB,IAAM5E,EAAMkB,KAAK8D,SACjB,GACiB,IAAfhF,EAAI7F,QACW,KAAf6F,EAAI7F,QACH6F,EAAI7F,OAAS,GAAgB,MAAX6F,EAAI,GACvB,CAEA,IAAMsF,EAAU3E,MAAMC,cAAc6C,iBAAiBzD,GACrDkB,KAAKuD,UAAUQ,IAAIK,GACnBpE,KAAK0D,cAAgBW,WACnBrE,KAAKsE,oBAAoBnI,KAAK6D,MAC9B,IAEJ,CACF,EAEAsE,oBAAqB,WAAY,IAAAC,EAAA,KAC/BvE,KAAKwD,SAASgB,YAAY,UAE1B,IAAI1D,EAAO,CAACV,OAAQJ,KAAKI,OAAQtB,IAAKkB,KAAK8D,UAC3CrE,MAAMwB,kBAAkB,OAAQ,4BAA6B,CAACH,KAAAA,IAAOZ,MACnE,WACEqE,EAAKnB,QAAQnD,uBAAuBC,MAAK,SAACC,GACxCoE,EAAKf,SAASiB,SAAS,UACvBF,EAAK3I,OAAOuE,EAASoE,EAAKnE,QAC5B,GACF,GAEJ,EAEAxE,OAAQ,SAAUsF,GAEhB,IAAMwD,EAAW1E,KAAKuB,KAAKoC,KAAK,uBAChC,GAA6B,SAAzBzC,EAAKyD,kBAA+BzD,EAAK0D,cAAc3L,OAAQ,CACjE,IAAM4L,EAAWrF,EAAE,UAAW,CAC5B4B,MACE,uBAC+B,IAA9BF,EAAK0D,cAAc3L,OAAe,QAAU,MAE7CyL,EAASzL,OACXyL,EAASpD,YAAYuD,GAErBA,EAASrD,SAASxB,KAAKuB,KAAKoC,KAAK,SAErC,MAAWe,EAASzL,QAClByL,EAAS7I,SAIX,IAAMiJ,EAAc9E,KAAKuB,KAAKoC,KAAK,YACnC,GAAIzC,EAAK6D,qBAAuB7D,EAAK8D,QAAS,CAC5C,IAAMC,EAAc/D,EAAKgE,iBACrB1F,EAAE,OAAQ,CACRyC,KAAMxC,MAAM0F,OAAO,gBAAkBnF,KAAKI,QAC1CgB,MAAO,YAET5B,EAAE,SAAU,CAAC4B,MAAO,YACpBF,EAAK6D,qBACPvF,EAAE,SAAU,CAAC4B,MAAO,eAAgBS,KAAMX,EAAKkE,UAAU5D,SACvDyD,GAGA/D,EAAK8D,SACPxF,EAAE,SAAU,CACV4B,MAAO,gBACPS,KAAMpC,MAAM0C,EAAE,MAAO,WACpBX,SAASyD,GAEVH,EAAY7L,OACd6L,EAAYxD,YAAY2D,GAExBA,EAAY/H,aAAa8C,KAAKuB,KAAKoC,KAAK,YAE5C,MAAWmB,EAAY7L,QACrB6L,EAAYjJ,SAId,IAAMwJ,EACJnE,EAAKsB,YAAwC,YAA1BtB,EAAKyD,iBAqB1B,GApBIU,GACFrF,KAAKsD,cAAckB,YAAY,UAC3BtD,EAAKsB,aAAexC,KAAKuD,UAAUQ,MAAMuB,MAAM,QACjDtF,KAAKuD,UAAUQ,IACbtE,MAAMC,cAAc6C,iBAAiBrB,EAAKsB,cAI9CxC,KAAKsD,cAAcmB,SAAS,UAI1BY,GAAkBnE,EAAK0D,cAAc3L,OACvC+G,KAAKuD,UAAUkB,SAAS,SAExBzE,KAAKuD,UAAUiB,YAAY,SAI7BxE,KAAKuB,KAAKoC,KAAK,WAAW9H,SACtBqF,EAAK0D,cAAc3L,OAAQ,CAE7B,IADA,IAAIsM,EAAU/F,IACLxG,EAAI,EAAGA,EAAIkI,EAAK0D,cAAc3L,OAAQD,IAAK,CAClD,IAAIwM,OAAO,EACX,OAAQtE,EAAK0D,cAAc5L,IACzB,IAAK,YACHwM,EAAU/F,MAAM0C,EACd,MACA,iDAEF,MACF,IAAK,gBACHqD,EACE/F,MAAM0C,EAAE,MAAO,0CAA2C,CACxDL,KACEZ,EAAK4B,gBAAgB2C,OAAO,GAAGtC,cAC/BjC,EAAK4B,gBAAgBI,UAAU,KAEnC,8DACAzD,MAAM0C,EAAE,MAAO,UACf,YACF,MACF,IAAK,aACHqD,EAAU/F,MAAM0C,EACd,MACA,+HACA,CACEuD,YACE,iGACFC,OAAQlG,MAAMmG,SAAS,oBAADC,OACA7F,KAAKI,OAAM,KAAAyF,OAAI3E,EAAKkE,YAI9C,MACF,IAAK,SACHI,EAAU/F,MAAM0C,EACd,MACA,uDACA,CACE2D,QAAS5E,EAAK4E,UAGlB,MACF,IAAK,WACHN,EAAU/F,MAAM0C,EAAE,MAAO,8BACzB,MACF,QACEqD,EAAU/F,MAAM0C,EAAE,MAAO,gCAG7B,IAAM4D,EAAKvG,EAAE,OAAQ,CAAC4B,MAAO,QAASC,KAAMmE,IAC5C,GAA8B,kBAA1BtE,EAAK0D,cAAc5L,GAAwB,CAC7C,IAAMgN,EAAQxG,EAAE,UAAW,CACzBoD,OAAQ,OACR,iBAAkB,UAEjBlB,OAAOjC,MAAMsD,gBACbrB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,SACNQ,MAAO,4BAGVZ,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,eACNQ,MAAOtC,KAAKI,UAGfsB,OACClC,EAAE,WAAY,CACZ/D,KAAM,SACNqG,KAAM,UACNQ,MAAOpB,EAAK4B,mBAGfpB,OAAOqE,GAEVtG,MAAMuD,eAAegD,GACrBT,EAAUA,EAAQrH,IAAI8H,EACxB,MACET,EAAUA,EAAQrH,IAAI6H,EAE1B,CACAR,EAAQ/D,SAASxB,KAAKqD,UACtB5D,MAAMuD,gBACR,CAGA,IAAIiD,EAAcjG,KAAKuB,KAAKoC,KAAK,YACjC,GAAIzC,EAAKgF,QAAS,CAChB,IAAIC,EAAc3G,EAAE,OAAQ,CAC1B4B,MAAO,4BACPC,KACE5B,MAAM0C,EAAE,MAAO,6BACf,IACA1C,MAAM0C,EACJ,MACA,iDACAc,QACA,MACA,YAAc/B,EAAKkF,WAAa,wBAGlCH,EAAYhN,OACdgN,EAAY3E,YAAY6E,GAExBA,EAAY3E,SAASxB,KAAKqD,SAE9B,CAG8B,UAA1BnC,EAAKyD,kBACP3E,KAAKyD,QAAQe,YAAY,UACrBtD,EAAK0D,cAAc3L,OACrB+G,KAAKyD,QAAQgB,SAAS,UAEtBzE,KAAKyD,QAAQe,YAAY,WAG3BxE,KAAKyD,QAAQgB,SAAS,SAE1B,GAEH,CAxeD,CAweG4B,O", "sources": ["webpack:///../../../../../node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///../../../../../node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:///./plugins.scss?64be", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///./PluginManager.js"], "sourcesContent": ["/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ruleSet[1].rules[3].use[1]!../../../../../node_modules/css-loader/dist/cjs.js!../../../../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].use[3]!../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[4]!./plugins.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"7690077b\", content, true, {});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "import './plugins.scss';\n\n(function ($) {\n  /** global: Craft */\n  /** global: Garnish */\n  Craft.PluginManager = Garnish.Base.extend(\n    {\n      init: function () {\n        this.getPluginLicenseInfo().then((response) => {\n          for (let handle in response) {\n            if (response.hasOwnProperty(handle)) {\n              if (!response[handle].isComposerInstalled) {\n                this.addUninstalledPluginRow(handle, response[handle]);\n              } else {\n                new Plugin(this, $('#plugin-' + handle)).update(\n                  response[handle]\n                );\n              }\n            }\n          }\n        });\n      },\n\n      getPluginLicenseInfo: function () {\n        return new Promise(function (resolve, reject) {\n          Craft.sendApiRequest('GET', 'cms-licenses', {\n            params: {\n              include: 'plugins',\n            },\n          })\n            .then((response) => {\n              let data = {\n                pluginLicenses: response.license.pluginLicenses || [],\n              };\n              Craft.sendActionRequest('POST', 'app/get-plugin-license-info', {\n                data,\n              })\n                .then((response) => {\n                  resolve(response.data);\n                })\n                .catch(() => {\n                  reject();\n                });\n            })\n            .catch(reject);\n        });\n      },\n\n      addUninstalledPluginRow: function (handle, info) {\n        let $table = $('#plugins');\n        if (!$table.length) {\n          $table = $('<table/>', {\n            id: 'plugins',\n            class: 'data fullwidth collapsible',\n            html: '<tbody></tbody>',\n          });\n          $('#no-plugins').replaceWith($table);\n        }\n\n        const $row = $('<tr/>', {\n          data: {\n            handle: handle,\n          },\n        })\n          .appendTo($table.children('tbody'))\n          .append(\n            $('<th/>').append(\n              $('<div/>', {class: 'plugin-infos'})\n                .append(\n                  $('<div/>', {class: 'icon'}).append(\n                    $('<img/>', {src: info.iconUrl})\n                  )\n                )\n                .append(\n                  $('<div/>', {class: 'plugin-details'})\n                    .append($('<h2/>', {text: info.name}))\n                    .append(\n                      info.description\n                        ? $('<p/>', {text: info.description})\n                        : $()\n                    )\n                    .append(\n                      info.documentationUrl\n                        ? $('<p/>', {class: 'links'}).append(\n                            $('<a/>', {\n                              href: info.documentationUrl,\n                              target: '_blank',\n                              text: Craft.t('app', 'Documentation'),\n                            })\n                          )\n                        : $()\n                    )\n                    .append(\n                      $('<div/>', {class: 'flex license-key'}).append(\n                        $('<div />', {class: 'pane'}).append(\n                          $('<input/>', {\n                            class: 'text code',\n                            size: 29,\n                            maxlength: 29,\n                            value: Craft.PluginManager.normalizeUserKey(\n                              info.licenseKey\n                            ),\n                            readonly: true,\n                            disabled: true,\n                          })\n                        )\n                      )\n                    )\n                )\n            )\n          )\n          .append(\n            $('<td/>', {\n              class: 'nowrap',\n              'data-title': Craft.t('app', 'Status'),\n            })\n              .append($('<span/>', {class: 'status'}))\n              .append(\n                $('<span/>', {class: 'light', text: Craft.t('app', 'Missing')})\n              )\n          )\n          .append(\n            info.latestVersion\n              ? $('<td/>', {\n                  class: 'nowrap thin',\n                  'data-title': Craft.t('app', 'Action'),\n                }).append(\n                  $('<form/>', {\n                    method: 'post',\n                    'accept-charset': 'UTF-8',\n                  })\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'action',\n                        value: 'pluginstore/install',\n                      })\n                    )\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'packageName',\n                        value: info.packageName,\n                      })\n                    )\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'handle',\n                        value: handle,\n                      })\n                    )\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'edition',\n                        value: info.licensedEdition,\n                      })\n                    )\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'version',\n                        value: info.latestVersion,\n                      })\n                    )\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'licenseKey',\n                        value: info.licenseKey,\n                      })\n                    )\n                    .append(\n                      $('<input/>', {\n                        type: 'hidden',\n                        name: 'return',\n                        value: 'settings/plugins',\n                      })\n                    )\n                    .append(Craft.getCsrfInput())\n                    .append(\n                      $('<div/>', {class: 'btngroup'})\n                        .append(\n                          $('<button/>', {\n                            type: 'button',\n                            class: 'btn menubtn',\n                            'data-icon': 'settings',\n                          })\n                        )\n                        .append(\n                          $('<div/>', {\n                            class: 'menu',\n                            'data-align': 'right',\n                          }).append(\n                            $('<ul/>').append(\n                              $('<li/>').append(\n                                $('<a/>', {\n                                  class: 'formsubmit',\n                                  text: Craft.t('app', 'Install'),\n                                })\n                              )\n                            )\n                          )\n                        )\n                    )\n                )\n              : $()\n          );\n        Craft.initUiElements($row);\n      },\n    },\n    {\n      normalizeUserKey: function (key) {\n        if (typeof key !== 'string' || key === '') {\n          return '';\n        }\n        if (key[0] === '$') {\n          return key;\n        }\n        return key.replace(/.{4}/g, '$&-').substring(0, 29).toUpperCase();\n      },\n    }\n  );\n\n  const Plugin = Garnish.Base.extend({\n    manager: null,\n    $row: null,\n    $details: null,\n    $keyContainer: null,\n    $keyInput: null,\n    $spinner: null,\n    $buyBtn: null,\n    handle: null,\n    updateTimeout: null,\n\n    init: function (manager, $row) {\n      this.manager = manager;\n      this.$row = $row;\n      this.$details = this.$row.find('.plugin-details');\n      this.$keyContainer = $row.find('.license-key');\n      this.$keyInput = this.$keyContainer\n        .find('input.text')\n        .removeAttr('readonly');\n      this.$buyBtn = this.$keyContainer.find('.btn');\n      this.$spinner = $row.find('.spinner');\n      this.handle = this.$row.data('handle');\n      this.addListener(this.$keyInput, 'focus', 'onKeyFocus');\n      this.addListener(this.$keyInput, 'input', 'onKeyChange');\n    },\n\n    getKey: function () {\n      return this.$keyInput.val().replace(/\\-/g, '').toUpperCase();\n    },\n\n    onKeyFocus: function () {\n      this.$keyInput.select();\n    },\n\n    onKeyChange: function () {\n      if (this.updateTimeout) {\n        clearTimeout(this.updateTimeout);\n      }\n      const key = this.getKey();\n      if (\n        key.length === 0 ||\n        key.length === 24 ||\n        (key.length > 1 && key[0] === '$')\n      ) {\n        // normalize\n        const userKey = Craft.PluginManager.normalizeUserKey(key);\n        this.$keyInput.val(userKey);\n        this.updateTimeout = setTimeout(\n          this.updateLicenseStatus.bind(this),\n          100\n        );\n      }\n    },\n\n    updateLicenseStatus: function () {\n      this.$spinner.removeClass('hidden');\n\n      let data = {handle: this.handle, key: this.getKey()};\n      Craft.sendActionRequest('POST', 'app/update-plugin-license', {data}).then(\n        () => {\n          this.manager.getPluginLicenseInfo().then((response) => {\n            this.$spinner.addClass('hidden');\n            this.update(response[this.handle]);\n          });\n        }\n      );\n    },\n\n    update: function (info) {\n      // update the status icon\n      const $oldIcon = this.$row.find('.license-key-status');\n      if (info.licenseKeyStatus == 'valid' || info.licenseIssues.length) {\n        const $newIcon = $('<span/>', {\n          class:\n            'license-key-status ' +\n            (info.licenseIssues.length === 0 ? 'valid' : ''),\n        });\n        if ($oldIcon.length) {\n          $oldIcon.replaceWith($newIcon);\n        } else {\n          $newIcon.appendTo(this.$row.find('.icon'));\n        }\n      } else if ($oldIcon.length) {\n        $oldIcon.remove();\n      }\n\n      // add the edition/trial badge\n      const $oldEdition = this.$row.find('.edition');\n      if (info.hasMultipleEditions || info.isTrial) {\n        const $newEdition = info.upgradeAvailable\n          ? $('<a/>', {\n              href: Craft.getUrl('plugin-store/' + this.handle),\n              class: 'edition',\n            })\n          : $('<div/>', {class: 'edition'});\n        if (info.hasMultipleEditions) {\n          $('<div/>', {class: 'edition-name', text: info.edition}).appendTo(\n            $newEdition\n          );\n        }\n        if (info.isTrial) {\n          $('<div/>', {\n            class: 'edition-trial',\n            text: Craft.t('app', 'Trial'),\n          }).appendTo($newEdition);\n        }\n        if ($oldEdition.length) {\n          $oldEdition.replaceWith($newEdition);\n        } else {\n          $newEdition.insertBefore(this.$row.find('.version'));\n        }\n      } else if ($oldEdition.length) {\n        $oldEdition.remove();\n      }\n\n      // show the license key?\n      const showLicenseKey =\n        info.licenseKey || info.licenseKeyStatus !== 'unknown';\n      if (showLicenseKey) {\n        this.$keyContainer.removeClass('hidden');\n        if (info.licenseKey && !this.$keyInput.val().match(/^\\$/)) {\n          this.$keyInput.val(\n            Craft.PluginManager.normalizeUserKey(info.licenseKey)\n          );\n        }\n      } else {\n        this.$keyContainer.addClass('hidden');\n      }\n\n      // update the license key input class\n      if (showLicenseKey && info.licenseIssues.length) {\n        this.$keyInput.addClass('error');\n      } else {\n        this.$keyInput.removeClass('error');\n      }\n\n      // add the error message\n      this.$row.find('p.error').remove();\n      if (info.licenseIssues.length) {\n        let $issues = $();\n        for (let i = 0; i < info.licenseIssues.length; i++) {\n          let message;\n          switch (info.licenseIssues[i]) {\n            case 'no_trials':\n              message = Craft.t(\n                'app',\n                'Plugin trials are not allowed on this domain.'\n              );\n              break;\n            case 'wrong_edition':\n              message =\n                Craft.t('app', 'This license is for the {name} edition.', {\n                  name:\n                    info.licensedEdition.charAt(0).toUpperCase() +\n                    info.licensedEdition.substring(1),\n                }) +\n                ' <button type=\"button\" class=\"btn submit small formsubmit\">' +\n                Craft.t('app', 'Switch') +\n                '</button>';\n              break;\n            case 'mismatched':\n              message = Craft.t(\n                'app',\n                'This license is tied to another Craft install. Visit {accountLink} to detach it, or <a href=\"{buyUrl}\">buy a new license</a>',\n                {\n                  accountLink:\n                    '<a href=\"https://console.craftcms.com\" rel=\"noopener\" target=\"_blank\">console.craftcms.com</a>',\n                  buyUrl: Craft.getCpUrl(\n                    `plugin-store/buy/${this.handle}/${info.edition}`\n                  ),\n                }\n              );\n              break;\n            case 'astray':\n              message = Craft.t(\n                'app',\n                'This license isn’t allowed to run version {version}.',\n                {\n                  version: info.version,\n                }\n              );\n              break;\n            case 'required':\n              message = Craft.t('app', 'A license key is required.');\n              break;\n            default:\n              message = Craft.t('app', 'Your license key is invalid.');\n          }\n\n          const $p = $('<p/>', {class: 'error', html: message});\n          if (info.licenseIssues[i] === 'wrong_edition') {\n            const $form = $('<form/>', {\n              method: 'post',\n              'accept-charset': 'UTF-8',\n            })\n              .append(Craft.getCsrfInput())\n              .append(\n                $('<input/>', {\n                  type: 'hidden',\n                  name: 'action',\n                  value: 'plugins/switch-edition',\n                })\n              )\n              .append(\n                $('<input/>', {\n                  type: 'hidden',\n                  name: 'pluginHandle',\n                  value: this.handle,\n                })\n              )\n              .append(\n                $('<input/>', {\n                  type: 'hidden',\n                  name: 'edition',\n                  value: info.licensedEdition,\n                })\n              )\n              .append($p);\n\n            Craft.initUiElements($form);\n            $issues = $issues.add($form);\n          } else {\n            $issues = $issues.add($p);\n          }\n        }\n        $issues.appendTo(this.$details);\n        Craft.initUiElements();\n      }\n\n      // add the expired badge\n      var $oldExpired = this.$row.find('.expired');\n      if (info.expired) {\n        var $newExpired = $('<p/>', {\n          class: 'warning with-icon expired',\n          html:\n            Craft.t('app', 'This license has expired.') +\n            ' ' +\n            Craft.t(\n              'app',\n              '<a>Renew now</a> for another year of updates.'\n            ).replace(\n              '<a>',\n              '<a href=\"' + info.renewalUrl + '\" target=\"_blank\">'\n            ),\n        });\n        if ($oldExpired.length) {\n          $oldExpired.replaceWith($newExpired);\n        } else {\n          $newExpired.appendTo(this.$details);\n        }\n      }\n\n      // show/hide the Buy button\n      if (info.licenseKeyStatus === 'trial') {\n        this.$buyBtn.removeClass('hidden');\n        if (info.licenseIssues.length) {\n          this.$buyBtn.addClass('submit');\n        } else {\n          this.$buyBtn.removeClass('submit');\n        }\n      } else {\n        this.$buyBtn.addClass('hidden');\n      }\n    },\n  });\n})(jQuery);\n"], "names": ["listToStyles", "parentId", "list", "styles", "newStyles", "i", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "__esModule", "default", "module", "locals", "exports", "add", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "prop", "prototype", "hasOwnProperty", "call", "$", "Craft", "Plugin<PERSON>anager", "Garnish", "Base", "extend", "init", "_this", "this", "getPluginLicenseInfo", "then", "response", "handle", "isComposerInstalled", "Plugin", "addUninstalledPluginRow", "Promise", "resolve", "reject", "sendApiRequest", "params", "include", "data", "pluginLicenses", "license", "sendActionRequest", "info", "$table", "class", "html", "replaceWith", "$row", "appendTo", "children", "append", "src", "iconUrl", "text", "name", "description", "documentationUrl", "href", "target", "t", "size", "maxlength", "value", "normalizeUserKey", "licenseKey", "readonly", "disabled", "latestVersion", "method", "packageName", "licensedEdition", "getCsrfInput", "initUiElements", "replace", "substring", "toUpperCase", "manager", "$details", "$keyContainer", "$keyInput", "$spinner", "$buyBtn", "updateTimeout", "find", "removeAttr", "addListener", "<PERSON><PERSON><PERSON>", "val", "onKeyFocus", "select", "onKeyChange", "clearTimeout", "<PERSON><PERSON><PERSON>", "setTimeout", "updateLicenseStatus", "_this2", "removeClass", "addClass", "$oldIcon", "licenseKeyStatus", "licenseIssues", "$newIcon", "$oldEdition", "hasMultipleEditions", "isTrial", "$newEdition", "upgradeAvailable", "getUrl", "edition", "showLicenseKey", "match", "$issues", "message", "char<PERSON>t", "accountLink", "buyUrl", "getCpUrl", "concat", "version", "$p", "$form", "$oldExpired", "expired", "$newExpired", "renewalUrl", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}