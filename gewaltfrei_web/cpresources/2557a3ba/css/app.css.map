{"version": 3, "file": "css/app.css", "mappings": "AACA,2BACE,aAEF,6CACE,cACA,eAEF,wCAEE,eAAc,CADd,iBACe,CAEjB,wCACE,kBAEF,mCAEE,eACA,kBAFA,SAEA,CAEF,sCACE,6BAEF,2CAEE,mBADA,eACA,CAEF,wCACE,kBAEF,qCAIE,cADA,gBADA,uBADA,oBAGA,CAEF,2CACE,yBAA0B,CAC1B,sBAAuB,CAEzB,wCACE,mBAEF,sCAEE,2BACA,4BAFA,mBAEA,CC7CF,0CACE,YCDF,aACE,oBAOF,iBAEE,gBADA,oBACA,CCVF,4BACE,kBAEA,kBADA,oBACA,CAGF,sCACE,kBACA,oBAGF,mBACE,WAGF,sBACE,gBAGF,0BACE,mBAGF,iDACE,mBAGF,mCACE,SAGF,sBACE,UAGF,8BACE,yBAGF,qEACE,6BAGF,yBACE,iBAGF,mDACE,gBAGF,wBACE,sBACA,6BACA,SACA,oBACA,gBACA,oBACA,iBACA,wCAGF,uBACE", "sources": ["webpack:///../../../../../node_modules/vuetable-2/src/components/Vuetable.vue", "webpack:///../../../../../packages/craftcms-vue/admintable/components/AdminTableCheckbox.vue", "webpack:///../../../../../packages/craftcms-vue/admintable/components/AdminTableDetailRow.vue", "webpack:///../../../../../packages/craftcms-vue/admintable/App.vue"], "sourcesContent": ["\n[v-cloak] {\n  display: none;\n}\n.vuetable th.sortable:hover {\n  color: #2185d0;\n  cursor: pointer;\n}\n.vuetable-body-wrapper {\n  position:relative;\n  overflow-y:auto;\n}\n.vuetable-head-wrapper {\n  overflow-x: hidden;\n}\n.vuetable-actions {\n  width: 15%;\n  padding: 12px 0px;\n  text-align: center;\n}\n.vuetable-pagination {\n  background: #f9fafb !important;\n}\n.vuetable-pagination-info {\n  margin-top: auto;\n  margin-bottom: auto;\n}\n.vuetable-empty-result {\n  text-align: center;\n}\n.vuetable-clip-text {\n  white-space: pre-wrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  display: block;\n}\n.vuetable-semantic-no-top {\n  border-top:none !important;\n  margin-top:0 !important;\n}\n.vuetable-fixed-layout {\n  table-layout: fixed;\n}\n.vuetable-gutter-col {\n  padding: 0 !important;\n  border-left: none  !important;\n  border-right: none  !important;\n}\n", "\n.table-disabled-checkbox {\n  opacity: 0.25;\n}\n", "\n.detail-list {\n  padding: 0.2rem 0.5rem;\n}\n\n.detail-list-bg {\n  //background: #f1f5f8;\n}\n\n.detail-list-key {\n  padding-right: 0.25rem;\n  font-weight: bold;\n}\n", "\n.tableview td.checkbox-cell {\n  padding-right: 7px;\n  width: 12px !important;\n  position: relative;\n}\n\n.tableview td.checkbox-cell .checkbox {\n  position: absolute;\n  top: calc(50% - 6px);\n}\n\n.tableview.loading {\n  opacity: 0.3;\n}\n\n.tableview .cell-bold {\n  font-weight: bold;\n}\n\n.vue-admin-table .toolbar {\n  margin-bottom: 32px;\n}\n\n.vue-admin-table.vue-admin-table-padded .toolbar {\n  margin-bottom: 14px;\n}\n\n.vue-admin-table-padded .tablepane {\n  margin: 0;\n}\n\n.vue-admin-table-drag {\n  opacity: 0;\n}\n\ntable thead th.sortable:hover {\n  background-color: #f9f9f9;\n}\n\ntable.data.vue-admin-table-dragging tbody tr:not(.disabled):hover td {\n  background-color: transparent;\n}\n\n.vue-admin-table-buttons {\n  margin-left: auto;\n}\n\n.vue-admin-table-buttons .flex:not(.flex-nowrap) > * {\n  margin-bottom: 0;\n}\n\n.vue-admin-table-footer {\n  background-color: #fff;\n  border-top: 1px solid #f3f7fc;\n  bottom: 0;\n  margin-bottom: -14px;\n  margin-top: 14px;\n  padding-bottom: 14px;\n  padding-top: 14px;\n  position: sticky;\n}\n\n.detail-cursor-pointer {\n  cursor: pointer;\n}\n"], "names": [], "sourceRoot": ""}