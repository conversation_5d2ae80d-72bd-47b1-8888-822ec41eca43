!function(){var t;t=j<PERSON><PERSON><PERSON>,Craft.RecentEntriesWidget=Garnish.Base.extend({params:null,$widget:null,$body:null,$container:null,$list:null,hasEntries:null,init:function(i,e){this.params=e,this.$widget=t("#widget"+i),this.$body=this.$widget.find(".body:first"),this.$container=this.$body.find(".recententries-container:first"),this.$list=this.$container.find("ol:first"),this.hasEntries=!!this.$list.length,this.$widget.data("widget").on("destroy",this.destroy.bind(this)),Craft.RecentEntriesWidget.instances.push(this)},addEntry:function(i){this.hasEntries||(this.$list=t("<ol/>").appendTo(this.$container)),this.$list.prepend('<li class="widget__list-item">'+'<a href="'.concat(i.url,'">')+Craft.escapeHtml(i.title)+'</a> <span class="light">'+Craft.escapeHtml((i.dateCreated?Craft.formatDate(i.dateCreated):"")+(i.dateCreated&&i.username&&Craft.edition==Craft.Pro?", ":"")+(i.username&&Craft.edition==Craft.Pro?i.username:""))+"</span></li>"),this.hasEntries||(this.$container.find(".zilch").remove(),this.hasEntries=!0)},destroy:function(){Craft.RecentEntriesWidget.instances.splice(t.inArray(this,Craft.RecentEntriesWidget.instances),1),this.base()}},{instances:[]})}();
//# sourceMappingURL=RecentEntriesWidget.js.map