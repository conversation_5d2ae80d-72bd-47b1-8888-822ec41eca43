{"version": 3, "file": "RecentEntriesWidget.js", "mappings": "YAAA,IAAWA,IAuERC,OApEDC,MAAMC,oBAAsBC,QAAQC,KAAKC,OACvC,CACEC,OAAQ,KACRC,QAAS,KACTC,MAAO,KACPC,WAAY,KACZC,MAAO,KACPC,WAAY,KAEZC,KAAM,SAAUC,EAAUP,GACxBQ,KAAKR,OAASA,EACdQ,KAAKP,QAAUR,EAAE,UAAYc,GAC7BC,KAAKN,MAAQM,KAAKP,QAAQQ,KAAK,eAC/BD,KAAKL,WAAaK,KAAKN,MAAMO,KAAK,kCAClCD,KAAKJ,MAAQI,KAAKL,WAAWM,KAAK,YAClCD,KAAKH,aAAeG,KAAKJ,MAAMM,OAE/BF,KAAKP,QAAQU,KAAK,UAAUC,GAAG,UAAWJ,KAAKK,QAAQC,KAAKN,OAE5Db,MAAMC,oBAAoBmB,UAAUC,KAAKR,KAC3C,EAEAS,SAAU,SAAUC,GACbV,KAAKH,aAERG,KAAKJ,MAAQX,EAAE,SAAS0B,SAASX,KAAKL,aAGxCK,KAAKJ,MAAMgB,QACT,iCAAgC,YAAAC,OAClBH,EAAMI,IAAG,MACrB3B,MAAM4B,WAAWL,EAAMM,OAFzB,4BAKE7B,MAAM4B,YACHL,EAAMO,YAAc9B,MAAM+B,WAAWR,EAAMO,aAAe,KACxDP,EAAMO,aACPP,EAAMS,UACNhC,MAAMiC,SAAWjC,MAAMkC,IACnB,KACA,KACHX,EAAMS,UAAYhC,MAAMiC,SAAWjC,MAAMkC,IACtCX,EAAMS,SACN,KAdV,gBAqBGnB,KAAKH,aACRG,KAAKL,WAAWM,KAAK,UAAUqB,SAC/BtB,KAAKH,YAAa,EAEtB,EAEAQ,QAAS,WACPlB,MAAMC,oBAAoBmB,UAAUgB,OAClCtC,EAAEuC,QAAQxB,KAAMb,MAAMC,oBAAoBmB,WAC1C,GAEFP,KAAKyB,MACP,GAEF,CACElB,UAAW,I", "sources": ["webpack:///./RecentEntriesWidget.js"], "sourcesContent": ["(function ($) {\n  /** global: Craft */\n  /** global: Garnish */\n  Craft.RecentEntriesWidget = Garnish.Base.extend(\n    {\n      params: null,\n      $widget: null,\n      $body: null,\n      $container: null,\n      $list: null,\n      hasEntries: null,\n\n      init: function (widgetId, params) {\n        this.params = params;\n        this.$widget = $('#widget' + widgetId);\n        this.$body = this.$widget.find('.body:first');\n        this.$container = this.$body.find('.recententries-container:first');\n        this.$list = this.$container.find('ol:first');\n        this.hasEntries = !!this.$list.length;\n\n        this.$widget.data('widget').on('destroy', this.destroy.bind(this));\n\n        Craft.RecentEntriesWidget.instances.push(this);\n      },\n\n      addEntry: function (entry) {\n        if (!this.hasEntries) {\n          // Create the list first\n          this.$list = $('<ol/>').appendTo(this.$container);\n        }\n\n        this.$list.prepend(\n          '<li class=\"widget__list-item\">' +\n            `<a href=\"${entry.url}\">` +\n            Craft.escapeHtml(entry.title) +\n            '</a> ' +\n            '<span class=\"light\">' +\n            Craft.escapeHtml(\n              (entry.dateCreated ? Craft.formatDate(entry.dateCreated) : '') +\n                (entry.dateCreated &&\n                entry.username &&\n                Craft.edition == Craft.Pro\n                  ? ', '\n                  : '') +\n                (entry.username && Craft.edition == Craft.Pro\n                  ? entry.username\n                  : '')\n            ) +\n            '</span>' +\n            '</li>'\n        );\n\n        // Also animate the \"No entries exist\" text out of view\n        if (!this.hasEntries) {\n          this.$container.find('.zilch').remove();\n          this.hasEntries = true;\n        }\n      },\n\n      destroy: function () {\n        Craft.RecentEntriesWidget.instances.splice(\n          $.inArray(this, Craft.RecentEntriesWidget.instances),\n          1\n        );\n        this.base();\n      },\n    },\n    {\n      instances: [],\n    }\n  );\n})(jQuery);\n"], "names": ["$", "j<PERSON><PERSON><PERSON>", "Craft", "RecentEntriesWidget", "Garnish", "Base", "extend", "params", "$widget", "$body", "$container", "$list", "hasEntries", "init", "widgetId", "this", "find", "length", "data", "on", "destroy", "bind", "instances", "push", "addEntry", "entry", "appendTo", "prepend", "concat", "url", "escapeHtml", "title", "dateCreated", "formatDate", "username", "edition", "Pro", "remove", "splice", "inArray", "base"], "sourceRoot": ""}