!function(){var t;t=j<PERSON><PERSON><PERSON>,Craft.FeedWidget=Garnish.Base.extend({$widget:null,init:function(a,e,i){var d=this;this.$widget=t("#widget"+a),this.$widget.addClass("loading"),axios.get("https://feed-proxy.craftcms.com/",{params:{url:e}}).then((function(a){d.$widget.removeClass("loading"),d.$widget.find("ol").attr("dir",a.data.direction);var i=d.$widget.find("li");a.data.items=(a.data.items||[]).filter((function(t){return t.permalink.match(/^https?:\/\//)})),a.data.items.forEach((function(a,e){var d=t(i[e]),r=t("<a/>",{href:a.permalink,target:"_blank",text:a.title}).get(0).outerHTML+" ";a.date&&(r+='<span class="light nowrap">'+Craft.formatDate(a.date)+"</span>"),d.html(r)})),Craft.sendActionRequest("POST","dashboard/cache-feed-data",{data:{url:e,data:a.data}})})).catch((function(){d.$widget.removeClass("loading"),Craft.cp.displayError(Craft.t("app","Could not load the feed"))}))}})}();
//# sourceMappingURL=FeedWidget.js.map