{"version": 3, "file": "FeedWidget.js", "mappings": "YAAA,IAAWA,IA0DRC,OAvDDC,MAAMC,WAAaC,QAAQC,KAAKC,OAAO,CACrCC,QAAS,KAETC,KAAM,SAAUC,EAAUC,EAAKC,GAAO,IAAAC,EAAA,KACpCC,KAAKN,QAAUP,EAAE,UAAYS,GAC7BI,KAAKN,QAAQO,SAAS,WAGtBC,MACGC,IAAI,mCAAoC,CACvCC,OAAQ,CACNP,IAAKA,KAGRQ,MAAK,SAACC,GACLP,EAAKL,QAAQa,YAAY,WACzBR,EAAKL,QAAQc,KAAK,MAAMC,KAAK,MAAOH,EAASI,KAAKC,WAClD,IAAIC,EAAab,EAAKL,QAAQc,KAAK,MAEnCF,EAASI,KAAKG,OAASP,EAASI,KAAKG,OAAS,IAAIC,QAAO,SAACC,GAAI,OAC5DA,EAAKC,UAAUC,MAAM,eAAe,IAEtCX,EAASI,KAAKG,MAAMK,SAAQ,SAACH,EAAMI,GACjC,IAAMC,EAAYjC,EAAEyB,EAAWO,IAC3BE,EACFlC,EAAE,OAAQ,CACRmC,KAAMP,EAAKC,UACXO,OAAQ,SACRC,KAAMT,EAAKU,QACVtB,IAAI,GAAGuB,UAAY,IAEpBX,EAAKY,OACPN,GACE,8BACAhC,MAAMuC,WAAWb,EAAKY,MACtB,WAGJP,EAAUS,KAAKR,EACjB,IAGAhC,MAAMyC,kBAAkB,OAAQ,4BAA6B,CAC3DpB,KAAM,CACJb,IAAKA,EACLa,KAAMJ,EAASI,OAGrB,IAAE,OACK,WACLX,EAAKL,QAAQa,YAAY,WACzBlB,MAAM0C,GAAGC,aAAa3C,MAAM4C,EAAE,MAAO,2BACvC,GACJ,G", "sources": ["webpack:///./FeedWidget.js"], "sourcesContent": ["(function ($) {\n  /** global: Craft */\n  /** global: Garnish */\n  Craft.FeedWidget = Garnish.Base.extend({\n    $widget: null,\n\n    init: function (widgetId, url, limit) {\n      this.$widget = $('#widget' + widgetId);\n      this.$widget.addClass('loading');\n\n      // Get the feed data\n      axios\n        .get('https://feed-proxy.craftcms.com/', {\n          params: {\n            url: url,\n          },\n        })\n        .then((response) => {\n          this.$widget.removeClass('loading');\n          this.$widget.find('ol').attr('dir', response.data.direction);\n          let $listItems = this.$widget.find('li');\n          // Filter out any invalid links\n          response.data.items = (response.data.items || []).filter((item) =>\n            item.permalink.match(/^https?:\\/\\//)\n          );\n          response.data.items.forEach((item, i) => {\n            const $listItem = $($listItems[i]);\n            let widgetHtml =\n              $('<a/>', {\n                href: item.permalink,\n                target: '_blank',\n                text: item.title,\n              }).get(0).outerHTML + ' ';\n\n            if (item.date) {\n              widgetHtml +=\n                '<span class=\"light nowrap\">' +\n                Craft.formatDate(item.date) +\n                '</span>';\n            }\n\n            $listItem.html(widgetHtml);\n          });\n\n          // Now cache the data\n          Craft.sendActionRequest('POST', 'dashboard/cache-feed-data', {\n            data: {\n              url: url,\n              data: response.data,\n            },\n          });\n        })\n        .catch(() => {\n          this.$widget.removeClass('loading');\n          Craft.cp.displayError(Craft.t('app', 'Could not load the feed'));\n        });\n    },\n  });\n})(jQuery);\n"], "names": ["$", "j<PERSON><PERSON><PERSON>", "Craft", "FeedWidget", "Garnish", "Base", "extend", "$widget", "init", "widgetId", "url", "limit", "_this", "this", "addClass", "axios", "get", "params", "then", "response", "removeClass", "find", "attr", "data", "direction", "$listItems", "items", "filter", "item", "permalink", "match", "for<PERSON>ach", "i", "$listItem", "widgetHtml", "href", "target", "text", "title", "outerHTML", "date", "formatDate", "html", "sendActionRequest", "cp", "displayError", "t"], "sourceRoot": ""}