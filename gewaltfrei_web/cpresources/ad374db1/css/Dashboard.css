.widget{-webkit-perspective:1000px;perspective:1000px;position:relative;transition:opacity .2s linear,-webkit-transform .2s linear;-o-transition:opacity .2s linear,-o-transform .2s linear;transition:opacity .2s linear,transform .2s linear;transition:opacity .2s linear,transform .2s linear,-webkit-transform .2s linear,-o-transform .2s linear}.widget.scaleout{opacity:0;-webkit-transform:scale(.5);-o-transform:scale(.5);transform:scale(.5)}.widget>.back,.widget>.front{-webkit-backface-visibility:hidden;backface-visibility:hidden;-o-transition:.6s;transition:.6s}.widget:not(.flipped)>.front{-webkit-transform:rotateY(0deg);transform:rotateY(0deg)}.widget:not(.flipped)>.front>.pane>.icon.settings:hover{opacity:1!important}.widget:not(.flipped)>.back{-webkit-transform:rotateY(-180deg);transform:rotateY(-180deg)}.widget:not(.flipped):hover>.front>.pane>.icon.settings{opacity:.8}.widget:not(.flipped) .front>.pane>.icon.settings:focus{opacity:.8}.widget.flipped>.front{-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}.widget.flipped>.back{-webkit-transform:rotateY(0deg);transform:rotateY(0deg)}.widget.flipped>.front,.widget:not(.flipped)>.back{opacity:0}.widget>.front>.pane>.icon.settings{-webkit-align-items:center;align-items:center;cursor:pointer;display:-webkit-flex;display:flex;height:var(--touch-target-size);-webkit-justify-content:center;justify-content:center;opacity:0;position:absolute;top:12px;-o-transition:opacity .2s linear;transition:opacity .2s linear;width:var(--touch-target-size)}body.ltr .widget>.front>.pane>.icon.settings{right:12px}body.rtl .widget>.front>.pane>.icon.settings{left:12px}.widget>.front>.pane>.spinner.body-loading{display:none}.widget>.back{left:0;position:absolute;top:0;width:100%}.widget>.back>.pane{background:var(--gray-050)}.widget>.back>.pane>.errors{margin-top:24px}.widget.loading>.front>.pane>.spinner.body-loading{display:block}body.ltr .widget.loading>.front>.pane>.spinner.body-loading{float:right}body.rtl .widget.loading>.front>.pane>.spinner.body-loading{float:left}body.ltr .widget.loading>.front>.pane>.spinner.body-loading{margin-left:10px}body.rtl .widget.loading>.front>.pane>.spinner.body-loading{margin-right:10px}.widget.loading-new.new>.front>.pane{height:252px}.widget.loading-new.new>.front>.pane>.spinner.body-loading{display:block;left:calc(50% - 12px);position:absolute;top:calc(50% - 15px)}.widget .widget-heading{margin-bottom:15px}.widget .widget-heading h2{margin-bottom:3px}.widget .widget-heading h5{margin-top:0}.widget form+.errors{margin-top:15px}body ol.widget__list{list-style-type:none;margin:0;padding:0}.widget__list-item{padding:7px 0}.craft\\widgets\\mydrafts .widget__list-item{padding:0}.craft\\widgets\\recententries .body .table{table-layout:fixed}.craft\\widgets\\newusers .chart{height:200px;margin:0}.newwidgetmenu ul li a{position:relative}body.ltr .newwidgetmenu ul li a{padding-left:40px}body.rtl .newwidgetmenu ul li a{padding-right:40px}.newwidgetmenu ul li a .icon{position:absolute;top:11px;width:16px}body.ltr .newwidgetmenu ul li a .icon{left:14px}body.rtl .newwidgetmenu ul li a .icon{right:14px}.newwidgetmenu ul li a .icon svg,.widgetmanagerhud-icon svg{height:16px;width:16px}.newwidgetmenu ul li a .icon svg circle,.newwidgetmenu ul li a .icon svg ellipse,.newwidgetmenu ul li a .icon svg line,.newwidgetmenu ul li a .icon svg path,.newwidgetmenu ul li a .icon svg polygon,.newwidgetmenu ul li a .icon svg polyline,.newwidgetmenu ul li a .icon svg rect,.newwidgetmenu ul li a .icon svg text,.widgetmanagerhud-icon svg circle,.widgetmanagerhud-icon svg ellipse,.widgetmanagerhud-icon svg line,.widgetmanagerhud-icon svg path,.widgetmanagerhud-icon svg polygon,.widgetmanagerhud-icon svg polyline,.widgetmanagerhud-icon svg rect,.widgetmanagerhud-icon svg text{fill:var(--ui-control-color);stroke-width:0}.widgetmanagerhud{max-width:400px}.widgetmanagerhud .body{min-width:400px}@media only screen and (max-width:673px){.widgetmanagerhud{max-width:300px}.widgetmanagerhud .body{min-width:300px}.widgetmanagerhud .body .widgetmanagerhud-col-colspan-picker,.widgetmanagerhud .body .widgetmanagerhud-col-move{display:none}}
/*# sourceMappingURL=Dashboard.css.map*/