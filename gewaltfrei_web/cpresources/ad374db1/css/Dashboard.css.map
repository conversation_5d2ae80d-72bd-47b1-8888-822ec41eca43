{"version": 3, "file": "css/Dashboard.css", "mappings": "AAIA,QAEE,8CADA,kBAEA,+QAEA,iBACE,UACA,uEAGF,6BAEE,8DACA,iCAIA,6BACE,wDAEA,wDACE,oBAGJ,4BACE,8DAGF,wDACE,WAGF,wDACE,WAKF,uBACE,4DAEF,sBACE,wDAIJ,mDAEE,UAKE,oCAGE,8CAEA,eAJA,kCASA,gCARA,sDAEA,UAKA,kBAFA,SACA,+DAGA,+BCiQN,6CACE,UDvQmB,CCyQrB,6CACE,SD1QmB,CAQjB,2CACE,aAKN,cAGE,OAFA,kBACA,MAEA,WAEA,oBACE,2BAEA,4BACE,gBAQA,mDACE,cC0VR,4DACE,YAEF,4DACE,WAcF,4DACE,gBD3WM,CC6WR,4DACE,iBD9WM,CAQJ,qCACE,aAEA,2DACE,cAGA,sBAFA,kBACA,oBACA,CAMR,wBACE,mBAEA,2BACE,kBAEF,2BACE,aAIJ,qBACE,gBAKJ,qBACE,qBACA,SACA,UAGF,mBACE,cAEA,6CACE,UAKJ,4CACE,mBAIF,iCACE,aACA,SAIF,uBACE,kBCoUA,gCACE,iBDpUF,CCsUA,gCACE,kBDvUF,CAEA,6BACE,kBACA,SAEA,WCkJF,sCACE,SDpJc,CCsJhB,sCACE,UDvJc,CAKlB,4DAGE,YADA,UACA,CCEA,wkBAQE,4BDTgB,CCUhB,eDNJ,kBACE,gBAEA,wBACE,gBAIJ,yCACE,kBACE,gBAEA,wBACE,gBAMA,gHACE", "sources": ["webpack:///./dashboard.scss", "webpack:///../../../../../packages/craftcms-sass/_mixins.scss"], "sourcesContent": ["@charset \"UTF-8\";\n@import '@craftcms/sass/mixins';\n\n/* widgets */\n.widget {\n  position: relative;\n  perspective: 1000px;\n  transition: opacity linear 200ms, transform linear 200ms;\n\n  &.scaleout {\n    opacity: 0;\n    transform: scale(0.5);\n  }\n\n  & > .front,\n  & > .back {\n    backface-visibility: hidden;\n    transition: 600ms;\n  }\n\n  &:not(.flipped) {\n    & > .front {\n      transform: rotateY(0deg);\n\n      & > .pane > .icon.settings:hover {\n        opacity: 1 !important;\n      }\n    }\n    & > .back {\n      transform: rotateY(-180deg);\n    }\n\n    &:hover > .front > .pane > .icon.settings {\n      opacity: 0.8;\n    }\n\n    & .front > .pane > .icon.settings:focus {\n      opacity: 0.8;\n    }\n  }\n\n  &.flipped {\n    & > .front {\n      transform: rotateY(180deg);\n    }\n    & > .back {\n      transform: rotateY(0deg);\n    }\n  }\n\n  &:not(.flipped) > .back,\n  &.flipped > .front {\n    opacity: 0;\n  }\n\n  & > .front {\n    & > .pane {\n      & > .icon.settings {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        opacity: 0;\n        cursor: pointer;\n        @include right(12px);\n        top: 12px;\n        transition: opacity linear 200ms;\n        position: absolute;\n        height: var(--touch-target-size);\n        width: var(--touch-target-size);\n      }\n\n      & > .spinner.body-loading {\n        display: none;\n      }\n    }\n  }\n\n  & > .back {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n\n    & > .pane {\n      background: var(--gray-050);\n\n      & > .errors {\n        margin-top: 24px;\n      }\n    }\n  }\n\n  &.loading {\n    & > .front {\n      & > .pane {\n        & > .spinner.body-loading {\n          display: block;\n          @include floatright;\n          @include margin-left(10px);\n        }\n      }\n    }\n  }\n\n  &.loading-new.new {\n    & > .front {\n      & > .pane {\n        height: 252px;\n\n        & > .spinner.body-loading {\n          display: block;\n          position: absolute;\n          top: calc(50% - 15px);\n          left: calc(50% - 12px);\n        }\n      }\n    }\n  }\n\n  .widget-heading {\n    margin-bottom: 15px;\n\n    h2 {\n      margin-bottom: 3px;\n    }\n    h5 {\n      margin-top: 0;\n    }\n  }\n\n  form + .errors {\n    margin-top: 15px;\n  }\n}\n\n/* widget lists */\nbody ol.widget__list {\n  list-style-type: none;\n  margin: 0;\n  padding: 0;\n}\n\n.widget__list-item {\n  padding: 7px 0;\n\n  .craft\\\\widgets\\\\mydrafts & {\n    padding: 0;\n  }\n}\n\n/* Recent Entries widget */\n.craft\\\\widgets\\\\recententries .body .table {\n  table-layout: fixed;\n}\n\n/* New Users Widget */\n.craft\\\\widgets\\\\newusers .chart {\n  height: 200px;\n  margin: 0;\n}\n\n/* New Widget Menu */\n.newwidgetmenu ul li a {\n  position: relative;\n  @include padding-left(40px);\n\n  .icon {\n    position: absolute;\n    top: 11px;\n    @include left(14px);\n    width: 16px;\n  }\n}\n\n.newwidgetmenu ul li a .icon svg,\n.widgetmanagerhud-icon svg {\n  width: 16px;\n  height: 16px;\n  @include svg-mask(var(--ui-control-color));\n}\n\n/* New Widget Menu */\n.widgetmanagerhud {\n  max-width: 400px;\n\n  .body {\n    min-width: 400px;\n  }\n}\n\n@media only screen and (max-width: 673px) {\n  .widgetmanagerhud {\n    max-width: 300px;\n\n    .body {\n      min-width: 300px;\n\n      .widgetmanagerhud-col-colspan-picker {\n        display: none;\n      }\n\n      .widgetmanagerhud-col-move {\n        display: none;\n      }\n    }\n  }\n}\n", "$white: #fff;\n$black: #000;\n\n$grey050: hsl(212, 60%, 97%);\n$grey100: hsl(212, 50%, 93%);\n$grey200: hsl(212, 30%, 85%);\n$grey300: hsl(211, 13%, 65%);\n$grey350: hsl(211, 11%, 59%);\n$grey400: hsl(210, 10%, 53%);\n$grey500: hsl(211, 12%, 43%);\n$grey550: hsl(210, 13%, 40%);\n$grey600: hsl(209, 14%, 37%);\n$grey700: hsl(209, 18%, 30%);\n$grey800: hsl(209, 20%, 25%);\n$grey900: hsl(210, 24%, 16%);\n$grey1000: hsl(210, 24%, 10%);\n\n$blue050: #e3f8ff;\n$blue100: #b3ecff;\n$blue200: #81defd;\n$blue300: #5ed0fa;\n$blue400: #40c3f7;\n$blue500: #2bb0ed;\n$blue600: #1992d4;\n$blue700: #127fbf;\n$blue800: #0b69a3;\n$blue900: #035388;\n\n$cyan050: #e0fcff;\n$cyan100: #bef8fd;\n$cyan200: #87eaf2;\n$cyan300: #54d1db;\n$cyan400: #38bec9;\n$cyan500: #2cb1bc;\n$cyan600: #14919b;\n$cyan700: #0e7c86;\n$cyan800: #0a6c74;\n$cyan900: #044e54;\n\n$pink050: #ffe3ec;\n$pink100: #ffb8d2;\n$pink200: #ff8cba;\n$pink300: #f364a2;\n$pink400: #e8368f;\n$pink500: #da127d;\n$pink600: #bc0a6f;\n$pink700: #a30664;\n$pink800: #870557;\n$pink900: #620042;\n\n$red050: #ffe3e3;\n$red100: #ffbdbd;\n$red200: #ff9b9b;\n$red300: #f86a6a;\n$red400: #ef4e4e;\n$red500: #e12d39;\n$red600: #cf1124;\n$red700: #ab091e;\n$red800: #8a041a;\n$red900: #610316;\n\n$yellow050: #fffbea;\n$yellow100: #fff3c4;\n$yellow200: #fce588;\n$yellow300: #fadb5f;\n$yellow400: #f7c948;\n$yellow500: #f0b429;\n$yellow600: #de911d;\n$yellow700: #cb6e17;\n$yellow800: #b44d12;\n$yellow900: #8d2b0b;\n\n$teal050: #effcf6;\n$teal100: #c6f7e2;\n$teal200: #8eedc7;\n$teal300: #65d6ad;\n$teal400: #3ebd93;\n$teal500: #27ab83;\n$teal600: #199473;\n$teal700: #147d64;\n$teal800: #0c6b58;\n$teal900: #014d40;\n\n// submit button colors\n$primaryColor: $red500;\n$secondaryColor: $grey500;\n\n$inputColor: hsl(212, 25%, 50%);\n\n// text colors\n$textColor: $grey700;\n$mediumDarkTextColor: $grey550;\n$mediumTextColor: $grey550;\n$lightTextColor: $grey500;\n$linkColor: #1f5fea;\n\n// menu colors\n$menuOptionColor: $textColor;\n$menuOptionActiveColor: $white;\n$menuOptionActiveBackgroundColor: $grey500;\n\n// hairline colors\n$hairlineColor: transparentize($grey800, 0.9);\n$mediumHairlineColor: transparentize($grey600, 0.75);\n$darkHairlineColor: transparentize($grey400, 0.5);\n\n// focus colors\n$lightFocusColor: $blue300;\n$mediumFocusColor: $blue500;\n$darkFocusColor: #0f74b1;\n\n// focus rings\n$lightFocusRing: 0 0 0 1px $lightFocusColor,\n  0 0 0 3px transparentize($lightFocusColor, 0.3);\n$mediumFocusRing: 0 0 0 1px $mediumFocusColor,\n  0 0 0 3px transparentize($mediumFocusColor, 0.3);\n$darkFocusRing: 0 0 0 1px $darkFocusColor,\n  0 0 0 3px transparentize($darkFocusColor, 0.3);\n\n// selection colors\n$lightSelColor: $grey200;\n$darkSelColor: $grey500;\n\n// alert/notice colors\n$errorColor: $red600;\n$warningColor: $yellow800;\n$successColor: $teal500;\n$noticeColor: $blue800;\n\n// UI element styles\n$smallBorderRadius: 3px;\n$mediumBorderRadius: 4px;\n$largeBorderRadius: 5px;\n\n$menuBorderRadius: $mediumBorderRadius;\n$checkboxSize: 16px;\n$radioSize: 16px;\n\n@mixin sans-serif-font {\n  font-family: system-ui, BlinkMacSystemFont, -apple-system, 'Segoe UI',\n    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',\n    'Helvetica Neue', sans-serif;\n}\n\n@mixin fixed-width-font {\n  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier,\n    monospace;\n  font-size: 0.9em !important;\n}\n\n@function toRem($values...) {\n  $max: length($values);\n  $remValues: '';\n\n  @for $i from 1 through $max {\n    $remValues: #{$remValues + calc(nth($values, $i) / 16)}rem;\n\n    @if $i < $max {\n      $remValues: #{$remValues + ' '};\n    }\n  }\n\n  @return $remValues;\n}\n\n@mixin fontSize($size) {\n  font-size: toRem($size);\n}\n\n// Other\n\n@mixin focus-styles {\n  body:not(.reduce-focus-visibility) &:focus,\n  body.reduce-focus-visibility &:focus-visible {\n    @content;\n  }\n}\n\n@mixin svg-mask($color) {\n  rect,\n  circle,\n  ellipse,\n  line,\n  polyline,\n  polygon,\n  path,\n  text {\n    fill: $color;\n    stroke-width: 0;\n  }\n}\n\n@mixin icon {\n  font-family: 'Craft';\n  speak: none;\n  -webkit-font-feature-settings: 'liga', 'dlig';\n  -moz-font-feature-settings: 'liga=1, dlig=1';\n  -moz-font-feature-settings: 'liga', 'dlig';\n  -ms-font-feature-settings: 'liga', 'dlig';\n  -o-font-feature-settings: 'liga', 'dlig';\n  font-feature-settings: 'liga', 'dlig';\n  text-rendering: optimizeLegibility;\n  font-weight: normal;\n  font-variant: normal;\n  text-transform: none;\n  line-height: 1;\n  direction: ltr; // Fixes a rendering issue in Chrome/Win\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n\n  display: inline-block;\n  text-align: center;\n  font-style: normal;\n  vertical-align: middle;\n  word-wrap: normal !important;\n  user-select: none;\n\n  opacity: var(--icon-opacity);\n}\n\n@mixin angle($dir: down, $color: currentColor, $width: 2px) {\n  display: block;\n  content: '';\n  font-size: 0;\n  width: 7px;\n  height: 7px;\n  border: solid $color;\n  border-width: 0 $width $width 0;\n  opacity: 0.8;\n\n  @if $dir == up {\n    transform: rotate(225deg);\n  } @else if $dir == down {\n    transform: rotate(45deg);\n  } @else if $dir == left {\n    body.ltr & {\n      transform: rotate(135deg);\n    }\n    body.rtl & {\n      transform: rotate(-45deg);\n    }\n  } @else if $dir == right {\n    body.ltr & {\n      transform: rotate(-45deg);\n    }\n    body.rtl & {\n      transform: rotate(135deg);\n    }\n  }\n}\n\n@mixin clearafter {\n  content: '';\n  display: block;\n  height: 0;\n  clear: both;\n  visibility: hidden;\n}\n\n@mixin shadow {\n  box-shadow: 0 1px 5px -1px transparentize($grey900, 0.8);\n}\n\n@mixin pane {\n  background: $white;\n  box-shadow: 0 0 0 1px $grey200, 0 2px 12px transparentize($grey200, 0.5);\n\n  &:focus {\n    box-shadow: var(--focus-ring);\n  }\n}\n\n@mixin modal {\n  border-radius: $largeBorderRadius;\n  background-color: $white;\n  box-shadow: 0 25px 100px transparentize($grey900, 0.5) !important;\n}\n\n@mixin light-on-dark-text() {\n  // Make light on dark text sharp on Macs\n  // (sub-pixel antialiasing looks too bold/blurry with light text on dark background)\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  font-weight: 500;\n}\n\n@mixin light-focus-ring() {\n  --focus-ring: 0 0 0 1px hsl(var(--light-focus-hsl)),\n    0 0 0 3px hsla(var(--light-focus-hsl), 0.7);\n}\n\n@mixin custom-color-focus-ring($primary, $secondary: null) {\n  @if $secondary != null {\n    --focus-ring: 0 0 0 1px #{$primary}, 0 0 0 3px #{$secondary};\n  } @else {\n    --focus-ring: 0 0 0 3px #{$primary};\n  }\n}\n\n@mixin two-color-focus-ring($light-button: true) {\n  // Creates a two-color focus ring, with a white\n  // If button is light, the dark box shadow is adjacent to the button\n  // Else the light box shadow is adjacent to the button\n  --light-color: var(--white);\n  --dark-color: var(--gray-800);\n\n  @if $light-button {\n    --focus-ring: 0 0 0 3px var(--dark-color), 0 0 0 6px var(--light-color);\n  } @else {\n    --focus-ring: 0 0 0 3px var(--light-color), 0 0 0 6px var(--dark-color);\n  }\n}\n\n// RTL stuff\n\n@mixin left($left) {\n  body.ltr & {\n    left: $left;\n  }\n  body.rtl & {\n    right: $left;\n  }\n}\n\n@mixin right($right) {\n  body.ltr & {\n    right: $right;\n  }\n  body.rtl & {\n    left: $right;\n  }\n}\n\n@mixin alignleft {\n  body.ltr & {\n    text-align: left;\n  }\n  body.rtl & {\n    text-align: right;\n  }\n}\n\n@mixin alignright {\n  body.ltr & {\n    text-align: right;\n  }\n  body.rtl & {\n    text-align: left;\n  }\n}\n\n@mixin border-left($params...) {\n  body.ltr & {\n    border-left: $params;\n  }\n  body.rtl & {\n    border-right: $params;\n  }\n}\n\n@mixin border-right($params...) {\n  body.ltr & {\n    border-right: $params;\n  }\n  body.rtl & {\n    border-left: $params;\n  }\n}\n\n@mixin border-left-width($param) {\n  body.ltr & {\n    border-left-width: $param;\n  }\n  body.rtl & {\n    border-right-width: $param;\n  }\n}\n\n@mixin border-right-width($param) {\n  body.ltr & {\n    border-right-width: $param;\n  }\n  body.rtl & {\n    border-left-width: $param;\n  }\n}\n\n@mixin border-radius($tl, $tr, $br, $bl) {\n  body.ltr & {\n    border-radius: $tl $tr $br $bl;\n  }\n  body.rtl & {\n    border-radius: $tr $tl $bl $br;\n  }\n}\n\n@mixin border-top-left-radius($params...) {\n  body.ltr & {\n    border-top-left-radius: $params;\n  }\n  body.rtl & {\n    border-top-right-radius: $params;\n  }\n}\n\n@mixin border-top-right-radius($params...) {\n  body.ltr & {\n    border-top-right-radius: $params;\n  }\n  body.rtl & {\n    border-top-left-radius: $params;\n  }\n}\n\n@mixin border-bottom-left-radius($params...) {\n  body.ltr & {\n    border-bottom-left-radius: $params;\n  }\n  body.rtl & {\n    border-bottom-right-radius: $params;\n  }\n}\n\n@mixin border-bottom-right-radius($params...) {\n  body.ltr & {\n    border-bottom-right-radius: $params;\n  }\n  body.rtl & {\n    border-bottom-left-radius: $params;\n  }\n}\n\n@mixin floatleft {\n  body.ltr & {\n    float: left;\n  }\n  body.rtl & {\n    float: right;\n  }\n}\n\n@mixin floatright {\n  body.ltr & {\n    float: right;\n  }\n  body.rtl & {\n    float: left;\n  }\n}\n\n@mixin margin($t, $r, $b, $l, $important: '') {\n  body.ltr & {\n    margin: $t $r $b $l unquote($important);\n  }\n  body.rtl & {\n    margin: $t $l $b $r unquote($important);\n  }\n}\n\n@mixin margin-left($margin...) {\n  body.ltr & {\n    margin-left: $margin;\n  }\n  body.rtl & {\n    margin-right: $margin;\n  }\n}\n\n@mixin margin-right($margin...) {\n  body.ltr & {\n    margin-right: $margin;\n  }\n  body.rtl & {\n    margin-left: $margin;\n  }\n}\n\n@mixin padding($t, $r, $b, $l, $important: '') {\n  body.ltr & {\n    padding: $t $r $b $l unquote($important);\n  }\n  body.rtl & {\n    padding: $t $l $b $r unquote($important);\n  }\n}\n\n@mixin padding-left($padding...) {\n  body.ltr & {\n    padding-left: $padding;\n  }\n  body.rtl & {\n    padding-right: $padding;\n  }\n}\n\n@mixin padding-right($padding...) {\n  body.ltr & {\n    padding-right: $padding;\n  }\n  body.rtl & {\n    padding-left: $padding;\n  }\n}\n\n// Misc\n\n@mixin dark-inputs {\n  @include placeholder-styles($grey400);\n\n  .btn,\n  .select:not(.selectize) select {\n    background-color: $grey200;\n\n    &:focus,\n    &:hover {\n      background-color: darken($grey200, 5%);\n    }\n\n    &:active,\n    &.active {\n      background-color: darken($grey200, 10%);\n    }\n  }\n\n  .text {\n    background-color: $grey200;\n\n    &:focus {\n      background-color: darken($grey200, 5%);\n    }\n  }\n}\n\n@mixin header-btn {\n  width: 30px;\n  min-height: 30px;\n  padding-left: 0;\n  padding-right: 0;\n\n  &:not(:hover):not(:active):not(.active) {\n    background-color: transparent;\n  }\n  &:not(:active):not(.active):hover {\n    background-color: transparentize($grey300, 0.85);\n  }\n}\n\n@mixin h6-styles {\n  margin: 14px 0 3px;\n  font-size: 11px;\n  line-height: 1.2;\n  color: $lightTextColor;\n  text-transform: uppercase;\n}\n\n@mixin token-styles {\n  display: inline-block;\n  border-radius: $smallBorderRadius;\n  padding: 3px 7px;\n  font-size: 12px;\n  line-height: 14px;\n  color: $textColor;\n  background-color: $grey100;\n}\n\n@mixin active-token-styles {\n  background-color: $grey200;\n}\n\n@mixin menu-styles {\n  z-index: 100;\n  border-radius: $menuBorderRadius;\n  padding: 0 14px;\n  overflow: auto;\n  background: $white;\n  user-select: none;\n  box-shadow: 0 0 0 1px transparentize($grey900, 0.9),\n    0 5px 20px transparentize($grey900, 0.75);\n}\n\n@mixin menu-option-styles {\n  margin: 0 -14px;\n  padding: 10px 14px;\n  color: $menuOptionColor;\n  text-decoration: none;\n  white-space: nowrap;\n}\n\n@mixin menu-option-active-styles {\n  color: $menuOptionActiveColor;\n  background-color: $menuOptionActiveBackgroundColor;\n}\n\n@mixin disclosure-link-hover-styles {\n  color: $menuOptionColor;\n  background-color: $grey050;\n}\n\n@mixin input-styles {\n  border-radius: $smallBorderRadius;\n  border: 1px solid transparentize($inputColor, 0.75);\n  background-color: hsl(212, 50%, 99%);\n  background-clip: padding-box;\n}\n\n@mixin input-focused-styles {\n  box-shadow: var(--focus-ring);\n}\n\n@mixin placeholder-styles($color) {\n  input::-webkit-input-placeholder,\n  textarea::-webkit-input-placeholder {\n    color: $color;\n  }\n\n  input:-ms-input-placeholder,\n  textarea:-ms-input-placeholder {\n    color: $color;\n  }\n\n  input::-ms-input-placeholder,\n  textarea::-ms-input-placeholder {\n    color: $color;\n  }\n\n  input:-moz-placeholder,\n  textarea:-moz-placeholder {\n    color: $color;\n  }\n\n  input::-moz-placeholder,\n  textarea::-moz-placeholder {\n    color: $color;\n  }\n\n  input::placeholder,\n  textarea::placeholder {\n    color: $color;\n  }\n}\n\n@mixin select-styles {\n  position: relative;\n  border-radius: $largeBorderRadius;\n  white-space: nowrap;\n}\n\n@mixin select-container-styles {\n  max-width: 100%;\n  position: relative;\n  :not(.flex) > & {\n    display: inline-block;\n  }\n}\n\n@mixin select-arrow-styles {\n  @include angle;\n  position: absolute;\n  z-index: 1;\n  top: calc(50% - 5px);\n  @include right(9px);\n  user-select: none;\n  pointer-events: none;\n}\n\n@mixin select-input-styles {\n  display: block;\n  position: relative;\n  max-width: 100%;\n  border: none;\n  @include padding(7px, 22px, 7px, 10px);\n  font-size: 14px;\n  line-height: 20px;\n  color: $textColor;\n  background-color: hsl(212, 25%, 90%);\n  appearance: none;\n  // from https://stackoverflow.com/a/15933790/1688568\n  &::-ms-expand {\n    display: none;\n  }\n}\n\n@mixin select-input-fullwidth-styles {\n  min-width: 100%;\n}\n\n@mixin select-input-focused-styles {\n  outline-color: transparent;\n  background-color: hsl(212, 25%, 85%);\n  box-shadow: var(--focus-ring);\n}\n\n@mixin touch-target {\n  height: var(--touch-target-size);\n  width: var(--touch-target-size);\n}\n\n@mixin visually-hidden {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: 0;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  border: 0;\n}\n\n@mixin readable {\n  font-size: 16px;\n  line-height: 22px;\n\n  h1,\n  .h1,\n  h2,\n  .h2,\n  h3,\n  .h3,\n  h4,\n  .h4,\n  h5,\n  .h5,\n  h6,\n  .h6 {\n    margin: 24px 0 16px;\n    font-weight: 600;\n  }\n\n  h1,\n  .h1 {\n    font-size: 32px;\n    line-height: 40px;\n    color: #000;\n  }\n\n  h2,\n  .h2 {\n    font-size: 24px;\n    line-height: 30px;\n  }\n\n  h3,\n  .h3 {\n    font-size: 20px;\n    line-height: 24px;\n  }\n\n  h4,\n  .h4 {\n    font-size: 16px;\n    line-height: 20px;\n  }\n\n  h5,\n  .h5 {\n    font-size: 14px;\n    line-height: 18px;\n  }\n\n  h6,\n  .h6 {\n    font-size: 13.6px;\n    line-height: 17px;\n    color: $mediumTextColor;\n  }\n\n  ul,\n  ol {\n    margin: 1em 0;\n    @include padding-left(2em);\n  }\n\n  ul li {\n    list-style-type: disc;\n  }\n\n  li + li {\n    margin-top: 0.25em;\n  }\n\n  .tip-dismiss-btn {\n    position: absolute;\n    top: 12px;\n    @include right(12px);\n\n    & + p {\n      margin-top: 0;\n    }\n  }\n\n  blockquote {\n    margin: 16px 0;\n\n    &:not(.note) {\n      padding: 0 16px;\n      color: $mediumTextColor;\n      @include border-left(4px solid $hairlineColor);\n    }\n\n    &.note {\n      position: relative;\n      border-radius: 4px;\n      padding: 1em;\n      @include padding-left(50px);\n      border: 1px solid;\n\n      &.dismissible {\n        @include padding-right(36px);\n      }\n\n      &:not(.tip):not(.warning) {\n        border-color: $errorColor;\n        color: #bf503f;\n\n        &:before {\n          content: 'alert';\n          color: $errorColor;\n        }\n      }\n\n      &.tip {\n        border-color: $linkColor;\n        color: $linkColor;\n\n        &:before {\n          content: 'lightbulb';\n          color: $linkColor;\n        }\n      }\n\n      &.warning {\n        border-color: $warningColor;\n        color: #cf783a;\n\n        &:before {\n          content: 'alert';\n          color: $warningColor;\n        }\n      }\n\n      &:before {\n        @include icon;\n        opacity: 1;\n        position: absolute;\n        top: 15px;\n        @include left(16px);\n        font-size: 24px;\n        width: 24px;\n      }\n\n      a[href] {\n        color: currentColor;\n        text-decoration: underline;\n      }\n    }\n  }\n\n  .go:after {\n    font-size: 14px;\n  }\n}\n\n@mixin checkered-bg($size) {\n  // h/t https://gist.github.com/dfrankland/f6fed3e3ccc42e3de482b324126f9542\n  $halfSize: $size * 0.5;\n  background-image: linear-gradient(\n      45deg,\n      #{transparentize($grey300, 0.75)} 25%,\n      transparent 25%\n    ),\n    linear-gradient(\n      135deg,\n      #{transparentize($grey300, 0.75)} 25%,\n      transparent 25%\n    ),\n    linear-gradient(\n      45deg,\n      transparent 75%,\n      #{transparentize($grey300, 0.75)} 75%\n    ),\n    linear-gradient(\n      135deg,\n      transparent 75%,\n      #{transparentize($grey300, 0.75)} 75%\n    );\n  background-size: $size $size;\n  background-position: 0 0, $halfSize 0, $halfSize -#{$halfSize}, 0 $halfSize;\n}\n"], "names": [], "sourceRoot": ""}