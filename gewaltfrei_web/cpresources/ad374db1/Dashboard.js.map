{"version": 3, "file": "Dashboard.js", "mappings": "qEAIe,SAASA,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,CAAC,EACRC,EAAI,EAAGA,EAAIH,EAAKI,OAAQD,IAAK,CACpC,IAAIE,EAAOL,EAAKG,GACZG,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIP,EAAW,IAAMI,EACrBK,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBH,EAAUI,GAGbJ,EAAUI,GAAIK,MAAMC,KAAKL,GAFzBN,EAAOW,KAAKV,EAAUI,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,IAIlD,CACA,OAAON,CACT,C,gCClBA,IAAIY,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,CAMhB,EAEEC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,WAAa,EACpBC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiBhC,EAAUC,EAAMgC,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,CAAC,EAEvB,IAAIhC,EAASH,EAAaC,EAAUC,GAGpC,OAFAkC,EAAejC,GAER,SAAiBkC,GAEtB,IADA,IAAIC,EAAY,GACPjC,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,IACdkC,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,EACjB,CAOA,IANIF,EAEFD,EADAjC,EAASH,EAAaC,EAAUoC,IAGhClC,EAAS,GAEFE,EAAI,EAAGA,EAAIiC,EAAUhC,OAAQD,IAAK,CACzC,IAAIkC,EACJ,GAAsB,KADlBA,EAAWD,EAAUjC,IACZmC,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,GAC9B,CACF,CACF,CACF,CAEA,SAAS4B,EAAgBjC,GACvB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,GACdkC,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,OAEvC,KAAO,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,EACxD,CACF,CACF,CAEA,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,CACT,CAEA,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIxC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,EAExC,CAEA,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,EACpE,MAEEV,EAAeD,IACfM,EAASQ,EAAWD,KAAK,KAAMZ,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,EACtC,EAKF,OAFAK,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAOhD,MAAQsC,EAAItC,KACnBgD,EAAO/C,QAAUqC,EAAIrC,OACrB+C,EAAO9C,YAAcoC,EAAIpC,UAC3B,OAEFqC,EAAOD,EAAMU,EACf,MACER,GAEJ,CACF,CAEA,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,KACxC,GAGF,SAASV,EAAqBX,EAAciB,EAAOX,EAAQF,GACzD,IAAItC,EAAMwC,EAAS,GAAKF,EAAItC,IAE5B,GAAIkC,EAAasB,WACftB,EAAasB,WAAWC,QAAUP,EAAYC,EAAOnD,OAChD,CACL,IAAI0D,EAAUpD,SAASqD,eAAe3D,GAClC4D,EAAa1B,EAAa0B,WAC1BA,EAAWT,IAAQjB,EAAaS,YAAYiB,EAAWT,IACvDS,EAAWhE,OACbsC,EAAa2B,aAAaH,EAASE,EAAWT,IAE9CjB,EAAaG,YAAYqB,EAE7B,CACF,CAEA,SAASX,EAAYb,EAAcI,GACjC,IAAItC,EAAMsC,EAAItC,IACVC,EAAQqC,EAAIrC,MACZC,EAAYoC,EAAIpC,UAiBpB,GAfID,GACFiC,EAAa4B,aAAa,QAAS7D,GAEjCe,EAAQ+C,OACV7B,EAAa4B,aAAa7C,EAAUqB,EAAIxC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU8D,QAAQ,GAAK,MAEnDhE,GAAO,uDAAyDiE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUnE,MAAgB,OAG9HgC,EAAasB,WACftB,EAAasB,WAAWC,QAAUzD,MAC7B,CACL,KAAOkC,EAAaoC,YAClBpC,EAAaS,YAAYT,EAAaoC,YAExCpC,EAAaG,YAAY/B,SAASqD,eAAe3D,GACnD,CACF,C,uCC1NA,IAAIuE,EAAU,EAAQ,KACnBA,EAAQC,aAAYD,EAAUA,EAAQE,SACnB,iBAAZF,IAAsBA,EAAU,CAAC,CAACG,EAAO5E,GAAIyE,EAAS,MAC7DA,EAAQI,SAAQD,EAAOE,QAAUL,EAAQI,SAG/BE,EADH,SACO,WAAYN,GAAS,EAAM,CAAC,E,GCRzCO,yBAA2B,CAAC,EAGhC,SAASC,oBAAoBC,GAE5B,IAAIC,EAAeH,yBAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaL,QAGrB,IAAIF,EAASI,yBAAyBE,GAAY,CACjDlF,GAAIkF,EAEJJ,QAAS,CAAC,GAOX,OAHAO,oBAAoBH,GAAUN,EAAQA,EAAOE,QAASG,qBAG/CL,EAAOE,OACf,CCrBAG,oBAAoBK,EAAI,SAASV,GAChC,IAAIW,EAASX,GAAUA,EAAOF,WAC7B,WAAa,OAAOE,EAAgB,OAAG,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAK,oBAAoBO,EAAED,EAAQ,CAAEE,EAAGF,IAC5BA,CACR,ECNAN,oBAAoBO,EAAI,SAASV,EAASY,GACzC,IAAI,IAAIC,KAAOD,EACXT,oBAAoBW,EAAEF,EAAYC,KAASV,oBAAoBW,EAAEd,EAASa,IAC5EE,OAAOC,eAAehB,EAASa,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAG3E,ECPAV,oBAAoBW,EAAI,SAASpD,EAAKyD,GAAQ,OAAOJ,OAAOK,UAAUC,eAAeC,KAAK5D,EAAKyD,EAAO,E,wPCEtG,SAAWI,GAMTC,MAAMC,UAAYC,QAAQC,KAAKC,OAAO,CACpCC,MAAO,KACPC,kBAAmB,KACnBC,cAAe,KAEfC,YAAa,KACbC,KAAM,KACNC,QAAS,KACTC,cAAe,KACfC,iBAAkB,KAClBC,oBAAqB,KAErBC,KAAM,SAAUN,GAAa,IAAAO,EAAA,KAC3BC,KAAKR,YAAcA,EACnBQ,KAAKN,QAAU,CAAC,EAEhBM,KAAKV,kBAAoBP,EAAE,qBAC3BiB,KAAKT,cAAgBR,EAAE,qBAEvBiB,KAAKC,YAAYD,KAAKV,kBAAmB,QAAS,qBAElDJ,QAAQgB,KAAKC,OAAM,WACjBJ,EAAKV,MAAQN,EAAE,mBACfgB,EAAKN,KAAOM,EAAKV,MAAMe,KAAK,QAE5BL,EAAKE,YAAY,qBAAsB,SAAS,SAACI,GAC/CA,EAAMC,iBACNP,EAAKQ,4BAA4BF,EACnC,IAEAN,EAAKE,YAAY,qBAAsB,WAAW,SAACI,GAE/CA,EAAMG,UAAYtB,QAAQuB,WAC1BJ,EAAMG,UAAYtB,QAAQwB,aAE1BL,EAAMC,iBACNP,EAAKQ,4BAA4BF,GAErC,GACF,GACF,EAEAM,YAAa,SAAU3F,EAAM4F,EAAUC,GACrC,OAAID,OAC8C,IAArCZ,KAAKR,YAAYxE,GAAM4F,GACzBC,EAEAb,KAAKR,YAAYxE,GAAM4F,GAGzBZ,KAAKR,YAAYxE,EAE5B,EAEAuF,4BAA6B,SAAUO,GACrCd,KAAKT,cAAca,KAAK,WAAWW,OACnC,IAAMC,EAAUjC,EAAE+B,EAAEG,QACpBjB,KAAKkB,aAAaF,EAAQZ,KAAK,QAASY,EAAQZ,KAAK,QACvD,EAEAc,aAAc,SAAdA,aAAwBlG,KAAMmG,KAAMC,cAClC,IAAMC,uBACoB,IAAjBD,aAA4B,YAAAE,OACnBC,KAAKC,MAAsB,IAAhBD,KAAKE,UAAsB,sBAAAH,OACzCF,aAAa1I,GAAE,aACxBgJ,kBACoB,IAAjBN,aACHpB,KAAKW,YAAY3F,KAAM,eAAgB,IAAI2G,QACzC,iBACAN,mBAEF,KACAO,gBACoB,IAAjBR,aACHpB,KAAKW,YAAY3F,KAAM,aAAc,IAAI2G,QACvC,iBACAN,mBAEF,KACAQ,UAAY9C,EAChB,8DAEI+C,WAAa/C,EAAE,SAAU,CAC7BgD,MAAO,kCACP,YAAa/G,OAEZgH,SAAShH,KAAKd,eACd+H,OACClD,EAAE,SAAU,CAACgD,MAAO,UAAUE,OAC5BlD,EAAE,SAAU,CAACgD,MAAO,SACjBE,OAAOlD,EAAE,SAAU,CAACgD,MAAO,0BAC3BE,OACClD,EAAE,SAAU,CAACgD,MAAO,mBACjBE,OAAO,SACPA,OAAO,UAEXA,OAAOlD,EAAE,SAAU,CAACgD,MAAO,UAC3BE,OAAOlD,EAAE,SAAU,CAACgD,MAAO,4BAGjCE,OACClD,EAAE,SAAU,CAACgD,MAAO,SAASE,OAC3BlD,EAAE,UAAW,CAACgD,MAAO,SAClBE,OACClD,EAAE,WAAY,CACZ/D,KAAM,SACNmG,KAAM,OACNe,MAAOlH,QAGViH,OACClD,EAAE,WAAY,CACZ/D,KAAM,SACNmG,KAAM,oBACNe,MAAOb,qBAGVY,OACClD,EAAE,QAAS,CACTgD,MAAO,QACPI,KAAMnD,MAAMoD,EAAE,MAAO,kBAAmB,CACtCpH,KAAMmG,UAIXc,OAAOlD,EAAE,SAAU,CAACgD,MAAO,cAC3BE,OAAO,SACPA,OACClD,EAAE,SAAU,CAACgD,MAAO,uBACjBE,OACCjD,MAAMqD,GAAGC,mBAAmB,CAC1BC,MAAOvD,MAAMoD,EAAE,MAAO,QACtBI,SAAS,KAGZP,OACClD,EAAE,YAAa,CACb/D,KAAM,SACN+G,MAAO,iBACPI,KAAMnD,MAAMoD,EAAE,MAAO,gBAMlCK,SAASZ,WAERH,cACFI,WAAWE,SAAS,WACpBF,WAAWY,SAAS,UAAUV,SAAS,YAEvCF,WAAWE,SAAS,WACpBF,WAAWY,SAAS,SAASV,SAAS,WAGxC,IAAMW,OAAS,IAAI3D,MAAM4D,OACvBd,WACAJ,aACIA,aAAaC,QAAQ,iBAAkBN,mBACvC,KACJO,WACI,WACEiB,KAAKjB,WACP,EACA7C,EAAEpF,MAiBR,GAXIqG,KAAKP,KAAKqD,OAAOtK,OACnBqJ,UAAUkB,YAAY/C,KAAKP,KAAKqD,OAAOE,QAEvCnB,UAAUoB,UAAUjD,KAAKP,KAAKqC,YAGhC9B,KAAKP,KAAKyD,SAASrB,WACnB3C,QAAQiE,yBAAyBtB,WAEjCC,WAAWsB,YAAY,iBAEK,IAAjBhC,aACTU,WAAWsB,YAAY,WACvBT,OAAOxH,OAAOiG,mBACT,IAAKM,aAAc,CACxB,IAAMtB,KAAO,CACXpF,KAAMA,MAGRgE,MAAMqE,MAAMrK,MACV,kBACE,IAAIsK,SAAQ,SAACC,GACXvE,MAAMwE,kBAAkB,OAAQ,0BAA2B,CAACpD,KAAAA,OACzDqD,MAAK,SAACC,GACL5B,WAAWsB,YAAY,WACvBT,OAAOxH,OAAOuI,EAAStD,KACzB,IAAE,OACK,WACLuC,OAAOgB,SACT,IAAE,QACOJ,EACb,GAAE,GAER,CACF,EAEAK,kBAAmB,WAAY,IAAAC,EAAA,KAC7B,GAAK7D,KAAKL,cAyHRK,KAAKL,cAAcmE,WAzHI,CAqBvB,IApBA,IAAIC,EAAW/D,KAAKX,MAAM2E,KAAK,qBAC7BC,EAAQlF,EACN,sHAGA0D,SAASvD,QAAQgF,MACnBC,EAAapF,EACX,wCACGgF,EAASvL,OAAS,UAAY,IAC/B,KACAwG,MAAMoD,EAAE,MAAO,mCACf,QACFK,SAASwB,GACXG,EAASrF,EACP,sBACIgF,EAASvL,OAAqB,GAAZ,WACpB,2BACFiK,SAASwB,GACXI,EAAStF,EAAE,YAAY0D,SAAS2B,GAEzB7L,EAAI,EAAGA,EAAIwL,EAASvL,OAAQD,IAAK,CACxC,IACEoK,EADYoB,EAASO,GAAG/L,GACP6H,KAAK,UAGnBuC,GAAWA,EAAOjK,IAIvBiK,EAAO4B,gBAAgB9B,SAAS4B,EAClC,CAEArE,KAAKL,cAAgB,IAAIT,QAAQsF,IAAIxE,KAAKV,kBAAmB2E,EAAO,CAClEQ,SAAU,uBACVC,OAAQ,WACNb,EAAKvE,kBACF0C,SAAS,UACT2C,KAAK,gBAAiB,OAC3B,EACAC,OAAQ,WACNf,EAAKvE,kBACF8D,YAAY,UACZuB,KAAK,gBAAiB,QAC3B,IAGF3E,KAAKJ,iBAAmB,IAAIZ,MAAM6F,WAAW,CAC3CC,cAAeV,EACfW,kBAAmBZ,EACnBa,UAAU,EACVC,cAAe,iCACfC,aAAc,+BACdC,qBAAsB,KACtBC,qBAAsB,KACtBC,gBAAiB,aACjBC,eAAgB,SAACC,GAGf,IAFA,IAAIC,EAAa,KAERjN,EAAI,EAAGA,EAAIgN,EAAI/M,OAAQD,IAAK,CACnC,IAAIoK,EAASkB,EAAKnE,QAAQ6F,EAAIhN,IAEzBiN,EAGH7C,EAAOd,UAAUkB,YAAYyC,EAAW3D,WAFxCc,EAAOd,UAAUoB,UAAUY,EAAKxE,OAKlCmG,EAAa7C,CACf,CAEAkB,EAAKpE,KAAKgG,gBACZ,EACAC,aAAc,SAAChN,GACb,IAAMiK,EAASkB,EAAKnE,QAAQhH,GAC5BiK,EAAOgB,UAEP,IAAMgC,EAAW3G,MAAMqD,GAAGuD,aAAa,CACrCrD,MAAOvD,MAAMoD,EAAE,MAAO,QACtBI,SAAS,IAGLqD,EAAe7G,MAAM8G,GAAGC,eAC5B/G,MAAMoD,EAAE,MAAO,oBAAqB,CAClCjB,KAAMwB,EAAOqD,aAEf,CACEC,QAASN,IAIbA,EAASO,GAAG,SAAS,WACnB,IAAIP,EAASQ,SAAS,WAAtB,CAIAR,EAAS3D,SAAS,WAElB,IAAM5B,EAAO,CACXpF,KAAM2H,EAAO3H,KACboL,SAAUzD,EAAO0D,gBAGnBrH,MAAMwE,kBAAkB,OAAQ,0BAA2B,CAACpD,KAAAA,IACzDqD,MAAK,SAACC,GACLG,EAAK3C,aACHyB,EAAO3H,KACP2H,EAAOqD,WACPtC,EAAStD,MAGXuF,EAASW,IAAI,SACbT,EAAaU,OACf,IAAE,SACO,WACPZ,EAASvC,YAAY,UACvB,GAtBF,CAuBF,GACF,GAEJ,CAGF,IAMFpE,MAAM4D,OAAS1D,QAAQC,KAAKC,OAAO,CACjC0C,WAAY,KACZD,UAAW,KAEX2E,OAAQ,KACRC,aAAc,KACdC,OAAQ,KACRC,UAAW,KACXC,SAAU,KACVC,eAAgB,KAEhBC,MAAO,KACPC,cAAe,KACfC,mBAAoB,KACpBC,gBAAiB,KACjBC,SAAU,KACVC,mBAAoB,KAEpBzO,GAAI,KACJsC,KAAM,KACNoM,MAAO,KACPC,SAAU,KACVhB,eAAgB,KAEhBiB,UAAW,KACX5F,aAAc,KACd6F,eAAgB,KAChBC,iBAAiB,EAEjBC,cAAe,KAEf3H,KAAM,SAAU4H,EAAWhG,EAAc6F,EAAgBlB,GACvDrG,KAAK8B,WAAa/C,EAAE2I,GACpB1H,KAAKqG,eAAiBA,EAEtBrG,KAAKiH,gBAAkBjH,KAAK8B,WAAWkC,KAAK,0BAC5ChE,KAAK6B,UAAY7B,KAAK8B,WAAW6F,SAGjC3H,KAAK8B,WAAW1B,KAAK,SAAUJ,MAG/BA,KAAKtH,GAAKsH,KAAK8B,WAAW1B,KAAK,MAC/BJ,KAAKhF,KAAOgF,KAAK8B,WAAW1B,KAAK,QACjCJ,KAAKoH,MAAQpH,KAAK8B,WAAW1B,KAAK,SAE9BJ,KAAKtH,KAEPkP,OAAOC,UAAUnI,QAAQM,KAAKtH,IAAMsH,MAGtCA,KAAKwG,OAASxG,KAAK8B,WAAWY,SAAS,UACvC1C,KAAKyG,aAAezG,KAAKwG,OAAOxC,KAAK,4BACrChE,KAAK4G,SAAW5G,KAAKwG,OAAOxC,KAAK,6BACjChE,KAAK0G,OAAS1G,KAAK4G,SAAS5C,KAAK,QACjChE,KAAK2G,UAAY3G,KAAK4G,SAAS5C,KAAK,QACpChE,KAAK6G,eAAiB7G,KAAKwG,OAAOxC,KAAK,mBAEvChE,KAAK8H,gBAAgBpG,EAAc6F,GAE9BvH,KAAK8B,WAAWqE,SAAS,YAG5BnG,KAAK+H,aACL/H,KAAKgI,kBACLhI,KAAKiI,cAJLjI,KAAKkI,cAOPlI,KAAKC,YAAYD,KAAKyG,aAAc,QAAS,eAC/C,EAEAsB,WAAY,WACV/H,KAAK8G,MAAQ9G,KAAK8B,WAAWY,SAAS,SACtC1C,KAAK+G,cAAgB/G,KAAK8G,MAAMpE,SAAS,QACzC1C,KAAKgH,mBAAqBhH,KAAK+G,cAAcrE,SAAS,aACtD,IAAIyF,EAAiBnI,KAAK+G,cAAcrE,SAAS,YACjD1C,KAAKkH,SAAWiB,EAAezF,SAAS,uBAExC1C,KAAKC,YACHkI,EAAezF,SAAS,eACxB,QACA,kBAEF1C,KAAKC,YAAYD,KAAK+G,cAAe,SAAU,eACjD,EAEAqB,WAAY,WACV,OAAOpI,KAAK6B,UAAUzB,KAAK,UAC7B,EAEAiI,WAAY,SAAUC,GACpBtI,KAAK6B,UAAUzB,KAAK,UAAWkI,GAAS3D,KAAK,eAAgB2D,GAC7DV,OAAOC,UAAUpI,KAAK8I,aAAY,EACpC,EAEA5H,YAAa,SAAUC,EAAUC,GAC/B,OAAO+G,OAAOC,UAAUlH,YAAYX,KAAKhF,KAAM4F,EAAUC,EAC3D,EAEAiH,gBAAiB,SAAUpG,EAAc6F,GACvCvH,KAAK0B,aAAeA,EACpB1B,KAAKuH,eAAiBA,EAElBvH,KAAK0B,aACP1B,KAAKyG,aAAarD,YAAY,UAE9BpD,KAAKyG,aAAazE,SAAS,SAE/B,EAEAgG,gBAAiB,WAAY,IAAAQ,EAAA,KAC3BxI,KAAKgH,mBAAmByB,KAAKzI,KAAK0B,cAElCxC,QAAQwJ,uBAAsB,WAC5B1J,MAAM2J,eAAeH,EAAKxB,oBAC1BwB,EAAKjB,gBACP,GACF,EAEAqB,aAAc,WAAY,IAAAC,EAAA,KACnB7I,KAAK8G,OACR9G,KAAK+H,aAIP/H,KAAKgI,kBAELhI,KAAK8G,MAAM1D,YAAY,UACvB0F,YAAW,WACTD,EAAK/G,WAAWE,SAAS,WAAW+G,SAClC,CAACC,OAAQH,EAAK/B,MAAMkC,UACpB,CACEC,SAAUJ,EAAKZ,WAAWvM,KAAKmN,IAGrC,GAAG,IACL,EAEAK,aAAc,WAAY,IAAAC,EAAA,KACxBnJ,KAAKwG,OAAOpD,YAAY,UAExB0F,YAAW,WACTK,EAAKrH,WAAWsB,YAAY,WAAW2F,SACrC,CAACC,OAAQG,EAAK3C,OAAOwC,UACrB,CACEC,SAAUE,EAAKjB,YAAYxM,KAAKyN,KAGpCA,EAAKlC,gBAAgBmC,OACvB,GAAG,IACL,EAEAC,aAAc,SAAUvI,GAAG,IAAAwI,EAAA,KACzBxI,EAAER,iBAEEN,KAAKkH,SAASf,SAAS,aAI3BnG,KAAKkH,SAASlF,SAAS,WAEvBhD,MAAMqE,MAAMrK,MACV,kBACE,IAAIsK,SAAQ,SAACC,GACX,IAAMgG,EAASD,EAAKxH,WAAWqE,SAAS,OAClC,0BACA,iCACJ/F,EAAOkJ,EAAKvC,cAAcyC,YAE5BxK,MAAMwE,kBAAkB,OAAQ+F,EAAQ,CAACnJ,KAAAA,IACtCqD,MAAK,SAACC,GACD4F,EAAKnC,qBACPmC,EAAKnC,mBAAmB/L,SACxBkO,EAAKnC,mBAAqB,MAG5BnI,MAAM8G,GAAGC,eAAe/G,MAAMoD,EAAE,MAAO,kBAGlCsB,EAAStD,KAAKqJ,MAGjBH,EAAKnO,OAAOuI,EAAStD,MACrBkJ,EAAKJ,gBAHLI,EAAK3F,SAKT,IAAE,OACK,SAAA+F,GAAgB,IAAdhG,EAAQgG,EAARhG,SACH4F,EAAKnC,qBACPmC,EAAKnC,mBAAmB/L,SACxBkO,EAAKnC,mBAAqB,MAG5BnI,MAAM8G,GAAG6D,aAAa3K,MAAMoD,EAAE,MAAO,0BAEjCsB,EAAStD,KAAKwJ,SAChBN,EAAKnC,mBAAqBnI,MAAMqD,GAC7BwH,gBAAgBnG,EAAStD,KAAKwJ,QAC9B7G,YAAYuG,EAAKtC,oBAExB,IAAE,SACO,WACPsC,EAAKpC,SAAS9D,YAAY,WAC1BG,GACF,GACJ,GAAE,IAER,EAEApI,OAAQ,SAARA,OAAkBuI,UACX1D,KAAK8G,OACR9G,KAAK+H,aAGP/H,KAAKoH,MAAQ1D,SAAS+F,KAAKrC,MAC3BpH,KAAKqH,SAAW3D,SAAS+F,KAAKpC,SAC9BrH,KAAKqG,eAAiB3C,SAAS+F,KAAKrD,SAGhCpG,KAAK8B,WAAWqE,SAAS,QAE3BnG,KAAKtH,GAAKgL,SAAS+F,KAAK/Q,GAExBsH,KAAK8B,WACF6C,KAAK,KAAM,SAAW3E,KAAKtH,IAC3B0K,YAAY,mBAEXpD,KAAK+G,eACP/G,KAAK+G,cAAc+C,QACjB,+CAAiD9J,KAAKtH,GAAK,OAK/DkP,OAAOC,UAAUnI,QAAQM,KAAKtH,IAAMsH,KAEhC4H,OAAOC,UAAUjI,kBACnBgI,OAAOC,UAAUjI,iBAAiBmK,OAAO/J,KAAKuE,kBAG5CqD,OAAOC,UAAUjI,kBACnBgI,OAAOC,UAAUjI,iBAAiByE,OAC/B3B,SAAS,aAAe1C,KAAKtH,GAAK,YAClCgK,SAAS,mBACT+F,KAAKzI,KAAKgK,sBAIZhK,KAAKoH,OAAUpH,KAAKqH,UAGnBrH,KAAKoH,MACPpH,KAAK0G,OAAOvE,KAAKnC,KAAKoH,OAEtBpH,KAAK0G,OAAOtL,SAGV4E,KAAKqH,SACPrH,KAAK2G,UAAUxE,KAAKnC,KAAKqH,UAEzBrH,KAAK2G,UAAUvL,UAXjB4E,KAAK4G,SAASxL,SAehB4E,KAAK6G,eAAe4B,KAAK/E,SAAS+F,KAAKQ,UAGnCvG,SAAS+F,KAAKnB,SAAWtI,KAAKoI,eAChCpI,KAAKqI,WAAW3E,SAAS+F,KAAKnB,SAC9BpJ,QAAQiE,yBAAyBnD,KAAK6B,YAGxC7C,MAAM2J,eAAe3I,KAAK6G,gBAC1B7H,MAAMkL,eAAexG,SAASyG,UAC9BnL,MAAMoL,eAAe1G,SAASuG,UAE9BjK,KAAK8H,gBAAgBpE,SAAS+F,KAAK/H,cAAc,WAC/CmB,KAAKa,SAAS+F,KAAK7H,WACrB,GACF,EAEAyI,eAAgB,WACVrK,KAAKtH,GACPsH,KAAKkJ,eAELlJ,KAAK2D,SAET,EAEAuE,YAAa,WACXlI,KAAKwH,iBAAkB,EACvBxH,KAAKsK,eAAetK,KAAK8G,MAAO,UAChC9G,KAAKC,YAAYD,KAAKwG,OAAQ,SAAU,yBACpCxG,KAAK8G,OACP9G,KAAK8G,MAAM9E,SAAS,SAExB,EAEAiG,WAAY,WAAY,IAAAsC,EAAA,KACtBvK,KAAKwH,iBAAkB,EACvBxH,KAAKsK,eAAetK,KAAKwG,OAAQ,UACjCxG,KAAKC,YAAYD,KAAK8G,MAAO,SAAU,yBACvC9G,KAAKwG,OAAOxE,SAAS,UAGrB8G,YAAW,WACTyB,EAAKxD,cAAc/C,KAAK,oBAAoBoF,OAC9C,GAAG,EACL,EAEAoB,sBAAuB,WACrBxK,KAAK8B,WAAWkH,QACbhJ,KAAKwH,gBAAkBxH,KAAK8G,MAAQ9G,KAAKwG,QAAQwC,SAEtD,EAEAyB,iBAAkB,WAChB,MAAO,gBAAPnJ,OAAuBtB,KAAKtH,GAC9B,EAEA6L,cAAe,WAAY,IAAAmG,EAAA,KACrBC,EAAO5L,EACT,gBACEiB,KAAKtH,GACL,iBACCsH,KAAKoH,MACFpI,MAAM4L,WAAW5K,KAAKoH,OACtBpH,KAAKW,YAAY,SALvB,4CAQEX,KAAKW,YAAY,WARnB,gBAWEX,KAAKyK,mBACL,KACAzK,KAAKgK,qBAbP,yIAiBEhL,MAAMoD,EAAE,MAAO,WAjBjB,sGAoBEpD,MAAMoD,EAAE,MAAO,UACf,+BACApD,MAAMoD,EAAE,MAAO,UACf,uBACApC,KAAKyK,mBAxBP,oBA6EF,OA/CAzK,KAAKyH,cAAgB,IAAIzI,MAAM6L,YAAY7K,KAAKoI,aAAc,CAC5D0C,IAAK,EACLC,IAAK,WACH,OAAOnD,OAAOC,UAAUpI,KAAK6H,SAC/B,EACA0D,KAAM,EACNzI,MAAOvD,MAAMoD,EAAE,MAAO,qBACtB6I,YAAajL,KAAKyK,mBAClBS,WAAY,SAAC5C,GACX,OAAOtJ,MAAMoD,EACX,MACA,yDACA,CACE+I,IAAK7C,GAGX,EACA8C,SAAU,SAAC9C,GAEToC,EAAKrC,WAAWC,GAChBV,OAAOC,UAAUpI,KAAK8I,aAAY,GAGlC,IAAInI,EAAO,CACT1H,GAAIgS,EAAKhS,GACT4P,QAASA,GAGXtJ,MAAMwE,kBAAkB,OAAQ,kCAAmC,CACjEpD,KAAAA,IAECqD,MAAK,SAACC,GACL1E,MAAM8G,GAAGC,eAAe/G,MAAMoD,EAAE,MAAO,iBACzC,IAAE,OACK,SAAAiJ,GAAUA,EAAR3H,SACP1E,MAAM8G,GAAG6D,aAAa3K,MAAMoD,EAAE,MAAO,yBACvC,GACJ,IAGFpC,KAAKyH,cAAc3F,WAAWW,SAC5BkI,EAAK3G,KAAK,6CAEZ4D,OAAOC,UAAUpI,KAAKyG,GAAG,eAAe,WACtCwE,EAAKjD,cAAc6D,SACrB,IAEOX,CACT,EAEA3E,SAAU,WACR,OAAOhG,KAAKoH,OAASpH,KAAKW,YAAY,OACxC,EAEAqJ,mBAAoB,WAClB,IAAIuB,EAAWvL,KAAKW,YAAY,QAEhC,OAAKX,KAAKoH,MAKRpI,MAAM4L,WAAW5K,KAAKoH,QACrBpH,KAAKoH,QAAUmE,EACZ,yBAA2BA,EAAW,WACtC,IAPGA,CASX,EAEA5H,QAAS,WAAY,IAAA6H,EAAA,YACZ5D,OAAOC,UAAUnI,QAAQM,KAAKtH,IACrCsH,KAAK8B,WAAWE,SAAS,YACzBhC,KAAKyL,OAEL3C,YAAW,WACTlB,OAAOC,UAAUpI,KAAKiM,YAAYF,EAAK3J,WACvC2J,EAAK3J,UAAUzG,QACjB,GAAG,IACL,GAEH,EArwBD,CAqwBGuQ,O", "sources": ["webpack:///../../../../../node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///../../../../../node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:///./dashboard.scss?3ad2", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///./Dashboard.js"], "sourcesContent": ["/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ruleSet[1].rules[3].use[1]!../../../../../node_modules/css-loader/dist/cjs.js!../../../../../node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[3].use[3]!../../../../../node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[3].use[4]!./dashboard.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"32dd7842\", content, true, {});", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "import './dashboard.scss';\n\n(function ($) {\n  /** global: Craft */\n  /** global: Garnish */\n  /**\n   * Dashboard class\n   */\n  Craft.Dashboard = Garnish.Base.extend({\n    $grid: null,\n    $widgetManagerBtn: null,\n    $newWidgetBtn: null,\n\n    widgetTypes: null,\n    grid: null,\n    widgets: null,\n    widgetManager: null,\n    widgetAdminTable: null,\n    widgetSettingsModal: null,\n\n    init: function (widgetTypes) {\n      this.widgetTypes = widgetTypes;\n      this.widgets = {};\n\n      this.$widgetManagerBtn = $('#widgetManagerBtn');\n      this.$newWidgetBtn = $('#newwidgetmenubtn');\n\n      this.addListener(this.$widgetManagerBtn, 'click', 'showWidgetManager');\n\n      Garnish.$doc.ready(() => {\n        this.$grid = $('#dashboard-grid');\n        this.grid = this.$grid.data('grid');\n\n        this.addListener('#new-widget-menu a', 'click', (event) => {\n          event.preventDefault();\n          this.handleNewWidgetOptionSelect(event);\n        });\n\n        this.addListener('#new-widget-menu a', 'keydown', (event) => {\n          if (\n            event.keyCode === Garnish.SPACE_KEY ||\n            event.keyCode === Garnish.RETURN_KEY\n          ) {\n            event.preventDefault();\n            this.handleNewWidgetOptionSelect(event);\n          }\n        });\n      });\n    },\n\n    getTypeInfo: function (type, property, defaultValue) {\n      if (property) {\n        if (typeof this.widgetTypes[type][property] === 'undefined') {\n          return defaultValue;\n        } else {\n          return this.widgetTypes[type][property];\n        }\n      } else {\n        return this.widgetTypes[type];\n      }\n    },\n\n    handleNewWidgetOptionSelect: function (e) {\n      this.$newWidgetBtn.data('trigger').hide();\n      const $option = $(e.target);\n      this.createWidget($option.data('type'), $option.data('name'));\n    },\n\n    createWidget: function (type, name, responseData) {\n      const settingsNamespace =\n        typeof responseData === 'undefined'\n          ? `newwidget${Math.floor(Math.random() * 1000000000)}-settings`\n          : `widget${responseData.id}-settings`;\n      const settingsHtml =\n        typeof responseData === 'undefined'\n          ? this.getTypeInfo(type, 'settingsHtml', '').replace(\n              /__NAMESPACE__/g,\n              settingsNamespace\n            )\n          : null;\n      const settingsJs =\n        typeof responseData === 'undefined'\n          ? this.getTypeInfo(type, 'settingsJs', '').replace(\n              /__NAMESPACE__/g,\n              settingsNamespace\n            )\n          : null;\n      const $gridItem = $(\n        '<div class=\"item\" data-colspan=\"1\" style=\"display: block\">'\n      );\n      const $container = $('<div/>', {\n        class: 'widget new loading-new scaleout',\n        'data-type': type,\n      })\n        .addClass(type.toLowerCase())\n        .append(\n          $('<div/>', {class: 'front'}).append(\n            $('<div/>', {class: 'pane'})\n              .append($('<div/>', {class: 'spinner body-loading'}))\n              .append(\n                $('<div/>', {class: 'widget-heading'})\n                  .append('<h2/>')\n                  .append('<h5/>')\n              )\n              .append($('<div/>', {class: 'body'}))\n              .append($('<div/>', {class: 'settings icon hidden'}))\n          )\n        )\n        .append(\n          $('<div/>', {class: 'back'}).append(\n            $('<form/>', {class: 'pane'})\n              .append(\n                $('<input/>', {\n                  type: 'hidden',\n                  name: 'type',\n                  value: type,\n                })\n              )\n              .append(\n                $('<input/>', {\n                  type: 'hidden',\n                  name: 'settingsNamespace',\n                  value: settingsNamespace,\n                })\n              )\n              .append(\n                $('<h2/>', {\n                  class: 'first',\n                  text: Craft.t('app', '{type} Settings', {\n                    type: name,\n                  }),\n                })\n              )\n              .append($('<div/>', {class: 'settings'}))\n              .append('<hr/>')\n              .append(\n                $('<div/>', {class: 'buttons clearafter'})\n                  .append(\n                    Craft.ui.createSubmitButton({\n                      label: Craft.t('app', 'Save'),\n                      spinner: true,\n                    })\n                  )\n                  .append(\n                    $('<button/>', {\n                      type: 'button',\n                      class: 'btn cancel-btn',\n                      text: Craft.t('app', 'Cancel'),\n                    })\n                  )\n              )\n          )\n        )\n        .appendTo($gridItem);\n\n      if (settingsHtml) {\n        $container.addClass('flipped');\n        $container.children('.front').addClass('hidden');\n      } else {\n        $container.addClass('loading');\n        $container.children('.back').addClass('hidden');\n      }\n\n      const widget = new Craft.Widget(\n        $container,\n        settingsHtml\n          ? settingsHtml.replace(/__NAMESPACE__/g, settingsNamespace)\n          : null,\n        settingsJs\n          ? () => {\n              eval(settingsJs);\n            }\n          : $.noop\n      );\n\n      // Append the new widget after the last one\n      // (can't simply append it to the grid container, since that will place it after the resize listener object)\n\n      if (this.grid.$items.length) {\n        $gridItem.insertAfter(this.grid.$items.last());\n      } else {\n        $gridItem.prependTo(this.grid.$container);\n      }\n\n      this.grid.addItems($gridItem);\n      Garnish.scrollContainerToElement($gridItem);\n\n      $container.removeClass('scaleout');\n\n      if (typeof responseData !== 'undefined') {\n        $container.removeClass('loading');\n        widget.update(responseData);\n      } else if (!settingsHtml) {\n        const data = {\n          type: type,\n        };\n\n        Craft.queue.push(\n          () =>\n            new Promise((resolve) => {\n              Craft.sendActionRequest('POST', 'dashboard/create-widget', {data})\n                .then((response) => {\n                  $container.removeClass('loading');\n                  widget.update(response.data);\n                })\n                .catch(() => {\n                  widget.destroy();\n                })\n                .finally(resolve);\n            })\n        );\n      }\n    },\n\n    showWidgetManager: function () {\n      if (!this.widgetManager) {\n        var $widgets = this.$grid.find('> .item > .widget'),\n          $form = $(\n            '<form method=\"post\" accept-charset=\"UTF-8\">' +\n              '<input type=\"hidden\" name=\"action\" value=\"widgets/save-widget\"/>' +\n              '</form>'\n          ).appendTo(Garnish.$bod),\n          $noWidgets = $(\n            '<p id=\"nowidgets\" class=\"zilch small' +\n              ($widgets.length ? ' hidden' : '') +\n              '\">' +\n              Craft.t('app', 'You don’t have any widgets yet.') +\n              '</p>'\n          ).appendTo($form),\n          $table = $(\n            '<table class=\"data' +\n              (!$widgets.length ? ' hidden' : '') +\n              '\" role=\"presentation\"/>'\n          ).appendTo($form),\n          $tbody = $('<tbody/>').appendTo($table);\n\n        for (var i = 0; i < $widgets.length; i++) {\n          var $widget = $widgets.eq(i),\n            widget = $widget.data('widget');\n\n          // Make sure it's actually saved\n          if (!widget || !widget.id) {\n            continue;\n          }\n\n          widget.getManagerRow().appendTo($tbody);\n        }\n\n        this.widgetManager = new Garnish.HUD(this.$widgetManagerBtn, $form, {\n          hudClass: 'hud widgetmanagerhud',\n          onShow: () => {\n            this.$widgetManagerBtn\n              .addClass('active')\n              .attr('aria-expanded', 'true');\n          },\n          onHide: () => {\n            this.$widgetManagerBtn\n              .removeClass('active')\n              .attr('aria-expanded', 'false');\n          },\n        });\n\n        this.widgetAdminTable = new Craft.AdminTable({\n          tableSelector: $table,\n          noObjectsSelector: $noWidgets,\n          sortable: true,\n          reorderAction: 'dashboard/reorder-user-widgets',\n          deleteAction: 'dashboard/delete-user-widget',\n          confirmDeleteMessage: null,\n          deleteSuccessMessage: null,\n          noItemsSelector: '#nowidgets',\n          onReorderItems: (ids) => {\n            var lastWidget = null;\n\n            for (var i = 0; i < ids.length; i++) {\n              var widget = this.widgets[ids[i]];\n\n              if (!lastWidget) {\n                widget.$gridItem.prependTo(this.$grid);\n              } else {\n                widget.$gridItem.insertAfter(lastWidget.$gridItem);\n              }\n\n              lastWidget = widget;\n            }\n\n            this.grid.resetItemOrder();\n          },\n          onDeleteItem: (id) => {\n            const widget = this.widgets[id];\n            widget.destroy();\n\n            const $undoBtn = Craft.ui.createButton({\n              label: Craft.t('app', 'Undo'),\n              spinner: true,\n            });\n\n            const notification = Craft.cp.displaySuccess(\n              Craft.t('app', '“{name}” deleted.', {\n                name: widget.getLabel(),\n              }),\n              {\n                details: $undoBtn,\n              }\n            );\n\n            $undoBtn.on('click', () => {\n              if ($undoBtn.hasClass('loading')) {\n                return;\n              }\n\n              $undoBtn.addClass('loading');\n\n              const data = {\n                type: widget.type,\n                settings: widget.storedSettings,\n              };\n\n              Craft.sendActionRequest('POST', 'dashboard/create-widget', {data})\n                .then((response) => {\n                  this.createWidget(\n                    widget.type,\n                    widget.getLabel(),\n                    response.data\n                  );\n\n                  $undoBtn.off('click');\n                  notification.close();\n                })\n                .finally(() => {\n                  $undoBtn.removeClass('loading');\n                });\n            });\n          },\n        });\n      } else {\n        this.widgetManager.show();\n      }\n    },\n  });\n\n  /**\n   * Dashboard Widget class\n   */\n  Craft.Widget = Garnish.Base.extend({\n    $container: null,\n    $gridItem: null,\n\n    $front: null,\n    $settingsBtn: null,\n    $title: null,\n    $subtitle: null,\n    $heading: null,\n    $bodyContainer: null,\n\n    $back: null,\n    $settingsForm: null,\n    $settingsContainer: null,\n    $settingsToggle: null,\n    $saveBtn: null,\n    $settingsErrorList: null,\n\n    id: null,\n    type: null,\n    title: null,\n    subtitle: null,\n    storedSettings: null,\n\n    totalCols: null,\n    settingsHtml: null,\n    initSettingsFn: null,\n    showingSettings: false,\n\n    colspanPicker: null,\n\n    init: function (container, settingsHtml, initSettingsFn, storedSettings) {\n      this.$container = $(container);\n      this.storedSettings = storedSettings;\n\n      this.$settingsToggle = this.$container.find('[data-settings-toggle]');\n      this.$gridItem = this.$container.parent();\n\n      // Store a reference to this object on the container element\n      this.$container.data('widget', this);\n\n      // Do a little introspection\n      this.id = this.$container.data('id');\n      this.type = this.$container.data('type');\n      this.title = this.$container.data('title');\n\n      if (this.id) {\n        // Store a reference to this object on the main Dashboard object\n        window.dashboard.widgets[this.id] = this;\n      }\n\n      this.$front = this.$container.children('.front');\n      this.$settingsBtn = this.$front.find('> .pane > .icon.settings');\n      this.$heading = this.$front.find('> .pane > .widget-heading');\n      this.$title = this.$heading.find('> h2');\n      this.$subtitle = this.$heading.find('> h5');\n      this.$bodyContainer = this.$front.find('> .pane > .body');\n\n      this.setSettingsHtml(settingsHtml, initSettingsFn);\n\n      if (!this.$container.hasClass('flipped')) {\n        this.onShowFront();\n      } else {\n        this.initBackUi();\n        this.refreshSettings();\n        this.onShowBack();\n      }\n\n      this.addListener(this.$settingsBtn, 'click', 'showSettings');\n    },\n\n    initBackUi: function () {\n      this.$back = this.$container.children('.back');\n      this.$settingsForm = this.$back.children('form');\n      this.$settingsContainer = this.$settingsForm.children('.settings');\n      var $btnsContainer = this.$settingsForm.children('.buttons');\n      this.$saveBtn = $btnsContainer.children('button[type=submit]');\n\n      this.addListener(\n        $btnsContainer.children('.cancel-btn'),\n        'click',\n        'cancelSettings'\n      );\n      this.addListener(this.$settingsForm, 'submit', 'saveSettings');\n    },\n\n    getColspan: function () {\n      return this.$gridItem.data('colspan');\n    },\n\n    setColspan: function (colspan) {\n      this.$gridItem.data('colspan', colspan).attr('data-colspan', colspan);\n      window.dashboard.grid.refreshCols(true);\n    },\n\n    getTypeInfo: function (property, defaultValue) {\n      return window.dashboard.getTypeInfo(this.type, property, defaultValue);\n    },\n\n    setSettingsHtml: function (settingsHtml, initSettingsFn) {\n      this.settingsHtml = settingsHtml;\n      this.initSettingsFn = initSettingsFn;\n\n      if (this.settingsHtml) {\n        this.$settingsBtn.removeClass('hidden');\n      } else {\n        this.$settingsBtn.addClass('hidden');\n      }\n    },\n\n    refreshSettings: function () {\n      this.$settingsContainer.html(this.settingsHtml);\n\n      Garnish.requestAnimationFrame(() => {\n        Craft.initUiElements(this.$settingsContainer);\n        this.initSettingsFn();\n      });\n    },\n\n    showSettings: function () {\n      if (!this.$back) {\n        this.initBackUi();\n      }\n\n      // Refresh the settings every time\n      this.refreshSettings();\n\n      this.$back.removeClass('hidden');\n      setTimeout(() => {\n        this.$container.addClass('flipped').velocity(\n          {height: this.$back.height()},\n          {\n            complete: this.onShowBack.bind(this),\n          }\n        );\n      }, 100);\n    },\n\n    hideSettings: function () {\n      this.$front.removeClass('hidden');\n\n      setTimeout(() => {\n        this.$container.removeClass('flipped').velocity(\n          {height: this.$front.height()},\n          {\n            complete: this.onShowFront.bind(this),\n          }\n        );\n        this.$settingsToggle.focus();\n      }, 100);\n    },\n\n    saveSettings: function (e) {\n      e.preventDefault();\n\n      if (this.$saveBtn.hasClass('loading')) {\n        return;\n      }\n\n      this.$saveBtn.addClass('loading');\n\n      Craft.queue.push(\n        () =>\n          new Promise((resolve) => {\n            const action = this.$container.hasClass('new')\n                ? 'dashboard/create-widget'\n                : 'dashboard/save-widget-settings',\n              data = this.$settingsForm.serialize();\n\n            Craft.sendActionRequest('POST', action, {data})\n              .then((response) => {\n                if (this.$settingsErrorList) {\n                  this.$settingsErrorList.remove();\n                  this.$settingsErrorList = null;\n                }\n\n                Craft.cp.displaySuccess(Craft.t('app', 'Widget saved.'));\n\n                // Make sure the widget is still allowed to be shown, just in case\n                if (!response.data.info) {\n                  this.destroy();\n                } else {\n                  this.update(response.data);\n                  this.hideSettings();\n                }\n              })\n              .catch(({response}) => {\n                if (this.$settingsErrorList) {\n                  this.$settingsErrorList.remove();\n                  this.$settingsErrorList = null;\n                }\n\n                Craft.cp.displayError(Craft.t('app', 'Couldn’t save widget.'));\n\n                if (response.data.errors) {\n                  this.$settingsErrorList = Craft.ui\n                    .createErrorList(response.data.errors)\n                    .insertAfter(this.$settingsContainer);\n                }\n              })\n              .finally(() => {\n                this.$saveBtn.removeClass('loading');\n                resolve();\n              });\n          })\n      );\n    },\n\n    update: function (response) {\n      if (!this.$back) {\n        this.initBackUi();\n      }\n\n      this.title = response.info.title;\n      this.subtitle = response.info.subtitle;\n      this.storedSettings = response.info.settings;\n\n      // Is this a new widget?\n      if (this.$container.hasClass('new')) {\n        // Discover ourself\n        this.id = response.info.id;\n\n        this.$container\n          .attr('id', 'widget' + this.id)\n          .removeClass('new loading-new');\n\n        if (this.$settingsForm) {\n          this.$settingsForm.prepend(\n            '<input type=\"hidden\" name=\"widgetId\" value=\"' + this.id + '\"/>'\n          );\n        }\n\n        // Store a reference to this object on the main Dashboard object, now that the widget actually exists\n        window.dashboard.widgets[this.id] = this;\n\n        if (window.dashboard.widgetAdminTable) {\n          window.dashboard.widgetAdminTable.addRow(this.getManagerRow());\n        }\n      } else {\n        if (window.dashboard.widgetAdminTable) {\n          window.dashboard.widgetAdminTable.$tbody\n            .children('[data-id=\"' + this.id + '\"]:first')\n            .children('td:nth-child(2)')\n            .html(this.getManagerRowLabel());\n        }\n      }\n\n      if (!this.title && !this.subtitle) {\n        this.$heading.remove();\n      } else {\n        if (this.title) {\n          this.$title.text(this.title);\n        } else {\n          this.$title.remove();\n        }\n\n        if (this.subtitle) {\n          this.$subtitle.text(this.subtitle);\n        } else {\n          this.$subtitle.remove();\n        }\n      }\n\n      this.$bodyContainer.html(response.info.bodyHtml);\n\n      // New colspan?\n      if (response.info.colspan != this.getColspan()) {\n        this.setColspan(response.info.colspan);\n        Garnish.scrollContainerToElement(this.$gridItem);\n      }\n\n      Craft.initUiElements(this.$bodyContainer);\n      Craft.appendHeadHtml(response.headHtml);\n      Craft.appendBodyHtml(response.bodyHtml);\n\n      this.setSettingsHtml(response.info.settingsHtml, function () {\n        eval(response.info.settingsJs);\n      });\n    },\n\n    cancelSettings: function () {\n      if (this.id) {\n        this.hideSettings();\n      } else {\n        this.destroy();\n      }\n    },\n\n    onShowFront: function () {\n      this.showingSettings = false;\n      this.removeListener(this.$back, 'resize');\n      this.addListener(this.$front, 'resize', 'updateContainerHeight');\n      if (this.$back) {\n        this.$back.addClass('hidden');\n      }\n    },\n\n    onShowBack: function () {\n      this.showingSettings = true;\n      this.removeListener(this.$front, 'resize');\n      this.addListener(this.$back, 'resize', 'updateContainerHeight');\n      this.$front.addClass('hidden');\n\n      // Focus on the first input\n      setTimeout(() => {\n        this.$settingsForm.find(':focusable:first').focus();\n      }, 1);\n    },\n\n    updateContainerHeight: function () {\n      this.$container.height(\n        (this.showingSettings ? this.$back : this.$front).height()\n      );\n    },\n\n    getWidgetLabelId: function () {\n      return `widget-label-${this.id}`;\n    },\n\n    getManagerRow: function () {\n      var $row = $(\n        '<tr data-id=\"' +\n          this.id +\n          '\" data-name=\"' +\n          (this.title\n            ? Craft.escapeHtml(this.title)\n            : this.getTypeInfo('name')) +\n          '\">' +\n          '<td class=\"widgetmanagerhud-icon thin\">' +\n          this.getTypeInfo('iconSvg') +\n          '</td>' +\n          '<td id=\"' +\n          this.getWidgetLabelId() +\n          '\">' +\n          this.getManagerRowLabel() +\n          '</td>' +\n          '<td class=\"widgetmanagerhud-col-colspan-picker thin\"></td>' +\n          '<td class=\"widgetmanagerhud-col-move thin\"><a class=\"move icon\" title=\"' +\n          Craft.t('app', 'Reorder') +\n          '\" role=\"button\"></a></td>' +\n          '<td class=\"thin\"><a class=\"delete icon\" tabindex=\"0\" type=\"button\" title=\"' +\n          Craft.t('app', 'Delete') +\n          '\" role=\"button\" aria-label=\"' +\n          Craft.t('app', 'Delete') +\n          '\" aria-describedby=\"' +\n          this.getWidgetLabelId() +\n          '\"></a></td>' +\n          '</tr>'\n      );\n\n      // Initialize the colspan picker\n      this.colspanPicker = new Craft.SlidePicker(this.getColspan(), {\n        min: 1,\n        max: () => {\n          return window.dashboard.grid.totalCols;\n        },\n        step: 1,\n        label: Craft.t('app', 'Number of columns'),\n        describedBy: this.getWidgetLabelId(),\n        valueLabel: (colspan) => {\n          return Craft.t(\n            'app',\n            '{num, number} {num, plural, =1{column} other{columns}}',\n            {\n              num: colspan,\n            }\n          );\n        },\n        onChange: (colspan) => {\n          // Update the widget and grid\n          this.setColspan(colspan);\n          window.dashboard.grid.refreshCols(true);\n\n          // Save the change\n          let data = {\n            id: this.id,\n            colspan: colspan,\n          };\n\n          Craft.sendActionRequest('POST', 'dashboard/change-widget-colspan', {\n            data,\n          })\n            .then((response) => {\n              Craft.cp.displaySuccess(Craft.t('app', 'Widget saved.'));\n            })\n            .catch(({response}) => {\n              Craft.cp.displayError(Craft.t('app', 'Couldn’t save widget.'));\n            });\n        },\n      });\n\n      this.colspanPicker.$container.appendTo(\n        $row.find('> td.widgetmanagerhud-col-colspan-picker')\n      );\n      window.dashboard.grid.on('refreshCols', () => {\n        this.colspanPicker.refresh();\n      });\n\n      return $row;\n    },\n\n    getLabel: function () {\n      return this.title || this.getTypeInfo('name');\n    },\n\n    getManagerRowLabel: function () {\n      var typeName = this.getTypeInfo('name');\n\n      if (!this.title) {\n        return typeName;\n      }\n\n      return (\n        Craft.escapeHtml(this.title) +\n        (this.title !== typeName\n          ? ' <span class=\"light\">(' + typeName + ')</span>'\n          : '')\n      );\n    },\n\n    destroy: function () {\n      delete window.dashboard.widgets[this.id];\n      this.$container.addClass('scaleout');\n      this.base();\n\n      setTimeout(() => {\n        window.dashboard.grid.removeItems(this.$gridItem);\n        this.$gridItem.remove();\n      }, 200);\n    },\n  });\n})(jQuery);\n"], "names": ["listToStyles", "parentId", "list", "styles", "newStyles", "i", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "__esModule", "default", "module", "locals", "exports", "add", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "n", "getter", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "prop", "prototype", "hasOwnProperty", "call", "$", "Craft", "Dashboard", "Garnish", "Base", "extend", "$grid", "$widgetManagerBtn", "$newWidgetBtn", "widgetTypes", "grid", "widgets", "widgetManager", "widgetAdminTable", "widgetSettingsModal", "init", "_this", "this", "addListener", "$doc", "ready", "data", "event", "preventDefault", "handleNewWidgetOptionSelect", "keyCode", "SPACE_KEY", "RETURN_KEY", "getTypeInfo", "property", "defaultValue", "e", "hide", "$option", "target", "createWidget", "name", "responseData", "settingsNamespace", "concat", "Math", "floor", "random", "settingsHtml", "replace", "settingsJs", "$gridItem", "$container", "class", "addClass", "append", "value", "text", "t", "ui", "createSubmitButton", "label", "spinner", "appendTo", "children", "widget", "Widget", "eval", "$items", "insertAfter", "last", "prependTo", "addItems", "scrollContainerToElement", "removeClass", "queue", "Promise", "resolve", "sendActionRequest", "then", "response", "destroy", "showWidgetManager", "_this2", "show", "$widgets", "find", "$form", "$bod", "$noWidgets", "$table", "$tbody", "eq", "getManagerRow", "HUD", "hudClass", "onShow", "attr", "onHide", "AdminTable", "tableSelector", "noObjectsSelector", "sortable", "reorderAction", "deleteAction", "confirmDeleteMessage", "deleteSuccessMessage", "noItemsSelector", "onReorderItems", "ids", "lastWidget", "resetItemOrder", "onDeleteItem", "$undoBtn", "createButton", "notification", "cp", "displaySuccess", "get<PERSON><PERSON><PERSON>", "details", "on", "hasClass", "settings", "storedSettings", "off", "close", "$front", "$settingsBtn", "$title", "$subtitle", "$heading", "$bodyContainer", "$back", "$settingsForm", "$settingsContainer", "$settingsToggle", "$saveBtn", "$settingsErrorList", "title", "subtitle", "totalCols", "initSettingsFn", "showingSettings", "colspanPicker", "container", "parent", "window", "dashboard", "setSettingsHtml", "initBackUi", "refreshSettings", "onShowBack", "onShowFront", "$btnsContainer", "getColspan", "setColspan", "colspan", "refreshCols", "_this3", "html", "requestAnimationFrame", "initUiElements", "showSettings", "_this4", "setTimeout", "velocity", "height", "complete", "hideSettings", "_this5", "focus", "saveSettings", "_this6", "action", "serialize", "info", "_ref", "displayError", "errors", "createErrorList", "prepend", "addRow", "getManagerRowLabel", "bodyHtml", "appendHeadHtml", "headHtml", "appendBodyHtml", "cancelSettings", "removeListener", "_this7", "updateContainerHeight", "getWidgetLabelId", "_this8", "$row", "escapeHtml", "SlidePicker", "min", "max", "step", "describedBy", "valueLabel", "num", "onChange", "_ref2", "refresh", "typeName", "_this9", "base", "removeItems", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}