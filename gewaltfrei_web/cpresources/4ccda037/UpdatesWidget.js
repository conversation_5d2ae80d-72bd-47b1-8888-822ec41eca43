!function(){var t;t=jQ<PERSON>y,Craft.UpdatesWidget=Garnish.Base.extend({$widget:null,$body:null,$btn:null,checking:!1,init:function(i,e){this.$widget=t("#widget"+i),this.$body=this.$widget.find(".body:first"),this.initBtn(),e||this.checkForUpdates(!1)},initBtn:function(){this.$btn=this.$body.find(".btn:first"),this.addListener(this.$btn,"click",(function(){this.checkForUpdates(!0)}))},lookLikeWereChecking:function(){this.checking=!0,this.$widget.addClass("loading"),this.$btn.addClass("disabled")},dontLookLikeWereChecking:function(){this.checking=!1,this.$widget.removeClass("loading")},checkForUpdates:function(i){var e=this;this.checking||(this.lookLikeWereChecking(),Craft.cp.checkForUpdates(i,!1,this.showUpdateInfo.bind(this),(function(){e.dontLookLikeWereChecking(),e.$body.empty().append(t("<p/>",{class:"centeralign error",text:Craft.t("app","Unable to fetch updates at this time.")}))})))},showUpdateInfo:function(t){var i;this.dontLookLikeWereChecking(),t.total?(i=1==t.total?Craft.t("app","One update available!"):Craft.t("app","{total} updates available!",{total:t.total}),this.$body.html('<p class="centeralign">'+i+' <a class="go nowrap" href="'+Craft.getUrl("utilities/updates")+'">'+Craft.t("app","Go to Updates")+"</a></p>")):(this.$body.html('<p class="centeralign">'+Craft.t("app","Congrats! You’re up to date.")+'</p><p class="centeralign"><button type="button" class="btn" data-icon="refresh" aria-label="'+Craft.t("app","Check again")+'">'+Craft.t("app","Check again")+"</button></p>"),this.initBtn()),Craft.cp.updateUtilitiesBadge()}})}();
//# sourceMappingURL=UpdatesWidget.js.map