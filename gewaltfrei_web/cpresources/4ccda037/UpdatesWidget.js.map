{"version": 3, "file": "UpdatesWidget.js", "mappings": "YAAA,IAAWA,IAsGRC,OAnGDC,MAAMC,cAAgBC,QAAQC,KAAKC,OAAO,CACxCC,QAAS,KACTC,MAAO,KACPC,KAAM,KACNC,UAAU,EAEVC,KAAM,SAAUC,EAAUC,GACxBC,KAAKP,QAAUP,EAAE,UAAYY,GAC7BE,KAAKN,MAAQM,KAAKP,QAAQQ,KAAK,eAC/BD,KAAKE,UAEAH,GACHC,KAAKG,iBAAgB,EAEzB,EAEAD,QAAS,WACPF,KAAKL,KAAOK,KAAKN,MAAMO,KAAK,cAC5BD,KAAKI,YAAYJ,KAAKL,KAAM,SAAS,WACnCK,KAAKG,iBAAgB,EACvB,GACF,EAEAE,qBAAsB,WACpBL,KAAKJ,UAAW,EAChBI,KAAKP,QAAQa,SAAS,WACtBN,KAAKL,KAAKW,SAAS,WACrB,EAEAC,yBAA0B,WACxBP,KAAKJ,UAAW,EAChBI,KAAKP,QAAQe,YAAY,UAC3B,EAEAL,gBAAiB,SAAUM,GAAc,IAAAC,EAAA,KACnCV,KAAKJ,WAITI,KAAKK,uBACLjB,MAAMuB,GAAGR,gBACPM,GACA,EACAT,KAAKY,eAAeC,KAAKb,OACzB,WACEU,EAAKH,2BACLG,EAAKhB,MAAMoB,QAAQC,OACjB7B,EAAE,OAAQ,CACR8B,MAAO,oBACPC,KAAM7B,MAAM8B,EAAE,MAAO,2CAG3B,IAEJ,EAEAN,eAAgB,SAAUO,GAItB,IAAIC,EAHNpB,KAAKO,2BAEDY,EAAKE,OAILD,EADgB,GAAdD,EAAKE,MACMjC,MAAM8B,EAAE,MAAO,yBAEf9B,MAAM8B,EAAE,MAAO,6BAA8B,CACxDG,MAAOF,EAAKE,QAIhBrB,KAAKN,MAAM4B,KACT,0BACEF,EACA,+BACAhC,MAAMmC,OAAO,qBACb,KACAnC,MAAM8B,EAAE,MAAO,iBALjB,cAUFlB,KAAKN,MAAM4B,KACT,0BACElC,MAAM8B,EAAE,MAAO,gCADjB,gGAIE9B,MAAM8B,EAAE,MAAO,eACf,KACA9B,MAAM8B,EAAE,MAAO,eACf,iBAGJlB,KAAKE,WAIPd,MAAMuB,GAAGa,sBACX,G", "sources": ["webpack:///./UpdatesWidget.js"], "sourcesContent": ["(function ($) {\n  /** global: Craft */\n  /** global: Garnish */\n  Craft.UpdatesWidget = Garnish.Base.extend({\n    $widget: null,\n    $body: null,\n    $btn: null,\n    checking: false,\n\n    init: function (widgetId, cached) {\n      this.$widget = $('#widget' + widgetId);\n      this.$body = this.$widget.find('.body:first');\n      this.initBtn();\n\n      if (!cached) {\n        this.checkForUpdates(false);\n      }\n    },\n\n    initBtn: function () {\n      this.$btn = this.$body.find('.btn:first');\n      this.addListener(this.$btn, 'click', function () {\n        this.checkForUpdates(true);\n      });\n    },\n\n    lookLikeWereChecking: function () {\n      this.checking = true;\n      this.$widget.addClass('loading');\n      this.$btn.addClass('disabled');\n    },\n\n    dontLookLikeWereChecking: function () {\n      this.checking = false;\n      this.$widget.removeClass('loading');\n    },\n\n    checkForUpdates: function (forceRefresh) {\n      if (this.checking) {\n        return;\n      }\n\n      this.lookLikeWereChecking();\n      Craft.cp.checkForUpdates(\n        forceRefresh,\n        false,\n        this.showUpdateInfo.bind(this),\n        () => {\n          this.dontLookLikeWereChecking();\n          this.$body.empty().append(\n            $('<p/>', {\n              class: 'centeralign error',\n              text: Craft.t('app', 'Unable to fetch updates at this time.'),\n            })\n          );\n        }\n      );\n    },\n\n    showUpdateInfo: function (info) {\n      this.dontLookLikeWereChecking();\n\n      if (info.total) {\n        var updateText;\n\n        if (info.total == 1) {\n          updateText = Craft.t('app', 'One update available!');\n        } else {\n          updateText = Craft.t('app', '{total} updates available!', {\n            total: info.total,\n          });\n        }\n\n        this.$body.html(\n          '<p class=\"centeralign\">' +\n            updateText +\n            ' <a class=\"go nowrap\" href=\"' +\n            Craft.getUrl('utilities/updates') +\n            '\">' +\n            Craft.t('app', 'Go to Updates') +\n            '</a>' +\n            '</p>'\n        );\n      } else {\n        this.$body.html(\n          '<p class=\"centeralign\">' +\n            Craft.t('app', 'Congrats! You’re up to date.') +\n            '</p>' +\n            '<p class=\"centeralign\"><button type=\"button\" class=\"btn\" data-icon=\"refresh\" aria-label=\"' +\n            Craft.t('app', 'Check again') +\n            '\">' +\n            Craft.t('app', 'Check again') +\n            '</button></p>'\n        );\n\n        this.initBtn();\n      }\n\n      // Update the control panel header badge\n      Craft.cp.updateUtilitiesBadge();\n    },\n  });\n})(jQuery);\n"], "names": ["$", "j<PERSON><PERSON><PERSON>", "Craft", "UpdatesWidget", "Garnish", "Base", "extend", "$widget", "$body", "$btn", "checking", "init", "widgetId", "cached", "this", "find", "initBtn", "checkForUpdates", "addListener", "lookLikeWereChecking", "addClass", "dontLookLikeWereChecking", "removeClass", "forceRefresh", "_this", "cp", "showUpdateInfo", "bind", "empty", "append", "class", "text", "t", "info", "updateText", "total", "html", "getUrl", "updateUtilitiesBadge"], "sourceRoot": ""}